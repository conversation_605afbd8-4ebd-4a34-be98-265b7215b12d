<?php

namespace App\Models;

use CodeIgniter\Model;

class OperatorModel extends Model
{
    protected $table = 'operator';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'username', 'nama', 'no_wa', 'password', 'role', 'status', 'last_login'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    protected $validationRules = [
        'username' => 'required|min_length[3]|max_length[50]|is_unique[operator.username,id,{id}]',
        'nama' => 'required|min_length[3]|max_length[100]',
        'no_wa' => 'required|min_length[10]|max_length[15]',
        'password' => 'required|min_length[6]',
        'role' => 'required|in_list[admin,operator]',
        'status' => 'required|in_list[aktif,nonaktif]'
    ];

    protected $validationMessages = [
        'username' => [
            'required' => 'Username harus diisi',
            'min_length' => 'Username minimal 3 karakter',
            'max_length' => 'Username maksimal 50 karakter',
            'is_unique' => 'Username sudah digunakan'
        ],
        'nama' => [
            'required' => 'Nama harus diisi',
            'min_length' => 'Nama minimal 3 karakter',
            'max_length' => 'Nama maksimal 100 karakter'
        ],
        'no_wa' => [
            'required' => 'No. WhatsApp harus diisi',
            'min_length' => 'No. WhatsApp minimal 10 digit',
            'max_length' => 'No. WhatsApp maksimal 15 digit'
        ],
        'password' => [
            'required' => 'Password harus diisi',
            'min_length' => 'Password minimal 6 karakter'
        ],
        'role' => [
            'required' => 'Role harus dipilih',
            'in_list' => 'Role harus admin atau operator'
        ],
        'status' => [
            'required' => 'Status harus dipilih',
            'in_list' => 'Status harus aktif atau nonaktif'
        ]
    ];

    /**
     * Get operator by username
     */
    public function getByUsername($username)
    {
        return $this->where('username', $username)->first();
    }

    /**
     * Verify login credentials
     */
    public function verifyLogin($username, $password)
    {
        $operator = $this->getByUsername($username);
        
        if (!$operator) {
            return false;
        }

        if ($operator['status'] !== 'aktif') {
            return false;
        }

        if (password_verify($password, $operator['password'])) {
            // Update last login
            $this->update($operator['id'], ['last_login' => date('Y-m-d H:i:s')]);
            return $operator;
        }

        return false;
    }

    /**
     * Create operator with hashed password
     */
    public function createOperator($data)
    {
        // Set validation rules for create
        $createRules = [
            'username' => 'required|min_length[3]|max_length[50]|is_unique[operator.username]',
            'nama' => 'required|min_length[3]|max_length[100]',
            'no_wa' => 'required|min_length[10]|max_length[15]',
            'password' => 'required|min_length[6]',
            'role' => 'required|in_list[admin,operator]',
            'status' => 'required|in_list[aktif,nonaktif]'
        ];

        // Set validation rules temporarily
        $this->setValidationRules($createRules);

        // Validate data
        if (!$this->validate($data)) {
            return false;
        }

        // Hash password
        if (isset($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }

        return $this->insert($data);
    }

    /**
     * Update operator with optional password hash
     */
    public function updateOperator($id, $data)
    {
        // Set custom validation rules for update
        $updateRules = [
            'username' => "required|min_length[3]|max_length[50]|is_unique[operator.username,id,{$id}]",
            'nama' => 'required|min_length[3]|max_length[100]',
            'no_wa' => 'required|min_length[10]|max_length[15]',
            'role' => 'required|in_list[admin,operator]',
            'status' => 'required|in_list[aktif,nonaktif]'
        ];

        // Add password validation only if password is provided
        if (isset($data['password']) && !empty($data['password'])) {
            $updateRules['password'] = 'required|min_length[6]';
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        } else {
            unset($data['password']);
        }

        // Set validation rules temporarily
        $this->setValidationRules($updateRules);

        // Validate data
        if (!$this->validate($data)) {
            return false;
        }

        // Update data
        return $this->update($id, $data);
    }

    /**
     * Toggle operator status
     */
    public function toggleStatus($id)
    {
        $operator = $this->find($id);
        if (!$operator) {
            return false;
        }

        $newStatus = $operator['status'] === 'aktif' ? 'nonaktif' : 'aktif';
        return $this->update($id, ['status' => $newStatus]);
    }

    /**
     * Get active operators
     */
    public function getActiveOperators()
    {
        return $this->where('status', 'aktif')->findAll();
    }

    /**
     * Get operators with statistics
     */
    public function getOperatorsWithStats()
    {
        $operators = $this->orderBy('created_at', 'DESC')->findAll();
        
        foreach ($operators as &$operator) {
            // Remove password from output
            unset($operator['password']);
            
            // Add formatted dates
            $operator['created_at_formatted'] = date('d/m/Y H:i', strtotime($operator['created_at']));
            $operator['last_login_formatted'] = $operator['last_login'] ? 
                date('d/m/Y H:i', strtotime($operator['last_login'])) : 'Belum pernah login';
        }
        
        return $operators;
    }
}
