# Troubleshooting Guide - PlaySphere IoT Wemos

## 🎨 Ma<PERSON>ah Warna Display

### Problem: <PERSON><PERSON> terbalik (hitam jadi putih, hijau jadi ungu)

**Penyebab:** Konfigurasi RGB565 atau TFT_eSPI yang salah

**Solusi:**

1. **Test warna terlebih dahulu:**
   ```
   - Buka folder: iot/test_colors/
   - Upload test_colors.ino ke Wemos
   - Lihat hasil di layar dan serial monitor
   ```

2. **<PERSON><PERSON> warna masih salah, edit User_Setup.h:**
   ```cpp
   // Coba ganti color order
   #define TFT_RGB_ORDER TFT_BGR  // Ganti dari TFT_RGB
   ```

3. **Atau coba invert display:**
   ```cpp
   // Di setup()
   tft.invertDisplay(true);  // Ganti dari false
   ```

4. **Verifikasi pin connections:**
   ```
   SCL: D5 (GPIO 14) 
   SDA: D7 (GPIO 13) 
   RES: D4 (GPIO 2) 
   DC: D3 (GPIO 0) 
   BLK: D8 (GPIO 15)
   ```

## ⏱️ Masalah Timer

### Problem: Timer tidak berjalan (stuck)

**Penyebab:** Timer client-side tidak terupdate

**Solusi:**

1. **Cek serial monitor (115200 baud):**
   ```
   Timer parsed: 0h 1m 0s
   Timer countdown: 00:00:59
   Timer countdown: 00:00:58
   ```

2. **Jika tidak ada countdown, cek API response:**
   ```bash
   php test_timer_realtime.php
   ```

3. **Verifikasi data dari server:**
   ```json
   {
     "timer": "00:01:00",
     "isPersonal": false,
     "status": "in_use"
   }
   ```

### Problem: Member paket 1 menit tampil 01:00:00 bukan 00:00:59

**Penyebab:** Perhitungan countdown di server salah

**Solusi:**

1. **Cek database sesi:**
   ```sql
   SELECT id, durasi_sisa, waktu_mulai 
   FROM sesi 
   WHERE status = 'berjalan';
   ```

2. **Update manual untuk test:**
   ```sql
   UPDATE sesi 
   SET durasi_sisa = 1 
   WHERE id = [sesi_id];
   ```

3. **Monitor API response:**
   ```bash
   curl -X GET 'http://*************/playsphere/api/channel/status/10'
   ```

### Problem: Timer member non-paket tidak countdown

**Penyebab:** Saldo atau harga_member tidak benar

**Solusi:**

1. **Cek saldo member:**
   ```sql
   SELECT id, nama, saldo 
   FROM member 
   WHERE id = [member_id];
   ```

2. **Cek harga konsol:**
   ```sql
   SELECT id, nama_konsol, harga_member 
   FROM konsol 
   WHERE id = [konsol_id];
   ```

3. **Hitung manual:**
   ```
   Max waktu = (saldo / harga_per_jam) * 60 menit
   Contoh: (25000 / 5000) * 60 = 300 menit = 5 jam
   ```

## 🔗 Masalah Koneksi

### Problem: "IP tidak terdaftar"

**Solusi:**

1. **Update IP di database:**
   ```sql
   UPDATE station 
   SET ip_address = '************' 
   WHERE nama_station = 'Station 2';
   ```

2. **Verifikasi IP Wemos di serial monitor:**
   ```
   WiFi Terhubung
   IP Address: ************
   ```

3. **Test API manual:**
   ```bash
   curl -H "X-Forwarded-For: ************" \
        http://*************/playsphere/api/channel/find
   ```

### Problem: WiFi tidak connect

**Solusi:**

1. **Cek credentials:**
   ```cpp
   const char* ssid = "PlaySphere";
   const char* password = "1234554321";
   ```

2. **Cek signal strength:**
   ```cpp
   Serial.println("RSSI: " + String(WiFi.RSSI()));
   ```

3. **Reset WiFi settings:**
   ```cpp
   WiFi.disconnect();
   WiFi.begin(ssid, password);
   ```

## 🎬 Masalah Animasi

### Problem: Tidak ada animasi waktu habis

**Penyebab:** Timer tidak mencapai 00:00:00 atau logic tidak terpanggil

**Solusi:**

1. **Cek log serial:**
   ```
   Timer reached 00:00:00 - Time's up!
   Time's up! Showing time up screen
   ```

2. **Test manual dengan timer pendek:**
   ```sql
   UPDATE sesi 
   SET durasi_sisa = 0.1 
   WHERE status = 'berjalan';
   ```

3. **Verifikasi showTimeUpScreen() dipanggil:**
   ```cpp
   Serial.println("showTimeUpScreen() called");
   ```

## 🔧 Compilation Errors

### Problem: "redefinition of TFT_eSPI tft"

**Solusi:**
- Tutup semua file .ino di Arduino IDE
- Buka hanya satu file (wemos.ino ATAU test_colors.ino)
- Restart Arduino IDE

### Problem: TFT_eSPI library errors

**Solusi:**

1. **Reinstall library:**
   ```
   Tools > Manage Libraries > Search "TFT_eSPI" > Install
   ```

2. **Verifikasi User_Setup.h:**
   ```cpp
   #define ST7789_DRIVER
   #define TFT_WIDTH  240
   #define TFT_HEIGHT 240
   ```

3. **Clear Arduino cache:**
   ```
   File > Preferences > Clear cache
   ```

## 📊 Monitoring Tools

### Real-time Timer Monitor
```bash
php test_timer_realtime.php
```

### API Test
```bash
php test_api.php
```

### Serial Monitor Commands
```
115200 baud rate
Look for:
- Timer countdown: HH:MM:SS
- Timer running: true/false
- Status updates
```

### Database Queries
```sql
-- Check active sessions
SELECT s.*, st.nama_station, m.nama as member_name
FROM sesi s
JOIN station st ON st.id = s.station_id
LEFT JOIN member m ON m.id = s.id_member
WHERE s.status = 'berjalan';

-- Check station IPs
SELECT id, nama_station, ip_address 
FROM station 
ORDER BY nama_station;
```

## 🚨 Emergency Reset

Jika semua tidak berfungsi:

1. **Reset Wemos:**
   ```cpp
   ESP.restart();
   ```

2. **Clear EEPROM:**
   ```cpp
   #include <EEPROM.h>
   EEPROM.begin(512);
   for (int i = 0; i < 512; i++) {
     EEPROM.write(i, 0);
   }
   EEPROM.commit();
   ```

3. **Factory reset display:**
   ```cpp
   tft.fillScreen(BLACK);
   tft.setRotation(0);
   tft.setRotation(2);
   ```

4. **Restart services:**
   ```bash
   # Restart web server
   # Restart database
   # Check network connectivity
   ```
