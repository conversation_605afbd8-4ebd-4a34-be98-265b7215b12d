<?= $this->extend('layout/template') ?>

<?= $this->section('content') ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <p class="text-muted mb-0"><PERSON><PERSON><PERSON> paket PlayStation</p>
    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addModal">
        <i class="fas fa-plus me-2"></i>Tambah Paket
    </button>
</div>

<div class="row">
    <?php foreach ($packages as $package): ?>
    <div class="col-md-3 mb-4">
        <div class="card border-0 h-100 rounded-4 shadow-sm">
            <div class="card-body p-3 text-center">
            <div class="position-absolute top-0 end-0 p-2">
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" 
                           onchange="toggleStatus(<?= $package['id'] ?>, this.checked)"
                           <?= $package['is_active'] ? 'checked' : '' ?>>
                </div>
            </div>
                <div class="bg-primary bg-opacity-10 rounded-circle p-3 mx-auto mb-3" style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-clock fa-lg text-primary"></i>
                </div>
                <h5 class="card-title mb-2"><?= $package['name'] ?></h5>
                <div class="text-success fw-bold mb-2">
                    Rp <?= number_format($package['price'], 0, ',', '.') ?>
                </div>
                <div class="text-muted small mb-3">
    <?php 
    $hours = floor($package['duration'] / 60);
    $minutes = $package['duration'] % 60;
    if ($hours > 0) {
        echo $hours . ' Jam';
        if ($minutes > 0) echo ' ' . $minutes . ' Menit';
    } else {
        echo $minutes . ' Menit';
    }
    ?>
</div>
                <p class="text-muted small mb-3"><?= $package['description'] ?></p>
                <div class="d-flex justify-content-center gap-2">
                    <button class="btn btn-warning btn-sm rounded-pill px-3" data-bs-toggle="modal" data-bs-target="#editModal<?= $package['id'] ?>">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-danger btn-sm rounded-pill px-3" onclick="deletePackage(<?= $package['id'] ?>)">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Modal Edit -->
        <div class="modal fade" id="editModal<?= $package['id'] ?>">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Edit Paket</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <form action="/package/edit/<?= $package['id'] ?>" method="post">
                        <div class="modal-body">
                            <div class="mb-3">
                                <label class="form-label">Nama Paket</label>
                                <input type="text" name="name" class="form-control" value="<?= $package['name'] ?>" required>
                            </div>
                            <div class="mb-3">
    <label class="form-label">Durasi</label>
    <div class="row">
        <div class="col">
            <label class="form-label small">Jam</label>
            <input type="number" name="hours" class="form-control" min="0" value="<?= floor($package['duration'] / 60) ?>" required>
        </div>
        <div class="col">
            <label class="form-label small">Menit</label>
            <input type="number" name="minutes" class="form-control" min="0" max="59" value="<?= $package['duration'] % 60 ?>" required>
        </div>
    </div>
</div>
                            <div class="mb-3">
                                <label class="form-label">Harga</label>
                                <input type="number" name="price" class="form-control" value="<?= $package['price'] ?>" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Deskripsi</label>
                                <textarea name="description" class="form-control" rows="3"><?= $package['description'] ?></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                            <button type="submit" class="btn btn-primary">Simpan</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <?php endforeach; ?>
</div>

<!-- Modal Tambah -->
<div class="modal fade" id="addModal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">Tambah Paket</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form action="/package/add" method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Nama Paket</label>
                        <input type="text" name="name" class="form-control" required>
                    </div>
                    <div class="mb-3">
    <label class="form-label">Durasi</label>
    <div class="row">
        <div class="col">
            <label class="form-label small">Jam</label>
            <input type="number" name="hours" class="form-control" min="0" value="<?= floor($package['duration'] / 60) ?>" required>
        </div>
        <div class="col">
            <label class="form-label small">Menit</label>
            <input type="number" name="minutes" class="form-control" min="0" max="59" value="<?= $package['duration'] % 60 ?>" required>
        </div>
    </div>
</div>
                    <div class="mb-3">
                        <label class="form-label">Harga</label>
                        <input type="number" name="price" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Deskripsi</label>
                        <textarea name="description" class="form-control" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    function deletePackage(id) {
        if(confirm('Yakin hapus paket ini?')) {
            window.location.href = `/package/delete/${id}`;
        }
    }
</script>
<script>
    function toggleStatus(id, status) {
        fetch(`/package/toggle/${id}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ status: status })
        })
        .then(response => response.json())
        .then(data => {
            if (!data.success) {
                alert('Gagal mengubah status');
                location.reload();
            }
        });
    }
</script>
<?= $this->endSection() ?>