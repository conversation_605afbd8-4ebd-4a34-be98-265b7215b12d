<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */

 $routes->setAutoRoute(false);

// Dashboard
$routes->get('/', 'Dashboard::index');
$routes->get('dashboard', 'Dashboard::index');

$routes->post('dashboard/mulai', 'Dashboard::mulai'); // hanya POST, karena dipanggil via AJAX
$routes->post('dashboard/stop', 'Dashboard::stop');
$routes->post('dashboard/updateHarga', 'Dashboard::updateHarga');
$routes->get('dashboard/getSessionStatus/(:num)', 'Dashboard::getSessionStatus/$1');
$routes->get('dashboard/getSessionStatus', 'Dashboard::getSessionStatus');

// Konsol
$routes->get('/konsol', 'Konsol::index');
$routes->post('/konsol/simpan-konsol', 'Konsol::simpanKonsol');
$routes->post('/konsol/simpan-station', 'Konsol::simpanStation');
$routes->get('/konsol/edit-konsol/(:num)', 'Konsol::editKonsol/$1');
$routes->post('/konsol/update-konsol/(:num)', 'Konsol::updateKonsol/$1');
$routes->get('/konsol/edit-station/(:num)', 'Konsol::editStation/$1');
$routes->post('/konsol/update-station/(:num)', 'Konsol::updateStation/$1');
$routes->get('/konsol/hapus-konsol/(:num)', 'Konsol::hapusKonsol/$1');
$routes->get('/konsol/hapus-station/(:num)', 'Konsol::hapusStation/$1');

// Paket
$routes->get('paket', 'Paket::index');
$routes->post('paket/simpan', 'Paket::simpan');
$routes->post('paket/update/(:num)', 'Paket::update/$1');
$routes->get('paket/hapus/(:num)', 'Paket::hapus/$1');

// Member
$routes->get('member', 'Member::index');
$routes->post('member/simpan', 'Member::simpan');
$routes->post('member/update/(:num)', 'Member::update/$1');
$routes->post('member/topup/(:num)', 'Member::topup/$1');
$routes->get('member/hapus/(:num)', 'Member::hapus/$1');
$routes->get('member/cetakRiwayat/(:num)', 'Member::cetakRiwayat/$1');
$routes->get('member/riwayat/(:num)', 'Member::riwayat/$1');

// API
$routes->get('/api/member-list', 'ApiController::memberList');
$routes->get('/api/paket-list', 'ApiController::paketList');
