# 🎮 PlaySphere

**PlaySphere** adalah aplikasi billing untuk rental PlayStation berbasis **CodeIgniter 4**, dirancang khusus untuk kebutuhan warung rental gaming modern. Dilengkapi dengan kontrol sesi, timer real-time, sistem member, dan siap terhubung dengan perangkat IoT seperti **Wemos D1 Mini** untuk kontrol perangkat fisik.

## 📋 Daftar Isi
- [Fitur Unggulan](#-fitur-unggulan)
- [Teknologi](#️-teknologi)
- [Struktur Proyek](#-struktur-proyek)
- [Cara Menjalankan](#-cara-menjalankan)
- [Pengembangan Mendatang](#-pengembangan-mendatang)
- [Catatan Rilis](#-catatan-rilis)
- [Tim <PERSON>](#-tim-pengembang)
- [Lisensi](#-lisensi)

## ✨ Fitur Unggulan

- ⏱️ **Timer per Station** dengan kontrol `Play`, `Stop`, `Pindah`
- 👥 **Sistem Member & Non Member** dengan manajemen data lengkap
- 💰 **Saldo & Paket Member** otomatis dikonversi menjadi durasi bermain
- 📦 **Manajemen Konsol & Station** untuk pengaturan harga dan paket
- 📊 **Dashboard Interaktif** dan responsif (optimal pada resolusi 1920x1080)
- 🌐 **Integrasi IoT** siap terhubung ke perangkat fisik (relay/Wemos D1 Mini)
- 🧠 **AJAX-based Form** untuk pengalaman pengguna yang cepat dan modern
- 🔄 **Real-time Timer** dengan sistem countdown yang akurat
- 💳 **Sistem Topup Saldo** untuk member dengan riwayat transaksi
- 🎯 **Manajemen Paket** dengan durasi dan harga yang dapat disesuaikan

## 🛠️ Teknologi

- **PHP 8.x** - Bahasa pemrograman server-side
- **CodeIgniter 4** - Framework PHP modern dan ringan
- **MySQL** - Sistem manajemen database
- **Bootstrap 5** - Framework CSS untuk tampilan responsif
- **JavaScript (AJAX)** - Untuk interaksi dinamis tanpa refresh halaman
- **Wemos D1 Mini** - Microcontroller untuk integrasi IoT (pengembangan masa depan)

## 📂 Struktur Proyek

- `app/Controllers` - Logika aplikasi dan penanganan request
  - `Dashboard.php` - Kontrol utama untuk monitoring dan manajemen station
  - `Kasir.php` - Pengelolaan transaksi dan pembayaran
  - `Konsol.php` - Manajemen konsol gaming
  - `Member.php` - Pengelolaan data member dan saldo
  - `Paket.php` - Pengaturan paket rental dengan durasi dan harga
  - `Setting.php` - Konfigurasi sistem

- `app/Models` - Model data untuk interaksi dengan database
  - Model untuk Konsol, Station, Member, Sesi, Paket, dan Topup

- `app/Views` - Tampilan antarmuka pengguna
  - `dashboard` - Tampilan utama monitoring station
  - `kasir` - Antarmuka kasir untuk transaksi
  - `konsol` - Manajemen data konsol
  - `member` - Pengelolaan data member
  - `paket` - Pengaturan paket rental
  - `setting` - Konfigurasi sistem
  - `layouts` - Template layout utama

- `assets` - File statis seperti CSS, JavaScript, dan gambar

## 🚀 Cara Menjalankan

1. **Clone repository** ini:
   ```bash
   git clone https://github.com/inidaus/playsphere.git
   ```

2. **Jalankan server lokal** (XAMPP / Laragon) dan arahkan ke folder playsphere.

3. **Buat database** dan import file playsphere.sql (jika tersedia).

4. **Atur konfigurasi database** di `app/Config/Database.php`.

5. **Buka di browser**: http://localhost/playsphere/

## 🔮 Pengembangan Mendatang

- 🎨 **Mode Malam** - Tampilan dark mode untuk kenyamanan mata
- 🧾 **Export Laporan** - Ekspor data sesi ke format Excel/PDF
- 📱 **API Mobile** - Pengembangan API untuk aplikasi mobile
- 🌐 **Sinkronisasi Cloud** - Backup data ke database cloud
- 🔒 **Sistem Login** - Manajemen admin dan operator dengan hak akses
- 🔁 **Reset Timer Otomatis** - Pengaturan reset timer per jam
- 📟 **Integrasi OLED** - Tampilan status pada layar OLED via Wemos

## 📝 Catatan Rilis

### v3.0.0 - *Revival of the Sphere* (2025-06-25)

- Reinkarnasi dari PlaySphere versi sebelumnya (v1 WordPress, v2 CI4)
- Branding ulang dengan desain fresh dan UI yang lebih ringan
- Dashboard client (station) responsif dengan timer real-time
- Sistem member lengkap dengan validasi saldo dan konversi otomatis
- Integrasi kontrol relay Wemos D1 Mini
- Optimasi UX untuk resolusi 1920x1080

*Untuk riwayat perubahan lengkap, lihat file [CHANGELOG.md](CHANGELOG.md)*

## 👨‍💻 Tim Pengembang

**Mas Daus**  
WarungStack Developer | Code Barista | Penggerak Langit Inovasi

> "Satu tangan ngoding, satu tangan ngasih kembalian. Tapi hati selalu di timer PlaySphere."

## 📄 Lisensi

Private Project – Tidak untuk distribusi publik.

---

<p align="center">PlaySphere © 2025 - Tempat semua sesi dimulai, dihitung, dan diabadikan 🎮</p>
