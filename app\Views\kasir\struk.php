<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Struk Pembayaran - PlaySphere</title>
    <style>
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
        
        body {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            max-width: 300px;
            margin: 0 auto;
            padding: 10px;
        }
        
        .header {
            text-align: center;
            border-bottom: 1px dashed #000;
            padding-bottom: 10px;
            margin-bottom: 10px;
        }
        
        .logo {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .info {
            font-size: 10px;
        }
        
        .content {
            margin-bottom: 10px;
        }
        
        .row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2px;
        }
        
        .separator {
            border-top: 1px dashed #000;
            margin: 10px 0;
        }
        
        .total {
            font-weight: bold;
            font-size: 14px;
        }
        
        .footer {
            text-align: center;
            border-top: 1px dashed #000;
            padding-top: 10px;
            margin-top: 10px;
            font-size: 10px;
        }
        
        .print-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
    </style>
</head>
<body>
    <div class="no-print">
        <button class="print-btn" onclick="window.print()">🖨️ Cetak Struk</button>
        <button class="print-btn" onclick="window.close()" style="background: #6c757d;">❌ Tutup</button>
    </div>

    <?php
    helper('rental');
    $rentalSettings = getRentalSettings();
    ?>
    <div class="header">
        <div style="text-align: center; margin-bottom: 10px;">
            <img src="<?= base_url($rentalSettings['logo_rental']) ?>" alt="Logo" style="width: 40px; height: 40px; object-fit: contain;">
        </div>
        <div class="logo"><?= strtoupper($rentalSettings['nama_rental']) ?></div>
        <div class="info"><?= $rentalSettings['slogan_rental'] ?></div>
        <div class="info"><?= $rentalSettings['alamat_rental'] ?></div>
        <div class="info">WA: <?= $rentalSettings['whatsapp_rental'] ?></div>
    </div>

    <div class="content">
        <div class="row">
            <span>No. Transaksi:</span>
            <span>TRX-<?= date('Ymd', strtotime($sesi['created_at'])) ?>-<?= str_pad($sesi['id'], 4, '0', STR_PAD_LEFT) ?></span>
        </div>
        <div class="row">
            <span>Tanggal:</span>
            <span><?= date('d/m/Y H:i', strtotime($sesi['waktu_bayar'] ?? $sesi['created_at'])) ?></span>
        </div>
        <div class="row">
            <span>Kasir:</span>
            <span>Admin</span>
        </div>
    </div>

    <div class="separator"></div>

    <div class="content">
        <div class="row">
            <span>Jenis Transaksi:</span>
            <span>Pembayaran Sesi Gaming</span>
        </div>
        <div class="row">
            <span>Station:</span>
            <span><?= esc($sesi['nama_station']) ?></span>
        </div>
        <div class="row">
            <span>Konsol:</span>
            <span><?= esc($sesi['nama_konsol']) ?></span>
        </div>
        <div class="row">
            <span>Jenis User:</span>
            <span><?= ucfirst($sesi['jenis_user']) ?></span>
        </div>
        <?php if ($sesi['jenis_user'] === 'member' && !empty($sesi['nama_member'])): ?>
        <div class="row">
            <span>Member:</span>
            <span><?= esc($sesi['nama_member']) ?></span>
        </div>
        <?php endif; ?>
        <div class="row">
            <span>Waktu Mulai:</span>
            <span><?= date('d/m/Y H:i', strtotime($sesi['waktu_mulai'])) ?></span>
        </div>
        <div class="row">
            <span>Waktu Selesai:</span>
            <span><?= date('d/m/Y H:i', strtotime($sesi['waktu_berhenti'])) ?></span>
        </div>
        <?php
        $waktuMulai = strtotime($sesi['waktu_mulai']);
        $waktuSelesai = strtotime($sesi['waktu_berhenti']);
        $durasi = $waktuSelesai - $waktuMulai;
        $jam = floor($durasi / 3600);
        $menit = floor(($durasi % 3600) / 60);
        ?>
        <div class="row">
            <span>Durasi:</span>
            <span><?= $jam ?>j <?= $menit ?>m</span>
        </div>
    </div>

    <div class="separator"></div>

    <div class="content">
        <div class="row total">
            <span>TOTAL BAYAR:</span>
            <span>Rp <?= number_format($sesi['harga_total'], 0, ',', '.') ?></span>
        </div>
        <?php if (!empty($sesi['metode_bayar'])): ?>
        <div class="row">
            <span>Metode Bayar:</span>
            <span><?= ucfirst($sesi['metode_bayar']) ?></span>
        </div>
        <?php endif; ?>
        <?php if (!empty($sesi['jumlah_bayar'])): ?>
        <div class="row">
            <span>Jumlah Bayar:</span>
            <span>Rp <?= number_format($sesi['jumlah_bayar'], 0, ',', '.') ?></span>
        </div>
        <div class="row">
            <span>Kembalian:</span>
            <span>Rp <?= number_format($sesi['jumlah_bayar'] - $sesi['harga_total'], 0, ',', '.') ?></span>
        </div>
        <?php endif; ?>
    </div>

    <div class="footer">
        <div>*** TERIMA KASIH ***</div>
        <div>Selamat Bermain!</div>
        <div>WA: <?= $rentalSettings['whatsapp_rental'] ?></div>
        <div style="margin-top: 10px;">
            Powered by PlaySphere v3.0<br>
            <?= date('d/m/Y H:i:s') ?>
        </div>
    </div>

    <script>
        // Auto print when page loads (optional)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>
