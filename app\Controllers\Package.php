<?php namespace App\Controllers;

class Package extends BaseController
{
    protected $packageModel;

    public function __construct()
    {
        $this->packageModel = new \App\Models\PackageModel();
    }

    public function index()
    {
        $data = [
            'title' => 'Pengaturan Paket',
            'packages' => $this->packageModel->findAll()
        ];
        return view('package/index', $data);
    }
    public function toggle($id)
    {
        $status = $this->request->getJSON()->status;
        
        $this->packageModel->update($id, [
            'is_active' => $status ? 1 : 0
        ]);
    
        return $this->response->setJSON([
            'success' => true
        ]);
    }
    public function add()
    {
        $hours = (int)$this->request->getPost('hours');
        $minutes = (int)$this->request->getPost('minutes');
        $duration = ($hours * 60) + $minutes;
    
        $data = [
            'name' => $this->request->getPost('name'),
            'duration' => $duration, // dalam menit
            'price' => $this->request->getPost('price'),
            'description' => $this->request->getPost('description'),
            'is_active' => 1
        ];
    
        $this->packageModel->insert($data);
        return redirect()->to('/package')->with('message', 'Paket berhasil ditambahkan');
    }
    
    public function edit($id)
    {
        $hours = (int)$this->request->getPost('hours');
        $minutes = (int)$this->request->getPost('minutes');
        $duration = ($hours * 60) + $minutes;
    
        $data = [
            'name' => $this->request->getPost('name'),
            'duration' => $duration, // dalam menit
            'price' => $this->request->getPost('price'),
            'description' => $this->request->getPost('description')
        ];
    
        $this->packageModel->update($id, $data);
        return redirect()->to('/package')->with('message', 'Paket berhasil diupdate');
    }

    public function delete($id)
    {
        $this->packageModel->delete($id);
        return redirect()->to('/package')->with('message', 'Paket berhasil dihapus');
    }
}
