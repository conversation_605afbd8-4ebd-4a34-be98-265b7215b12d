/*
 * Test Colors untuk ST7789 Display
 * Upload script ini untuk test warna RGB565
 * 
 * CARA PENGGUNAAN:
 * 1. Buka folder iot/test_colors/ di Arduino IDE
 * 2. Upload ke Wemos D1 Mini
 * 3. Buka Serial Monitor (115200 baud)
 * 4. Lihat tampilan warna di layar
 */

#include <TFT_eSPI.h>

TFT_eSPI tft = TFT_eSPI();

// Test warna RGB565 - Multiple options untuk testing
#define TEST_BLACK        0x0000  // Hitam
#define TEST_WHITE        0xFFFF  // Putih
#define TEST_RED          0xF800  // <PERSON><PERSON>
#define TEST_GREEN        0x07E0  // Hijau
#define TEST_BLUE         0x001F  // Biru
#define TEST_YELLOW       0xFFE0  // Kuning
#define TEST_CYAN         0x07FF  // Cyan
#define TEST_MAGENTA      0xF81F  // Magenta
#define TEST_ORANGE       0xFD20  // Orange

// Card colors - Option 1 (Original)
#define CARD_GREEN_1      0x2589  // Hijau gelap
#define CARD_BLUE_1       0x4D9F  // Biru
#define CARD_GRAY_1       0x6B6D  // Abu-abu

// Card colors - Option 2 (Alternative)
#define CARD_GREEN_2      0x0400  // Hijau gelap alternatif
#define CARD_BLUE_2       0x001F  // Biru terang
#define CARD_GRAY_2       0x7BEF  // Abu-abu terang

// Card colors - Option 3 (Bright)
#define CARD_GREEN_3      0x07E0  // Hijau terang
#define CARD_BLUE_3       0x001F  // Biru terang
#define CARD_GRAY_3       0x8410  // Abu-abu gelap

void setup() {
    Serial.begin(115200);
    Serial.println("\n=== ST7789 COLOR TEST START ===");
    
    // Inisialisasi display
    tft.init();
    tft.setRotation(2);  // Rotation 2 sesuai konfigurasi
    tft.fillScreen(TEST_BLACK);
    
    Serial.println("Display initialized with rotation 2");
    Serial.println("Screen size: 240x240");
    
    // Test sequence
    Serial.println("\n1. Testing basic colors...");
    testBasicColors();
    delay(3000);
    
    Serial.println("2. Testing card colors...");
    testCardColors();
    delay(3000);
    
    Serial.println("3. Testing color gradients...");
    testColorGradients();
    delay(2000);
    
    Serial.println("\n=== Starting color cycle (Ctrl+C to stop) ===");
}

void loop() {
    // Cycle through card colors untuk simulasi PlaySphere
    testCardColorsCycle();
}

void testBasicColors() {
    tft.fillScreen(TEST_BLACK);
    tft.setTextSize(1);
    
    // Test basic colors dalam grid 3x3
    int w = 80, h = 60;
    
    // Row 1
    tft.fillRect(0, 0, w, h, TEST_RED);
    tft.setTextColor(TEST_WHITE);
    tft.setCursor(10, 25);
    tft.print("RED");
    
    tft.fillRect(w, 0, w, h, TEST_GREEN);
    tft.setTextColor(TEST_BLACK);
    tft.setCursor(w+10, 25);
    tft.print("GREEN");
    
    tft.fillRect(2*w, 0, w, h, TEST_BLUE);
    tft.setTextColor(TEST_WHITE);
    tft.setCursor(2*w+10, 25);
    tft.print("BLUE");
    
    // Row 2
    tft.fillRect(0, h, w, h, TEST_YELLOW);
    tft.setTextColor(TEST_BLACK);
    tft.setCursor(10, h+25);
    tft.print("YELLOW");
    
    tft.fillRect(w, h, w, h, TEST_CYAN);
    tft.setTextColor(TEST_BLACK);
    tft.setCursor(w+10, h+25);
    tft.print("CYAN");
    
    tft.fillRect(2*w, h, w, h, TEST_MAGENTA);
    tft.setTextColor(TEST_WHITE);
    tft.setCursor(2*w+10, h+25);
    tft.print("MAGENTA");
    
    // Row 3
    tft.fillRect(0, 2*h, w, h, TEST_WHITE);
    tft.setTextColor(TEST_BLACK);
    tft.setCursor(10, 2*h+25);
    tft.print("WHITE");
    
    tft.fillRect(w, 2*h, w, h, TEST_BLACK);
    tft.setTextColor(TEST_WHITE);
    tft.setCursor(w+10, 2*h+25);
    tft.print("BLACK");
    
    tft.fillRect(2*w, 2*h, w, h, TEST_ORANGE);
    tft.setTextColor(TEST_WHITE);
    tft.setCursor(2*w+10, 2*h+25);
    tft.print("ORANGE");
    
    // Bottom info
    tft.fillRect(0, 3*h, 240, 60, TEST_BLACK);
    tft.setTextColor(TEST_WHITE);
    tft.setTextSize(1);
    tft.setCursor(10, 3*h+10);
    tft.print("RGB565 Basic Colors Test");
    tft.setCursor(10, 3*h+25);
    tft.print("Check if colors match labels");
    
    Serial.println("Basic colors displayed - verify colors match labels");
}

void testCardColors() {
    tft.fillScreen(TEST_BLACK);
    tft.setTextSize(2);
    
    int y = 20;
    int h = 60;
    
    // Card Green (Active Station)
    tft.fillRoundRect(10, y, 220, h, 10, TEST_CARD_GREEN);
    tft.setTextColor(TEST_WHITE);
    tft.setCursor(30, y+20);
    tft.print("ACTIVE");
    
    y += h + 10;
    
    // Card Blue (Paused Station)
    tft.fillRoundRect(10, y, 220, h, 10, TEST_CARD_BLUE);
    tft.setTextColor(TEST_WHITE);
    tft.setCursor(30, y+20);
    tft.print("PAUSED");
    
    y += h + 10;
    
    // Card Gray (Standby Station)
    tft.fillRoundRect(10, y, 220, h, 10, TEST_CARD_GRAY);
    tft.setTextColor(TEST_WHITE);
    tft.setCursor(30, y+20);
    tft.print("STANDBY");
    
    // Info
    tft.fillRect(10, 210, 220, 25, TEST_BLACK);
    tft.setTextColor(TEST_WHITE);
    tft.setTextSize(1);
    tft.setCursor(15, 220);
    tft.print("PlaySphere Card Colors");
    
    Serial.println("Card colors displayed:");
    Serial.println("- Green: Active station");
    Serial.println("- Blue: Paused station");
    Serial.println("- Gray: Standby station");
}

void testColorGradients() {
    tft.fillScreen(TEST_BLACK);
    
    // Red gradient (top)
    for(int i = 0; i < 240; i++) {
        uint16_t red = (i * 31) / 240;  // 0-31 for red component
        uint16_t color = red << 11;
        tft.drawFastVLine(i, 0, 80, color);
    }
    
    // Green gradient (middle)
    for(int i = 0; i < 240; i++) {
        uint16_t green = (i * 63) / 240;  // 0-63 for green component
        uint16_t color = green << 5;
        tft.drawFastVLine(i, 80, 80, color);
    }
    
    // Blue gradient (bottom)
    for(int i = 0; i < 240; i++) {
        uint16_t blue = (i * 31) / 240;  // 0-31 for blue component
        uint16_t color = blue;
        tft.drawFastVLine(i, 160, 80, color);
    }
    
    // Labels
    tft.setTextColor(TEST_WHITE);
    tft.setTextSize(1);
    tft.setCursor(5, 5);
    tft.print("RED");
    tft.setCursor(5, 85);
    tft.print("GREEN");
    tft.setCursor(5, 165);
    tft.print("BLUE");
    
    Serial.println("RGB gradients displayed - check smooth transitions");
}

void testCardColorsCycle() {
    static int state = 0;
    static int colorOption = 1;
    static unsigned long lastChange = 0;
    static unsigned long lastColorChange = 0;

    // Change color option every 10 seconds
    if (millis() - lastColorChange > 10000) {
        lastColorChange = millis();
        colorOption = (colorOption % 3) + 1;
        Serial.println("\n=== SWITCHING TO COLOR OPTION " + String(colorOption) + " ===");
    }

    if (millis() - lastChange > 2000) {  // Change state every 2 seconds
        lastChange = millis();

        tft.fillScreen(TEST_BLACK);

        // Select colors based on current option
        uint16_t greenColor, blueColor, grayColor;
        switch(colorOption) {
            case 1:
                greenColor = CARD_GREEN_1;
                blueColor = CARD_BLUE_1;
                grayColor = CARD_GRAY_1;
                break;
            case 2:
                greenColor = CARD_GREEN_2;
                blueColor = CARD_BLUE_2;
                grayColor = CARD_GRAY_2;
                break;
            case 3:
                greenColor = CARD_GREEN_3;
                blueColor = CARD_BLUE_3;
                grayColor = CARD_GRAY_3;
                break;
        }

        switch(state) {
            case 0:
                // Standby simulation
                tft.fillRoundRect(10, 10, 220, 220, 15, grayColor);
                tft.setTextColor(TEST_WHITE);
                tft.setTextSize(2);
                tft.setCursor(30, 50);
                tft.print("Station 2");
                tft.setTextSize(3);
                tft.setCursor(70, 90);
                tft.print("PS-4");
                tft.setCursor(50, 130);
                tft.print("00:00:00");
                tft.setTextSize(1);
                tft.setCursor(50, 170);
                tft.print("PlaySphere Ready");
                tft.setCursor(30, 200);
                tft.print("Color Option " + String(colorOption));
                Serial.println("STANDBY (Gray " + String(colorOption) + ") - 0x" + String(grayColor, HEX));
                break;

            case 1:
                // Active simulation
                tft.fillRoundRect(10, 10, 220, 220, 15, greenColor);
                tft.setTextColor(TEST_WHITE);
                tft.setTextSize(2);
                tft.setCursor(30, 50);
                tft.print("Station 2");
                tft.setTextSize(3);
                tft.setCursor(70, 90);
                tft.print("PS-4");
                tft.setCursor(50, 130);
                tft.print("01:23:45");
                tft.setTextSize(1);
                tft.setCursor(80, 170);
                tft.print("Rp 15.000");
                tft.setCursor(30, 200);
                tft.print("Color Option " + String(colorOption));
                Serial.println("ACTIVE (Green " + String(colorOption) + ") - 0x" + String(greenColor, HEX));
                break;

            case 2:
                // Paused simulation
                tft.fillRoundRect(10, 10, 220, 220, 15, blueColor);
                tft.setTextColor(TEST_WHITE);
                tft.setTextSize(2);
                tft.setCursor(30, 50);
                tft.print("Station 2");
                tft.setTextSize(3);
                tft.setCursor(70, 90);
                tft.print("PS-4");
                tft.setTextColor(TEST_YELLOW);
                tft.setCursor(60, 130);
                tft.print("PAUSED");
                tft.setTextColor(TEST_WHITE);
                tft.setTextSize(1);
                tft.setCursor(80, 170);
                tft.print("Rp 12.000");
                tft.setCursor(30, 200);
                tft.print("Color Option " + String(colorOption));
                Serial.println("PAUSED (Blue " + String(colorOption) + ") - 0x" + String(blueColor, HEX));
                break;
        }

        state = (state + 1) % 3;
    }
}
