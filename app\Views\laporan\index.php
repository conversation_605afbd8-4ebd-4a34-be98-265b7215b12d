<?= $this->extend('layouts/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<div class="container-fluid">
  <!-- Header -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <h2 class="mb-1">
            <i class="bi bi-graph-up text-primary me-2"></i>
            Laporan & Statistik
          </h2>
          <p class="text-muted mb-0">Dashboard analitik dan laporan komprehensif</p>
        </div>
        <div>
          <button class="btn btn-outline-primary" onclick="exportAllReports()">
            <i class="bi bi-download me-1"></i>Export Semua
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Summary Cards -->
  <div class="row mb-4">
    <div class="col-md-3">
      <div class="card bg-gradient-primary text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h6 class="card-title">Total Konsol</h6>
              <h3 class="mb-0"><?= $totalKonsol ?></h3>
            </div>
            <div class="align-self-center">
              <i class="bi bi-controller" style="font-size: 2rem; opacity: 0.7;"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card bg-gradient-success text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h6 class="card-title">Total Station</h6>
              <h3 class="mb-0"><?= $totalStation ?></h3>
            </div>
            <div class="align-self-center">
              <i class="bi bi-display" style="font-size: 2rem; opacity: 0.7;"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card bg-gradient-info text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h6 class="card-title">Total Member</h6>
              <h3 class="mb-0"><?= $totalMember ?></h3>
            </div>
            <div class="align-self-center">
              <i class="bi bi-people" style="font-size: 2rem; opacity: 0.7;"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card bg-gradient-warning text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h6 class="card-title">Total Produk</h6>
              <h3 class="mb-0"><?= $totalProduk ?></h3>
            </div>
            <div class="align-self-center">
              <i class="bi bi-box" style="font-size: 2rem; opacity: 0.7;"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filter Controls -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <div class="row align-items-end">
            <div class="col-md-3">
              <label class="form-label">Periode</label>
              <select class="form-select" id="filterPeriode">
                <option value="hari">Harian</option>
                <option value="bulan">Bulanan</option>
                <option value="tahun">Tahunan</option>
              </select>
            </div>
            <div class="col-md-3" id="filterTanggalContainer">
              <label class="form-label">Tanggal</label>
              <input type="date" class="form-control" id="filterTanggal" value="<?= date('Y-m-d') ?>">
            </div>
            <div class="col-md-3" id="filterBulanContainer" style="display: none;">
              <label class="form-label">Bulan</label>
              <select class="form-select" id="filterBulan">
                <option value="2025-01">Januari 2025</option>
                <option value="2025-02">Februari 2025</option>
                <option value="2025-03">Maret 2025</option>
                <option value="2025-04">April 2025</option>
                <option value="2025-05">Mei 2025</option>
                <option value="2025-06">Juni 2025</option>
                <option value="2025-07" selected>Juli 2025</option>
                <option value="2025-08">Agustus 2025</option>
                <option value="2025-09">September 2025</option>
                <option value="2025-10">Oktober 2025</option>
                <option value="2025-11">November 2025</option>
                <option value="2025-12">Desember 2025</option>
              </select>
            </div>
            <div class="col-md-3" id="filterTahunContainer" style="display: none;">
              <label class="form-label">Tahun</label>
              <select class="form-select" id="filterTahun">
                <option value="2023">2023</option>
                <option value="2024">2024</option>
                <option value="2025" <?= date('Y') == '2025' ? 'selected' : '' ?>>2025</option>
                <option value="2026">2026</option>
                <option value="2027">2027</option>
              </select>
            </div>
            <div class="col-md-3">
              <button class="btn btn-primary w-100" id="filterButton">
                <i class="bi bi-search me-1"></i>Filter
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Tabs Navigation -->
  <ul class="nav nav-tabs mb-4" id="laporanTabs" role="tablist">
    <li class="nav-item" role="presentation">
      <button class="nav-link active" id="konsol-tab" data-bs-toggle="tab" data-bs-target="#konsol" type="button" role="tab">
        <i class="bi bi-controller me-1"></i>Konsol
      </button>
    </li>
    <li class="nav-item" role="presentation">
      <button class="nav-link" id="station-tab" data-bs-toggle="tab" data-bs-target="#station" type="button" role="tab">
        <i class="bi bi-display me-1"></i>Station
      </button>
    </li>
    <li class="nav-item" role="presentation">
      <button class="nav-link" id="member-tab" data-bs-toggle="tab" data-bs-target="#member" type="button" role="tab">
        <i class="bi bi-people me-1"></i>Member
      </button>
    </li>
    <li class="nav-item" role="presentation">
      <button class="nav-link" id="paket-tab" data-bs-toggle="tab" data-bs-target="#paket" type="button" role="tab">
        <i class="bi bi-box me-1"></i>Paket
      </button>
    </li>
    <li class="nav-item" role="presentation">
      <button class="nav-link" id="topup-tab" data-bs-toggle="tab" data-bs-target="#topup" type="button" role="tab">
        <i class="bi bi-credit-card me-1"></i>Top-up
      </button>
    </li>
    <li class="nav-item" role="presentation">
      <button class="nav-link" id="pendapatan-tab" data-bs-toggle="tab" data-bs-target="#pendapatan" type="button" role="tab">
        <i class="bi bi-currency-dollar me-1"></i>Pendapatan
      </button>
    </li>
    <li class="nav-item" role="presentation">
      <button class="nav-link" id="produk-tab" data-bs-toggle="tab" data-bs-target="#produk" type="button" role="tab">
        <i class="bi bi-bag me-1"></i>Produk
      </button>
    </li>
  </ul>

  <!-- Tab Content -->
  <div class="tab-content" id="laporanTabContent">
    
    <!-- Tab Konsol -->
    <div class="tab-pane fade show active" id="konsol" role="tabpanel">
      <div class="row">
        <div class="col-md-8">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-bar-chart me-2"></i>Penggunaan Konsol
              </h5>
            </div>
            <div class="card-body">
              <canvas id="chartKonsol" height="300"></canvas>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-pie-chart me-2"></i>Distribusi Penggunaan
              </h5>
            </div>
            <div class="card-body">
              <canvas id="chartKonsolPie" height="300"></canvas>
            </div>
          </div>
        </div>
      </div>
      <div class="row mt-3">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-table me-2"></i>Detail Laporan Konsol
              </h5>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-striped" id="tableKonsol">
                  <thead>
                    <tr>
                      <th>Konsol</th>
                      <th>Total Sesi</th>
                      <th>Total Durasi (Menit)</th>
                      <th>Total Pendapatan</th>
                    </tr>
                  </thead>
                  <tbody>
                    <!-- Data will be loaded dynamically -->
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Tab Station -->
    <div class="tab-pane fade" id="station" role="tabpanel">
      <div class="row">
        <div class="col-md-8">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-bar-chart me-2"></i>Penggunaan Station
              </h5>
            </div>
            <div class="card-body">
              <canvas id="chartStation" height="300"></canvas>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-pie-chart me-2"></i>Distribusi Penggunaan
              </h5>
            </div>
            <div class="card-body">
              <canvas id="chartStationPie" height="300"></canvas>
            </div>
          </div>
        </div>
      </div>
      <div class="row mt-3">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-table me-2"></i>Detail Laporan Station
              </h5>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-striped" id="tableStation">
                  <thead>
                    <tr>
                      <th>Station</th>
                      <th>Total Sesi</th>
                      <th>Total Durasi (Menit)</th>
                      <th>Total Pendapatan</th>
                    </tr>
                  </thead>
                  <tbody>
                    <!-- Data will be loaded dynamically -->
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Tab Member -->
    <div class="tab-pane fade" id="member" role="tabpanel">
      <div class="row">
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-pie-chart me-2"></i>Distribusi Saldo Member
              </h5>
            </div>
            <div class="card-body">
              <canvas id="chartMemberSaldo" height="300"></canvas>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-bar-chart me-2"></i>Aktivitas Member
              </h5>
            </div>
            <div class="card-body">
              <canvas id="chartMemberAktivitas" height="300"></canvas>
            </div>
          </div>
        </div>
      </div>
      <div class="row mt-3">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-table me-2"></i>Detail Member
              </h5>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-striped" id="tableMember">
                  <thead>
                    <tr>
                      <th>Nama</th>
                      <th>No. WhatsApp</th>
                      <th>Saldo</th>
                      <th>Total Sesi</th>
                      <th>Total Top-up</th>
                    </tr>
                  </thead>
                  <tbody>
                    <!-- Data will be loaded dynamically -->
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Tab Paket -->
    <div class="tab-pane fade" id="paket" role="tabpanel">
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-bar-chart me-2"></i>Popularitas Paket
              </h5>
            </div>
            <div class="card-body">
              <canvas id="chartPaket" height="400"></canvas>
            </div>
          </div>
        </div>
      </div>
      <div class="row mt-3">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-table me-2"></i>Statistik Popularitas Paket
              </h5>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-striped" id="tablePaket">
                  <thead>
                    <tr>
                      <th>Nama Paket</th>
                      <th>Konsol</th>
                      <th>Total Penggunaan</th>
                      <th>Popularitas</th>
                    </tr>
                  </thead>
                  <tbody>
                    <!-- Data will be loaded dynamically -->
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Tab Top-up -->
    <div class="tab-pane fade" id="topup" role="tabpanel">
      <div class="row">
        <div class="col-md-12">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-line-chart me-2"></i>Trend Top-up Member
              </h5>
            </div>
            <div class="card-body">
              <canvas id="chartTopup" height="200"></canvas>
            </div>
          </div>
        </div>
      </div>
      <div class="row mt-3">
        <div class="col-md-4">
          <div class="card bg-gradient-success text-white">
            <div class="card-body text-center">
              <h6>Total Top-up</h6>
              <h3 id="totalTopupAmount">Rp 0</h3>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card bg-gradient-info text-white">
            <div class="card-body text-center">
              <h6>Total Transaksi</h6>
              <h3 id="totalTopupTransaksi">0</h3>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card bg-gradient-warning text-white">
            <div class="card-body text-center">
              <h6>Rata-rata per Transaksi</h6>
              <h3 id="avgTopupAmount">Rp 0</h3>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Tab Pendapatan -->
    <div class="tab-pane fade" id="pendapatan" role="tabpanel">
      <div class="row">
        <div class="col-md-8">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-line-chart me-2"></i>Trend Pendapatan
              </h5>
            </div>
            <div class="card-body">
              <canvas id="chartPendapatan" height="300"></canvas>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-pie-chart me-2"></i>Sumber Pendapatan
              </h5>
            </div>
            <div class="card-body">
              <canvas id="chartPendapatanSumber" height="300"></canvas>
            </div>
          </div>
        </div>
      </div>
      <div class="row mt-3">
        <div class="col-md-3">
          <div class="card bg-gradient-primary text-white">
            <div class="card-body text-center">
              <h6>Pendapatan Sesi</h6>
              <h3 id="pendapatanSesi">Rp 0</h3>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card bg-gradient-success text-white">
            <div class="card-body text-center">
              <h6>Pendapatan Top-up</h6>
              <h3 id="pendapatanTopup">Rp 0</h3>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card bg-gradient-info text-white">
            <div class="card-body text-center">
              <h6>Pendapatan Produk</h6>
              <h3 id="pendapatanProduk">Rp 0</h3>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card bg-gradient-warning text-white">
            <div class="card-body text-center">
              <h6>Total Pendapatan</h6>
              <h3 id="totalPendapatan">Rp 0</h3>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Tab Produk -->
    <div class="tab-pane fade" id="produk" role="tabpanel">
      <div class="row">
        <div class="col-md-8">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-bar-chart me-2"></i>Penjualan Produk
              </h5>
            </div>
            <div class="card-body">
              <canvas id="chartProduk" height="300"></canvas>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="bi bi-trophy me-2"></i>Produk Terlaris
              </h5>
            </div>
            <div class="card-body">
              <div id="produkTerlaris">
                <div class="text-center text-muted">
                  <i class="bi bi-info-circle"></i>
                  <p>Fitur akan tersedia setelah ada transaksi penjualan</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>

<style>
.bg-gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-gradient-success {
  background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.bg-gradient-info {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-gradient-warning {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.card {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border: 1px solid rgba(0, 0, 0, 0.125);
}

.nav-tabs .nav-link {
  border: none;
  color: #6c757d;
}

.nav-tabs .nav-link.active {
  background-color: #fff;
  border-bottom: 3px solid #0d6efd;
  color: #0d6efd;
}

.table th {
  background-color: #f8f9fa;
  font-weight: 600;
}
</style>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Global variables for charts
let chartKonsol, chartKonsolPie, chartStation, chartStationPie;
let chartMemberSaldo, chartMemberAktivitas, chartPaket, chartPaketPendapatan;
let chartTopup, chartPendapatan, chartPendapatanSumber, chartProduk;

// Color palettes
const colors = {
  primary: ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe'],
  success: ['#11998e', '#38ef7d', '#56ab2f', '#a8edea', '#fed6e3'],
  info: ['#667eea', '#764ba2', '#667eea', '#764ba2', '#667eea'],
  warning: ['#f093fb', '#f5576c', '#ffecd2', '#fcb69f', '#ffecd2']
};

// Helper functions
function formatPrice(amount) {
  return 'Rp ' + parseInt(amount).toLocaleString('id-ID').replace(/,/g, '.');
}

function updateDateFilter() {
  console.log('=== updateDateFilter CALLED ===');

  const filterPeriode = document.getElementById('filterPeriode');
  if (!filterPeriode) {
    console.error('filterPeriode element not found!');
    return;
  }

  const periode = filterPeriode.value;
  console.log('Current periode value:', periode);

  // Get elements
  const tanggalContainer = document.getElementById('filterTanggalContainer');
  const bulanContainer = document.getElementById('filterBulanContainer');
  const tahunContainer = document.getElementById('filterTahunContainer');

  console.log('Container elements:', {
    tanggal: !!tanggalContainer,
    bulan: !!bulanContainer,
    tahun: !!tahunContainer
  });

  if (tanggalContainer) console.log('tanggalContainer current display:', tanggalContainer.style.display);
  if (bulanContainer) console.log('bulanContainer current display:', bulanContainer.style.display);
  if (tahunContainer) console.log('tahunContainer current display:', tahunContainer.style.display);

  // Hide all filter containers
  if (tanggalContainer) {
    tanggalContainer.style.display = 'none';
    console.log('Hidden tanggal container');
  }
  if (bulanContainer) {
    bulanContainer.style.display = 'none';
    console.log('Hidden bulan container');
  }
  if (tahunContainer) {
    tahunContainer.style.display = 'none';
    console.log('Hidden tahun container');
  }

  // Show appropriate filter
  console.log('Switching on periode:', periode);
  switch(periode) {
    case 'hari':
      if (tanggalContainer) {
        tanggalContainer.style.display = 'block';
        console.log('✅ SHOWING tanggal filter');
      } else {
        console.error('❌ tanggalContainer not found');
      }
      break;
    case 'bulan':
      if (bulanContainer) {
        bulanContainer.style.display = 'block';
        console.log('✅ SHOWING bulan filter');
      } else {
        console.error('❌ bulanContainer not found');
      }
      break;
    case 'tahun':
      if (tahunContainer) {
        tahunContainer.style.display = 'block';
        console.log('✅ SHOWING tahun filter');
      } else {
        console.error('❌ tahunContainer not found');
      }
      break;
    default:
      console.error('Unknown periode:', periode);
  }

  console.log('=== updateDateFilter COMPLETED ===');
}

function getFilterParams() {
  const periode = document.getElementById('filterPeriode').value;
  let params = { periode: periode };

  switch(periode) {
    case 'hari':
      params.tanggal = document.getElementById('filterTanggal').value;
      break;
    case 'bulan':
      params.bulan = document.getElementById('filterBulan').value;
      break;
    case 'tahun':
      params.tahun = document.getElementById('filterTahun').value;
      break;
  }

  return params;
}

function loadAllReports() {
  console.log('Loading all reports...');

  // Load current active tab
  const activeTab = document.querySelector('#laporanTabs .nav-link.active');
  const target = activeTab.getAttribute('data-bs-target');

  switch(target) {
    case '#konsol':
      loadLaporanKonsol();
      break;
    case '#station':
      loadLaporanStation();
      break;
    case '#member':
      loadLaporanMember();
      break;
    case '#paket':
      loadLaporanPaket();
      break;
    case '#topup':
      loadLaporanTopup();
      break;
    case '#pendapatan':
      loadLaporanPendapatan();
      break;
    case '#produk':
      loadLaporanProduk();
      break;
  }
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
  console.log('=== LAPORAN PAGE LOADED ===');

  // Test if elements exist
  const filterPeriode = document.getElementById('filterPeriode');
  const filterButton = document.getElementById('filterButton');
  const tanggalContainer = document.getElementById('filterTanggalContainer');
  const bulanContainer = document.getElementById('filterBulanContainer');
  const tahunContainer = document.getElementById('filterTahunContainer');

  console.log('Elements check:', {
    filterPeriode: !!filterPeriode,
    filterButton: !!filterButton,
    tanggalContainer: !!tanggalContainer,
    bulanContainer: !!bulanContainer,
    tahunContainer: !!tahunContainer
  });

  if (!filterPeriode) {
    console.error('filterPeriode element not found!');
    return;
  }

  // Initialize filter display
  console.log('Calling updateDateFilter...');
  updateDateFilter();

  // Add event listeners
  filterPeriode.addEventListener('change', function() {
    console.log('Periode changed to:', this.value);
    updateDateFilter();
  });

  if (filterButton) {
    filterButton.addEventListener('click', function() {
      console.log('Filter button clicked');
      loadAllReports();
    });
  }

  // Load initial report (konsol) after a short delay to ensure DOM is ready
  setTimeout(() => {
    console.log('Loading initial konsol report...');
    loadLaporanKonsol();
  }, 1000);

  // Tab change event
  document.querySelectorAll('#laporanTabs button[data-bs-toggle="tab"]').forEach(tab => {
    tab.addEventListener('shown.bs.tab', function(e) {
      const target = e.target.getAttribute('data-bs-target');
      console.log('Tab changed to:', target);

      // Load specific report when tab is shown
      switch(target) {
        case '#konsol':
          loadLaporanKonsol();
          break;
        case '#station':
          loadLaporanStation();
          break;
        case '#member':
          loadLaporanMember();
          break;
        case '#paket':
          loadLaporanPaket();
          break;
        case '#topup':
          loadLaporanTopup();
          break;
        case '#pendapatan':
          loadLaporanPendapatan();
          break;
        case '#produk':
          loadLaporanProduk();
          break;
      }
    });
  });
});

// Load laporan konsol
function loadLaporanKonsol() {
  console.log('Loading laporan konsol...');

  const params = getFilterParams();
  const queryString = new URLSearchParams(params).toString();
  const url = `<?= base_url('laporan/laporanKonsol') ?>?${queryString}`;

  console.log('Fetching from URL:', url);
  console.log('Params:', params);

  fetch(url, {
    method: 'GET',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => {
    console.log('Response status:', response.status);
    return response.json();
  })
  .then(data => {
    console.log('Konsol data received:', data);
    if (data.success) {
      console.log('Rendering konsol charts with data:', data.data);
      renderKonsolChart(data.data);
      renderKonsolTable(data.data);
    } else {
      console.error('Failed to load konsol report:', data.message);
      alert('Error: ' + data.message);
    }
  })
  .catch(error => {
    console.error('Error loading konsol report:', error);
    alert('Network error: ' + error.message);
  });
}

// Load laporan station
function loadLaporanStation() {
  console.log('Loading laporan station...');

  const params = getFilterParams();
  const queryString = new URLSearchParams(params).toString();

  fetch(`<?= base_url('laporan/laporanStation') ?>?${queryString}`, {
    method: 'GET',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      renderStationChart(data.data);
      renderStationTable(data.data);
    } else {
      console.error('Failed to load station report:', data.message);
    }
  })
  .catch(error => {
    console.error('Error loading station report:', error);
  });
}

// Load laporan member
function loadLaporanMember() {
  console.log('Loading laporan member...');

  fetch('<?= base_url('laporan/laporanMember') ?>', {
    method: 'GET',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      renderMemberChart(data.data);
      renderMemberTable(data.data);
    } else {
      console.error('Failed to load member report:', data.message);
    }
  })
  .catch(error => {
    console.error('Error loading member report:', error);
  });
}

// Load laporan paket
function loadLaporanPaket() {
  console.log('Loading laporan paket...');

  fetch('<?= base_url('laporan/laporanPaket') ?>', {
    method: 'GET',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      renderPaketChart(data.data);
      renderPaketTable(data.data);
    } else {
      console.error('Failed to load paket report:', data.message);
    }
  })
  .catch(error => {
    console.error('Error loading paket report:', error);
  });
}

// Load laporan topup
function loadLaporanTopup() {
  console.log('Loading laporan topup...');

  const params = getFilterParams();
  const queryString = new URLSearchParams(params).toString();

  fetch(`<?= base_url('laporan/laporanTopup') ?>?${queryString}`, {
    method: 'GET',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      renderTopupChart(data.data);
      renderTopupStats(data.data);
    } else {
      console.error('Failed to load topup report:', data.message);
    }
  })
  .catch(error => {
    console.error('Error loading topup report:', error);
  });
}

// Load laporan pendapatan
function loadLaporanPendapatan() {
  console.log('Loading laporan pendapatan...');

  const params = getFilterParams();
  const queryString = new URLSearchParams(params).toString();

  fetch(`<?= base_url('laporan/laporanPendapatan') ?>?${queryString}`, {
    method: 'GET',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      renderPendapatanChart(data.data);
      renderPendapatanStats(data.data);
    } else {
      console.error('Failed to load pendapatan report:', data.message);
    }
  })
  .catch(error => {
    console.error('Error loading pendapatan report:', error);
  });
}

// Load laporan produk
function loadLaporanProduk() {
  console.log('Loading laporan produk...');

  const params = getFilterParams();
  const queryString = new URLSearchParams(params).toString();

  fetch(`<?= base_url('laporan/laporanProduk') ?>?${queryString}`, {
    method: 'GET',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      renderProdukChart(data.data);
    } else {
      console.error('Failed to load produk report:', data.message);
    }
  })
  .catch(error => {
    console.error('Error loading produk report:', error);
  });
}

// ========== CHART RENDERING FUNCTIONS ==========

// Render konsol charts
function renderKonsolChart(data) {
  console.log('renderKonsolChart called with data:', data);

  const ctx = document.getElementById('chartKonsol');
  if (!ctx) {
    console.error('Chart canvas not found');
    return;
  }

  // Destroy existing chart
  if (chartKonsol) {
    chartKonsol.destroy();
  }

  // Handle empty data
  if (!data || data.length === 0) {
    console.log('No data to render for konsol chart');
    return;
  }

  chartKonsol = new Chart(ctx.getContext('2d'), {
    type: 'bar',
    data: {
      labels: data.map(item => item.konsol),
      datasets: [
        {
          label: 'Total Sesi',
          data: data.map(item => item.total_sesi),
          backgroundColor: colors.primary[0],
          borderColor: colors.primary[0],
          borderWidth: 1,
          yAxisID: 'y'
        },
        {
          label: 'Total Durasi (Menit)',
          data: data.map(item => item.total_durasi),
          backgroundColor: colors.success[0],
          borderColor: colors.success[0],
          borderWidth: 1,
          yAxisID: 'y1'
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          type: 'linear',
          display: true,
          position: 'left',
          title: {
            display: true,
            text: 'Jumlah Sesi'
          }
        },
        y1: {
          type: 'linear',
          display: true,
          position: 'right',
          title: {
            display: true,
            text: 'Durasi (Menit)'
          },
          grid: {
            drawOnChartArea: false,
          },
        }
      },
      plugins: {
        legend: {
          display: true,
          position: 'top'
        },
        title: {
          display: true,
          text: 'Statistik Penggunaan Konsol'
        }
      }
    }
  });

  // Render pie chart
  const ctxPie = document.getElementById('chartKonsolPie');
  if (ctxPie) {
    if (chartKonsolPie) {
      chartKonsolPie.destroy();
    }

    chartKonsolPie = new Chart(ctxPie.getContext('2d'), {
      type: 'doughnut',
      data: {
        labels: data.map(item => item.konsol),
        datasets: [{
          data: data.map(item => item.total_pendapatan),
          backgroundColor: colors.primary,
          borderWidth: 2,
          borderColor: '#fff'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: true,
            position: 'bottom'
          },
          title: {
            display: true,
            text: 'Pendapatan per Konsol'
          }
        }
      }
    });
  }
}

// Render konsol table
function renderKonsolTable(data) {
  const tbody = document.querySelector('#tableKonsol tbody');
  if (!tbody) {
    console.error('Table tbody not found');
    return;
  }

  let html = '';

  data.forEach(item => {
    html += `
      <tr>
        <td><strong>${item.konsol}</strong></td>
        <td><span class="badge bg-primary">${item.total_sesi}</span></td>
        <td><span class="badge bg-success">${item.total_durasi}</span></td>
        <td><strong>${formatPrice(item.total_pendapatan)}</strong></td>
      </tr>
    `;
  });

  tbody.innerHTML = html;
}

// Placeholder functions for other charts
function renderStationChart(data) {
  console.log('renderStationChart called with data:', data);
  // Implementation similar to konsol chart
}

function renderStationTable(data) {
  console.log('renderStationTable called with data:', data);
  // Implementation similar to konsol table
}

function renderMemberChart(data) {
  console.log('renderMemberChart called with data:', data);

  // Call the actual chart rendering functions
  renderMemberSaldoChart(data);
  renderMemberAktivitasChart(data);
}

function renderMemberTable(data) {
  console.log('renderMemberTable called with data:', data);
}

function renderPaketChart(data) {
  console.log('renderPaketChart called with data:', data);

  // Call the actual chart rendering function
  renderPaketBarChart(data);
}

function renderPaketTable(data) {
  console.log('renderPaketTable called with data:', data);

  // Call the actual table rendering function
  renderPaketTableData(data);
}

function renderTopupChart(data) {
  console.log('renderTopupChart called with data:', data);
}

function renderTopupStats(data) {
  console.log('renderTopupStats called with data:', data);
}

function renderPendapatanChart(data) {
  console.log('renderPendapatanChart called with data:', data);

  // Call the actual chart rendering function
  renderPendapatanLineChart(data);
}

function renderPendapatanStats(data) {
  console.log('renderPendapatanStats called with data:', data);
}

function renderProdukChart(data) {
  console.log('renderProdukChart called with data:', data);

  // Render chart
  renderProdukBarChart(data);

  // Render produk terlaris
  renderProdukTerlaris(data.produk_terlaris || []);
}

// Export functions
function exportAllReports() {
  Swal.fire('Info', 'Fitur export akan segera tersedia', 'info');
}

function getFilterParams() {
  const periode = document.getElementById('filterPeriode').value;
  let params = { periode: periode };

  switch(periode) {
    case 'hari':
      params.tanggal = document.getElementById('filterTanggal').value;
      break;
    case 'bulan':
      params.bulan = document.getElementById('filterBulan').value;
      break;
    case 'tahun':
      params.tahun = document.getElementById('filterTahun').value;
      break;
  }

  return params;
}

function loadAllReports() {
  console.log('Loading all reports...');

  // Load current active tab
  const activeTab = document.querySelector('#laporanTabs .nav-link.active');
  const target = activeTab.getAttribute('data-bs-target');

  switch(target) {
    case '#konsol':
      loadLaporanKonsol();
      break;
    case '#station':
      loadLaporanStation();
      break;
    case '#member':
      loadLaporanMember();
      break;
    case '#paket':
      loadLaporanPaket();
      break;
    case '#topup':
      loadLaporanTopup();
      break;
    case '#pendapatan':
      loadLaporanPendapatan();
      break;
    case '#produk':
      loadLaporanProduk();
      break;
  }
}

// Load laporan konsol
function loadLaporanKonsol() {
  console.log('Loading laporan konsol...');

  const params = getFilterParams();
  const queryString = new URLSearchParams(params).toString();
  const url = `<?= base_url('laporan/laporanKonsol') ?>?${queryString}`;

  console.log('Fetching from URL:', url);
  console.log('Params:', params);

  fetch(url, {
    method: 'GET',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => {
    console.log('Response status:', response.status);
    return response.json();
  })
  .then(data => {
    console.log('Konsol data received:', data);
    if (data.success) {
      console.log('Rendering konsol charts with data:', data.data);
      renderKonsolChart(data.data);
      renderKonsolTable(data.data);
    } else {
      console.error('Failed to load konsol report:', data.message);
      alert('Error: ' + data.message);
    }
  })
  .catch(error => {
    console.error('Error loading konsol report:', error);
    alert('Network error: ' + error.message);
  });
}

// Load laporan station
function loadLaporanStation() {
  console.log('Loading laporan station...');

  const params = getFilterParams();
  const queryString = new URLSearchParams(params).toString();

  fetch(`<?= base_url('laporan/laporanStation') ?>?${queryString}`, {
    method: 'GET',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      renderStationChart(data.data);
      renderStationTable(data.data);
    } else {
      console.error('Failed to load station report:', data.message);
    }
  })
  .catch(error => {
    console.error('Error loading station report:', error);
  });
}

// Load laporan member
function loadLaporanMember() {
  console.log('Loading laporan member...');

  fetch('<?= base_url('laporan/laporanMember') ?>', {
    method: 'GET',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      renderMemberChart(data.data);
      renderMemberTable(data.data);
    } else {
      console.error('Failed to load member report:', data.message);
    }
  })
  .catch(error => {
    console.error('Error loading member report:', error);
  });
}

// Load laporan paket
function loadLaporanPaket() {
  console.log('Loading laporan paket...');

  fetch('<?= base_url('laporan/laporanPaket') ?>', {
    method: 'GET',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      renderPaketChart(data.data);
      renderPaketTable(data.data);
    } else {
      console.error('Failed to load paket report:', data.message);
    }
  })
  .catch(error => {
    console.error('Error loading paket report:', error);
  });
}

// Load laporan topup
function loadLaporanTopup() {
  console.log('Loading laporan topup...');

  const params = getFilterParams();
  const queryString = new URLSearchParams(params).toString();

  fetch(`<?= base_url('laporan/laporanTopup') ?>?${queryString}`, {
    method: 'GET',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      renderTopupChart(data.data);
      renderTopupStats(data.data);
    } else {
      console.error('Failed to load topup report:', data.message);
    }
  })
  .catch(error => {
    console.error('Error loading topup report:', error);
  });
}

// Load laporan pendapatan
function loadLaporanPendapatan() {
  console.log('Loading laporan pendapatan...');

  const params = getFilterParams();
  const queryString = new URLSearchParams(params).toString();

  fetch(`<?= base_url('laporan/laporanPendapatan') ?>?${queryString}`, {
    method: 'GET',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      renderPendapatanChart(data.data);
      renderPendapatanStats(data.data);
    } else {
      console.error('Failed to load pendapatan report:', data.message);
    }
  })
  .catch(error => {
    console.error('Error loading pendapatan report:', error);
  });
}

// Load laporan produk
function loadLaporanProduk() {
  console.log('Loading laporan produk...');

  const params = getFilterParams();
  const queryString = new URLSearchParams(params).toString();

  fetch(`<?= base_url('laporan/laporanProduk') ?>?${queryString}`, {
    method: 'GET',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      renderProdukChart(data.data);
    } else {
      console.error('Failed to load produk report:', data.message);
    }
  })
  .catch(error => {
    console.error('Error loading produk report:', error);
  });
}

// ========== CHART RENDERING FUNCTIONS ==========

// Render konsol charts
function renderKonsolChart(data) {
  console.log('renderKonsolChart called with data:', data);

  const ctx = document.getElementById('chartKonsol').getContext('2d');

  // Destroy existing chart
  if (chartKonsol) {
    chartKonsol.destroy();
  }

  // Handle empty data
  if (!data || data.length === 0) {
    console.log('No data to render for konsol chart');
    return;
  }

  chartKonsol = new Chart(ctx, {
    type: 'bar',
    data: {
      labels: data.map(item => item.konsol),
      datasets: [
        {
          label: 'Total Sesi',
          data: data.map(item => item.total_sesi),
          backgroundColor: colors.primary[0],
          borderColor: colors.primary[0],
          borderWidth: 1,
          yAxisID: 'y'
        },
        {
          label: 'Total Durasi (Menit)',
          data: data.map(item => item.total_durasi),
          backgroundColor: colors.success[0],
          borderColor: colors.success[0],
          borderWidth: 1,
          yAxisID: 'y1'
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          type: 'linear',
          display: true,
          position: 'left',
          title: {
            display: true,
            text: 'Jumlah Sesi'
          }
        },
        y1: {
          type: 'linear',
          display: true,
          position: 'right',
          title: {
            display: true,
            text: 'Durasi (Menit)'
          },
          grid: {
            drawOnChartArea: false,
          },
        }
      },
      plugins: {
        legend: {
          display: true,
          position: 'top'
        },
        title: {
          display: true,
          text: 'Statistik Penggunaan Konsol'
        }
      }
    }
  });

  // Render pie chart
  const ctxPie = document.getElementById('chartKonsolPie').getContext('2d');

  if (chartKonsolPie) {
    chartKonsolPie.destroy();
  }

  chartKonsolPie = new Chart(ctxPie, {
    type: 'doughnut',
    data: {
      labels: data.map(item => item.konsol),
      datasets: [{
        data: data.map(item => item.total_pendapatan),
        backgroundColor: colors.primary,
        borderWidth: 2,
        borderColor: '#fff'
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: true,
          position: 'bottom'
        },
        title: {
          display: true,
          text: 'Pendapatan per Konsol'
        }
      }
    }
  });
}

// Render konsol table
function renderKonsolTable(data) {
  const tbody = document.querySelector('#tableKonsol tbody');
  let html = '';

  data.forEach(item => {
    html += `
      <tr>
        <td><strong>${item.konsol}</strong></td>
        <td><span class="badge bg-primary">${item.total_sesi}</span></td>
        <td><span class="badge bg-success">${item.total_durasi}</span></td>
        <td><strong>${formatPrice(item.total_pendapatan)}</strong></td>
      </tr>
    `;
  });

  tbody.innerHTML = html;
}

// Render station charts
function renderStationChart(data) {
  const ctx = document.getElementById('chartStation').getContext('2d');

  if (chartStation) {
    chartStation.destroy();
  }

  chartStation = new Chart(ctx, {
    type: 'bar',
    data: {
      labels: data.map(item => item.station),
      datasets: [
        {
          label: 'Total Sesi',
          data: data.map(item => item.total_sesi),
          backgroundColor: colors.success[0],
          borderColor: colors.success[0],
          borderWidth: 1,
          yAxisID: 'y'
        },
        {
          label: 'Total Durasi (Menit)',
          data: data.map(item => item.total_durasi),
          backgroundColor: colors.info[0],
          borderColor: colors.info[0],
          borderWidth: 1,
          yAxisID: 'y1'
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          type: 'linear',
          display: true,
          position: 'left',
          title: {
            display: true,
            text: 'Jumlah Sesi'
          }
        },
        y1: {
          type: 'linear',
          display: true,
          position: 'right',
          title: {
            display: true,
            text: 'Durasi (Menit)'
          },
          grid: {
            drawOnChartArea: false,
          },
        }
      },
      plugins: {
        legend: {
          display: true,
          position: 'top'
        },
        title: {
          display: true,
          text: 'Statistik Penggunaan Station'
        }
      }
    }
  });

  // Render pie chart
  const ctxPie = document.getElementById('chartStationPie').getContext('2d');

  if (chartStationPie) {
    chartStationPie.destroy();
  }

  chartStationPie = new Chart(ctxPie, {
    type: 'doughnut',
    data: {
      labels: data.map(item => item.station),
      datasets: [{
        data: data.map(item => item.total_pendapatan),
        backgroundColor: colors.success,
        borderWidth: 2,
        borderColor: '#fff'
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: true,
          position: 'bottom'
        },
        title: {
          display: true,
          text: 'Pendapatan per Station'
        }
      }
    }
  });
}

// Render station table
function renderStationTable(data) {
  const tbody = document.querySelector('#tableStation tbody');
  let html = '';

  data.forEach(item => {
    html += `
      <tr>
        <td><strong>${item.station}</strong></td>
        <td><span class="badge bg-success">${item.total_sesi}</span></td>
        <td><span class="badge bg-info">${item.total_durasi}</span></td>
        <td><strong>${formatPrice(item.total_pendapatan)}</strong></td>
      </tr>
    `;
  });

  tbody.innerHTML = html;
}

// Render member saldo chart
function renderMemberSaldoChart(data) {
  const ctxSaldo = document.getElementById('chartMemberSaldo');
  if (!ctxSaldo) {
    console.error('Chart member saldo canvas not found');
    return;
  }

  if (!data || data.length === 0) {
    console.log('No member data to render');
    return;
  }

  if (chartMemberSaldo) {
    chartMemberSaldo.destroy();
  }

  // Group members by saldo range
  const saldoRanges = {
    '0-50k': 0,
    '50k-100k': 0,
    '100k-500k': 0,
    '500k+': 0
  };

  data.forEach(member => {
    const saldo = parseInt(member.saldo);
    if (saldo < 50000) saldoRanges['0-50k']++;
    else if (saldo < 100000) saldoRanges['50k-100k']++;
    else if (saldo < 500000) saldoRanges['100k-500k']++;
    else saldoRanges['500k+']++;
  });

  chartMemberSaldo = new Chart(ctxSaldo.getContext('2d'), {
    type: 'pie',
    data: {
      labels: Object.keys(saldoRanges),
      datasets: [{
        data: Object.values(saldoRanges),
        backgroundColor: colors.info,
        borderWidth: 2,
        borderColor: '#fff'
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: true,
          position: 'bottom'
        },
        title: {
          display: true,
          text: 'Distribusi Saldo Member'
        }
      }
    }
  });
}

// Render member aktivitas chart
function renderMemberAktivitasChart(data) {
  const ctxAktivitas = document.getElementById('chartMemberAktivitas');
  if (!ctxAktivitas) {
    console.error('Chart member aktivitas canvas not found');
    return;
  }

  if (!data || data.length === 0) {
    console.log('No member data to render');
    return;
  }

  if (chartMemberAktivitas) {
    chartMemberAktivitas.destroy();
  }

  // Sort by total sesi and take top 10
  const topMembers = data.sort((a, b) => b.total_sesi - a.total_sesi).slice(0, 10);

  chartMemberAktivitas = new Chart(ctxAktivitas.getContext('2d'), {
    type: 'bar',
    data: {
      labels: topMembers.map(member => member.nama),
      datasets: [{
        label: 'Total Sesi',
        data: topMembers.map(member => member.total_sesi),
        backgroundColor: colors.warning[0],
        borderColor: colors.warning[0],
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      indexAxis: 'y',
      plugins: {
        legend: {
          display: false
        },
        title: {
          display: true,
          text: 'Top 10 Member Teraktif'
        }
      }
    }
  });
}

// Render member table
function renderMemberTable(data) {
  const tbody = document.querySelector('#tableMember tbody');
  let html = '';

  data.forEach(member => {
    html += `
      <tr>
        <td><strong>${member.nama}</strong><br><small class="text-muted">${member.id_member}</small></td>
        <td>${member.no_wa || '-'}</td>
        <td><span class="badge bg-success">${formatPrice(member.saldo)}</span></td>
        <td><span class="badge bg-primary">${member.total_sesi}</span></td>
        <td><span class="badge bg-info">${formatPrice(member.total_topup)}</span></td>
      </tr>
    `;
  });

  tbody.innerHTML = html;
}

// Render paket bar chart
function renderPaketBarChart(data) {
  const ctx = document.getElementById('chartPaket');
  if (!ctx) {
    console.error('Chart paket canvas not found');
    return;
  }

  if (!data || data.length === 0) {
    console.log('No paket data to render');
    return;
  }

  if (chartPaket) {
    chartPaket.destroy();
  }

  chartPaket = new Chart(ctx.getContext('2d'), {
    type: 'bar',
    data: {
      labels: data.map(item => item.nama_paket),
      datasets: [{
        label: 'Total Penggunaan',
        data: data.map(item => item.total_penggunaan),
        backgroundColor: colors.warning[0],
        borderColor: colors.warning[0],
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        },
        title: {
          display: true,
          text: 'Popularitas Paket Gaming'
        }
      }
    }
  });
}

// Render paket table data
function renderPaketTableData(data) {
  const tbody = document.querySelector('#tablePaket tbody');
  if (!tbody) {
    console.error('Paket table tbody not found');
    return;
  }

  if (!data || data.length === 0) {
    tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">Tidak ada data paket untuk periode ini</td></tr>';
    return;
  }
  let html = '';

  // Sort by total_penggunaan descending
  const sortedData = data.sort((a, b) => b.total_penggunaan - a.total_penggunaan);
  const maxUsage = sortedData[0]?.total_penggunaan || 1;

  sortedData.forEach((paket, index) => {
    const popularityPercentage = Math.round((paket.total_penggunaan / maxUsage) * 100);
    let popularityBadge = '';

    if (popularityPercentage >= 80) {
      popularityBadge = '<span class="badge bg-success">Sangat Populer</span>';
    } else if (popularityPercentage >= 60) {
      popularityBadge = '<span class="badge bg-warning">Populer</span>';
    } else if (popularityPercentage >= 30) {
      popularityBadge = '<span class="badge bg-info">Cukup Populer</span>';
    } else {
      popularityBadge = '<span class="badge bg-secondary">Kurang Populer</span>';
    }

    html += `
      <tr>
        <td>
          <strong>${paket.nama_paket}</strong>
          <br><small class="text-muted">${formatPrice(paket.harga)} - ${paket.durasi}</small>
        </td>
        <td>${paket.nama_konsol || 'Tidak ada konsol'}</td>
        <td><span class="badge bg-primary">${paket.total_penggunaan} kali</span></td>
        <td>
          ${popularityBadge}
          <br><small class="text-muted">${popularityPercentage}% dari yang terpopuler</small>
        </td>
      </tr>
    `;
  });

  tbody.innerHTML = html;
}

// Render topup chart
function renderTopupChart(data) {
  const ctx = document.getElementById('chartTopup');
  if (!ctx) {
    console.error('Chart topup canvas not found');
    return;
  }

  if (chartTopup) {
    chartTopup.destroy();
  }

  // Handle empty data
  if (!data || !data.chart_data) {
    console.log('No topup chart data available');
    return;
  }

  const chartData = data.chart_data;
  let chartTitle = 'Trend Top-up Member';

  // Set title based on periode type
  switch(data.periode_type) {
    case 'harian':
      chartTitle = 'Top-up per Jam (24 Jam)';
      break;
    case 'bulanan':
      chartTitle = `Top-up per Tanggal (${data.bulan_info?.days_in_month || 31} Hari)`;
      break;
    case 'tahunan':
      chartTitle = `Top-up per Bulan (${data.tahun || 'Tahun'})`;
      break;
  }

  chartTopup = new Chart(ctx.getContext('2d'), {
    type: 'line',
    data: {
      labels: chartData.labels,
      datasets: [
        {
          label: 'Jumlah Transaksi',
          data: chartData.transaksi,
          backgroundColor: colors.primary[0] + '20',
          borderColor: colors.primary[0],
          borderWidth: 2,
          fill: true,
          yAxisID: 'y'
        },
        {
          label: 'Total Top-up (Rp)',
          data: chartData.topup,
          backgroundColor: colors.success[0] + '20',
          borderColor: colors.success[0],
          borderWidth: 2,
          fill: true,
          yAxisID: 'y1'
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      interaction: {
        mode: 'index',
        intersect: false,
      },
      scales: {
        x: {
          display: true,
          title: {
            display: true,
            text: data.periode_type === 'harian' ? 'Jam' :
                  data.periode_type === 'bulanan' ? 'Tanggal' : 'Bulan'
          }
        },
        y: {
          type: 'linear',
          display: true,
          position: 'left',
          title: {
            display: true,
            text: 'Jumlah Transaksi'
          }
        },
        y1: {
          type: 'linear',
          display: true,
          position: 'right',
          title: {
            display: true,
            text: 'Total Top-up (Rp)'
          },
          grid: {
            drawOnChartArea: false,
          },
        }
      },
      plugins: {
        legend: {
          display: true,
          position: 'top'
        },
        title: {
          display: true,
          text: chartTitle
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              let label = context.dataset.label || '';
              if (label) {
                label += ': ';
              }
              if (context.datasetIndex === 1) {
                label += formatPrice(context.parsed.y);
              } else {
                label += context.parsed.y;
              }
              return label;
            }
          }
        }
      }
    }
  });
}

// Render topup stats
function renderTopupStats(data) {
  document.getElementById('totalTopupAmount').textContent = formatPrice(data.total_topup);
  document.getElementById('totalTopupTransaksi').textContent = data.total_transaksi;

  const avgAmount = data.total_transaksi > 0 ? data.total_topup / data.total_transaksi : 0;
  document.getElementById('avgTopupAmount').textContent = formatPrice(avgAmount);
}

// Render pendapatan chart
function renderPendapatanLineChart(data) {
  const ctx = document.getElementById('chartPendapatan');
  if (!ctx) {
    console.error('Chart pendapatan canvas not found');
    return;
  }

  if (chartPendapatan) {
    chartPendapatan.destroy();
  }

  // Handle empty data
  if (!data || !data.chart_data) {
    console.log('No pendapatan chart data available');
    return;
  }

  const chartData = data.chart_data;
  let chartTitle = 'Trend Pendapatan';

  // Set title based on periode type
  switch(data.periode_type) {
    case 'harian':
      chartTitle = 'Pendapatan per Jam (24 Jam)';
      break;
    case 'bulanan':
      chartTitle = `Pendapatan per Tanggal (${data.bulan_info?.days_in_month || 31} Hari)`;
      break;
    case 'tahunan':
      chartTitle = `Pendapatan per Bulan (${data.tahun || 'Tahun'})`;
      break;
  }

  chartPendapatan = new Chart(ctx.getContext('2d'), {
    type: 'line',
    data: {
      labels: chartData.labels,
      datasets: [
        {
          label: 'Sesi Gaming',
          data: chartData.sesi,
          backgroundColor: colors.primary[0] + '20',
          borderColor: colors.primary[0],
          borderWidth: 2,
          fill: true
        },
        {
          label: 'Top-up',
          data: chartData.topup,
          backgroundColor: colors.success[0] + '20',
          borderColor: colors.success[0],
          borderWidth: 2,
          fill: true
        },
        {
          label: 'Produk',
          data: chartData.produk,
          backgroundColor: colors.warning[0] + '20',
          borderColor: colors.warning[0],
          borderWidth: 2,
          fill: true
        },
        {
          label: 'Total',
          data: chartData.total,
          backgroundColor: colors.info[0] + '40',
          borderColor: colors.info[0],
          borderWidth: 3,
          fill: false,
          borderDash: [5, 5]
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      interaction: {
        mode: 'index',
        intersect: false,
      },
      scales: {
        x: {
          display: true,
          title: {
            display: true,
            text: data.periode_type === 'harian' ? 'Jam' :
                  data.periode_type === 'bulanan' ? 'Tanggal' : 'Bulan'
          }
        },
        y: {
          display: true,
          title: {
            display: true,
            text: 'Pendapatan (Rp)'
          },
          ticks: {
            callback: function(value) {
              return formatPrice(value);
            }
          }
        }
      },
      plugins: {
        legend: {
          display: true,
          position: 'top'
        },
        title: {
          display: true,
          text: chartTitle
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              let label = context.dataset.label || '';
              if (label) {
                label += ': ';
              }
              label += formatPrice(context.parsed.y);
              return label;
            }
          }
        }
      }
    }
  });

  // Source pie chart
  const ctxSumber = document.getElementById('chartPendapatanSumber').getContext('2d');

  if (chartPendapatanSumber) {
    chartPendapatanSumber.destroy();
  }

  chartPendapatanSumber = new Chart(ctxSumber, {
    type: 'doughnut',
    data: {
      labels: ['Sesi Gaming', 'Top-up', 'Produk'],
      datasets: [{
        data: [data.pendapatan_sesi, data.pendapatan_topup, data.pendapatan_produk],
        backgroundColor: [colors.primary[0], colors.success[0], colors.warning[0]],
        borderWidth: 2,
        borderColor: '#fff'
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: true,
          position: 'bottom'
        },
        title: {
          display: true,
          text: 'Sumber Pendapatan'
        }
      }
    }
  });
}

// Render pendapatan stats
function renderPendapatanStats(data) {
  document.getElementById('pendapatanSesi').textContent = formatPrice(data.pendapatan_sesi);
  document.getElementById('pendapatanTopup').textContent = formatPrice(data.pendapatan_topup);
  document.getElementById('pendapatanProduk').textContent = formatPrice(data.pendapatan_produk);

  // Calculate and display total pendapatan
  const totalPendapatan = (data.pendapatan_sesi || 0) + (data.pendapatan_topup || 0) + (data.pendapatan_produk || 0);
  document.getElementById('totalPendapatan').textContent = formatPrice(totalPendapatan);
}

// Render produk bar chart
function renderProdukBarChart(data) {
  const ctx = document.getElementById('chartProduk');
  if (!ctx) {
    console.error('Chart canvas not found');
    return;
  }

  if (chartProduk) {
    chartProduk.destroy();
  }

  // Handle empty data
  if (!data || !data.detail || data.detail.length === 0) {
    console.log('No product data to render');
    ctx.getContext('2d').clearRect(0, 0, ctx.width, ctx.height);
    return;
  }

  // Sort by quantity and take top 10
  const sortedProducts = data.detail.sort((a, b) => b.total_quantity - a.total_quantity).slice(0, 10);

  chartProduk = new Chart(ctx.getContext('2d'), {
    type: 'bar',
    data: {
      labels: sortedProducts.map(item => item.nama_produk),
      datasets: [
        {
          label: 'Quantity Terjual',
          data: sortedProducts.map(item => item.total_quantity),
          backgroundColor: colors.primary[0],
          borderColor: colors.primary[0],
          borderWidth: 1,
          yAxisID: 'y'
        },
        {
          label: 'Pendapatan (Rp)',
          data: sortedProducts.map(item => item.total_pendapatan),
          backgroundColor: colors.success[0],
          borderColor: colors.success[0],
          borderWidth: 1,
          yAxisID: 'y1'
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          type: 'linear',
          display: true,
          position: 'left',
          title: {
            display: true,
            text: 'Quantity'
          }
        },
        y1: {
          type: 'linear',
          display: true,
          position: 'right',
          title: {
            display: true,
            text: 'Pendapatan (Rp)'
          },
          grid: {
            drawOnChartArea: false,
          },
        }
      },
      plugins: {
        legend: {
          display: true,
          position: 'top'
        },
        title: {
          display: true,
          text: 'Top 10 Produk Terlaris'
        }
      }
    }
  });
}

// Render produk terlaris list
function renderProdukTerlaris(produkTerlaris) {
  const container = document.getElementById('produkTerlaris');
  if (!container) {
    console.error('Produk terlaris container not found');
    return;
  }

  if (!produkTerlaris || produkTerlaris.length === 0) {
    container.innerHTML = `
      <div class="text-center text-muted">
        <i class="bi bi-info-circle"></i>
        <p>Belum ada data penjualan produk hari ini</p>
      </div>
    `;
    return;
  }

  let html = '<div class="list-group list-group-flush">';

  produkTerlaris.slice(0, 5).forEach((produk, index) => {
    let badgeClass = '';
    let icon = '';

    switch(index) {
      case 0:
        badgeClass = 'bg-warning';
        icon = '<i class="bi bi-trophy-fill"></i>';
        break;
      case 1:
        badgeClass = 'bg-secondary';
        icon = '<i class="bi bi-award-fill"></i>';
        break;
      case 2:
        badgeClass = 'bg-info';
        icon = '<i class="bi bi-star-fill"></i>';
        break;
      default:
        badgeClass = 'bg-light text-dark';
        icon = '<i class="bi bi-star"></i>';
    }

    html += `
      <div class="list-group-item border-0 px-0">
        <div class="d-flex justify-content-between align-items-start">
          <div class="flex-grow-1">
            <div class="d-flex align-items-center mb-1">
              <span class="badge ${badgeClass} me-2">${icon} #${index + 1}</span>
              <strong class="mb-0">${produk.nama_produk}</strong>
            </div>
            <small class="text-muted">${produk.kategori || 'Tidak diketahui'}</small>
            <div class="mt-1">
              <span class="badge bg-primary me-1">${produk.total_quantity} terjual</span>
              <span class="badge bg-success">${formatPrice(produk.total_pendapatan)}</span>
            </div>
          </div>
        </div>
      </div>
    `;
  });

  html += '</div>';

  // Add summary
  const totalQuantity = produkTerlaris.reduce((sum, p) => sum + p.total_quantity, 0);
  const totalRevenue = produkTerlaris.reduce((sum, p) => sum + p.total_pendapatan, 0);

  html += `
    <div class="mt-3 p-2 bg-light rounded">
      <small class="text-muted">
        <strong>Total:</strong> ${totalQuantity} item terjual, ${formatPrice(totalRevenue)} pendapatan
      </small>
    </div>
  `;

  container.innerHTML = html;
}

// Export functions
function exportAllReports() {
  Swal.fire('Info', 'Fitur export akan segera tersedia', 'info');
}

</script>
<?= $this->endSection() ?>
