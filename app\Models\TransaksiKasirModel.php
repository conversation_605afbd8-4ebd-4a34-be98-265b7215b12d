<?php

namespace App\Models;

use CodeIgniter\Model;

class TransaksiKasirModel extends Model
{
    protected $table = 'transaksi_kasir';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'nomor_transaksi', 'tanggal_transaksi', 'total_item', 'total_harga',
        'metode_bayar', 'jumlah_bayar', 'kembalian', 'kasir', 'keterangan'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    /**
     * Generate nomor transaksi unik
     * Format: TRX-YYYYMMDD-XXXX
     */
    public function generateNomorTransaksi()
    {
        $date = date('Ymd');
        $prefix = "TRX-{$date}-";
        
        // Cari nomor terakhir hari ini
        $lastTransaction = $this->where('nomor_transaksi LIKE', $prefix . '%')
                                ->orderBy('nomor_transaksi', 'DESC')
                                ->first();
        
        if ($lastTransaction) {
            // Extract nomor urut dari nomor transaksi terakhir
            $lastNumber = (int) substr($lastTransaction['nomor_transaksi'], -4);
            $nextNumber = $lastNumber + 1;
        } else {
            $nextNumber = 1;
        }
        
        return $prefix . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Buat transaksi baru dengan detail
     */
    public function createTransactionWithDetails($transactionData, $detailItems)
    {
        $db = \Config\Database::connect();
        $db->transStart();

        try {
            // Generate nomor transaksi
            $transactionData['nomor_transaksi'] = $this->generateNomorTransaksi();
            $transactionData['tanggal_transaksi'] = date('Y-m-d H:i:s');
            
            // Insert transaksi header
            $transactionId = $this->insert($transactionData);
            
            if (!$transactionId) {
                throw new \Exception('Failed to create transaction');
            }

            // Insert detail items
            $detailModel = new TransaksiKasirDetailModel();
            foreach ($detailItems as $item) {
                $item['transaksi_kasir_id'] = $transactionId;
                $detailModel->insert($item);
            }

            $db->transComplete();

            if ($db->transStatus() === false) {
                throw new \Exception('Transaction failed');
            }

            return [
                'success' => true,
                'transaction_id' => $transactionId,
                'nomor_transaksi' => $transactionData['nomor_transaksi']
            ];

        } catch (\Exception $e) {
            $db->transRollback();
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Get transaksi dengan detail
     */
    public function getTransactionWithDetails($transactionId)
    {
        $transaction = $this->find($transactionId);
        if (!$transaction) {
            return null;
        }

        $detailModel = new TransaksiKasirDetailModel();
        $details = $detailModel->where('transaksi_kasir_id', $transactionId)->findAll();
        
        $transaction['details'] = $details;
        return $transaction;
    }

    /**
     * Get laporan transaksi berdasarkan tanggal
     */
    public function getLaporanByDate($startDate, $endDate = null)
    {
        if (!$endDate) {
            $endDate = $startDate;
        }

        return $this->where('DATE(tanggal_transaksi) >=', $startDate)
                   ->where('DATE(tanggal_transaksi) <=', $endDate)
                   ->orderBy('tanggal_transaksi', 'DESC')
                   ->findAll();
    }
}
