/*
 * Test Colors untuk ST7789 Display
 * Upload script ini untuk test warna RGB565
 */

#include <TFT_eSPI.h>

TFT_eSPI tft = TFT_eSPI();

// Test warna RGB565
#define TEST_BLACK        0x0000
#define TEST_WHITE        0xFFFF
#define TEST_RED          0xF800
#define TEST_GREEN        0x07E0
#define TEST_BLUE         0x001F
#define TEST_YELLOW       0xFFE0
#define TEST_CYAN         0x07FF
#define TEST_MAGENTA      0xF81F

// Card colors
#define TEST_CARD_GREEN   0x2589
#define TEST_CARD_BLUE    0x4D9F
#define TEST_CARD_GRAY    0x6B6D

void setup() {
    Serial.begin(115200);
    
    tft.init();
    tft.setRotation(2);
    tft.fillScreen(TEST_BLACK);
    
    Serial.println("=== COLOR TEST START ===");
    
    // Test basic colors
    testBasicColors();
    delay(3000);
    
    // Test card colors
    testCardColors();
    delay(3000);
    
    // Test color gradients
    testColorGradients();
    
    Serial.println("=== COLOR TEST COMPLETE ===");
}

void loop() {
    // Cycle through card colors
    testCardColorsCycle();
}

void testBasicColors() {
    tft.fillScreen(TEST_BLACK);
    tft.setTextColor(TEST_WHITE);
    tft.setTextSize(2);
    
    // Test basic colors in grid
    int x = 0, y = 0;
    int w = 80, h = 60;
    
    // Row 1
    tft.fillRect(x, y, w, h, TEST_RED);
    tft.setCursor(x+10, y+20);
    tft.print("RED");
    
    tft.fillRect(x+w, y, w, h, TEST_GREEN);
    tft.setCursor(x+w+10, y+20);
    tft.setTextColor(TEST_BLACK);
    tft.print("GREEN");
    
    tft.fillRect(x+2*w, y, w, h, TEST_BLUE);
    tft.setCursor(x+2*w+10, y+20);
    tft.setTextColor(TEST_WHITE);
    tft.print("BLUE");
    
    // Row 2
    y += h;
    tft.fillRect(x, y, w, h, TEST_YELLOW);
    tft.setCursor(x+10, y+20);
    tft.setTextColor(TEST_BLACK);
    tft.print("YELLOW");
    
    tft.fillRect(x+w, y, w, h, TEST_CYAN);
    tft.setCursor(x+w+10, y+20);
    tft.setTextColor(TEST_BLACK);
    tft.print("CYAN");
    
    tft.fillRect(x+2*w, y, w, h, TEST_MAGENTA);
    tft.setCursor(x+2*w+10, y+20);
    tft.setTextColor(TEST_WHITE);
    tft.print("MAGENTA");
    
    // Row 3
    y += h;
    tft.fillRect(x, y, 3*w, h, TEST_WHITE);
    tft.setCursor(x+10, y+20);
    tft.setTextColor(TEST_BLACK);
    tft.print("WHITE BACKGROUND");
    
    // Row 4
    y += h;
    tft.fillRect(x, y, 3*w, h, TEST_BLACK);
    tft.setCursor(x+10, y+20);
    tft.setTextColor(TEST_WHITE);
    tft.print("BLACK BACKGROUND");
    
    Serial.println("Basic colors test displayed");
}

void testCardColors() {
    tft.fillScreen(TEST_BLACK);
    tft.setTextSize(2);
    
    int y = 20;
    int h = 60;
    
    // Card Green (Active)
    tft.fillRoundRect(10, y, 220, h, 10, TEST_CARD_GREEN);
    tft.setTextColor(TEST_WHITE);
    tft.setCursor(20, y+20);
    tft.print("ACTIVE (GREEN)");
    
    y += h + 10;
    
    // Card Blue (Paused)
    tft.fillRoundRect(10, y, 220, h, 10, TEST_CARD_BLUE);
    tft.setTextColor(TEST_WHITE);
    tft.setCursor(20, y+20);
    tft.print("PAUSED (BLUE)");
    
    y += h + 10;
    
    // Card Gray (Standby)
    tft.fillRoundRect(10, y, 220, h, 10, TEST_CARD_GRAY);
    tft.setTextColor(TEST_WHITE);
    tft.setCursor(20, y+20);
    tft.print("STANDBY (GRAY)");
    
    Serial.println("Card colors test displayed");
}

void testColorGradients() {
    tft.fillScreen(TEST_BLACK);
    
    // Red gradient
    for(int i = 0; i < 240; i++) {
        uint16_t color = (i/8) << 11; // Red component
        tft.drawFastVLine(i, 0, 80, color);
    }
    
    // Green gradient
    for(int i = 0; i < 240; i++) {
        uint16_t color = (i/4) << 5; // Green component
        tft.drawFastVLine(i, 80, 80, color);
    }
    
    // Blue gradient
    for(int i = 0; i < 240; i++) {
        uint16_t color = i/8; // Blue component
        tft.drawFastVLine(i, 160, 80, color);
    }
    
    Serial.println("Color gradients test displayed");
}

void testCardColorsCycle() {
    static int state = 0;
    static unsigned long lastChange = 0;
    
    if (millis() - lastChange > 2000) {
        lastChange = millis();
        
        tft.fillScreen(TEST_BLACK);
        
        switch(state) {
            case 0:
                // Standby
                tft.fillRoundRect(10, 10, 220, 220, 15, TEST_CARD_GRAY);
                tft.setTextColor(TEST_WHITE);
                tft.setTextSize(3);
                tft.setCursor(50, 100);
                tft.print("STANDBY");
                Serial.println("Displaying: STANDBY (Gray)");
                break;
                
            case 1:
                // Active
                tft.fillRoundRect(10, 10, 220, 220, 15, TEST_CARD_GREEN);
                tft.setTextColor(TEST_WHITE);
                tft.setTextSize(3);
                tft.setCursor(60, 100);
                tft.print("ACTIVE");
                Serial.println("Displaying: ACTIVE (Green)");
                break;
                
            case 2:
                // Paused
                tft.fillRoundRect(10, 10, 220, 220, 15, TEST_CARD_BLUE);
                tft.setTextColor(TEST_WHITE);
                tft.setTextSize(3);
                tft.setCursor(60, 100);
                tft.print("PAUSED");
                Serial.println("Displaying: PAUSED (Blue)");
                break;
        }
        
        state = (state + 1) % 3;
    }
}
