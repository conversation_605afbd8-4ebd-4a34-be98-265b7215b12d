<?php namespace App\Models;
use CodeIgniter\Model;

class PsTypeModel extends Model
{
    protected $table = 'ps_types';
    protected $primaryKey = 'id';
    protected $allowedFields = ['name', 'price_per_hour', 'description'];
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
}