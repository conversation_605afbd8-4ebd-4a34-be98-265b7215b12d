<?php
/**
 * Debug script untuk testing timer logic
 * Simulasi berbagai kondisi member dan personal
 */

// Simulasi data sesi aktif
$testCases = [
    [
        'name' => 'Personal User',
        'id_member' => null,
        'id_paket' => null,
        'waktu_mulai' => '2025-01-20 10:00:00',
        'durasi_sisa' => null,
        'saldo' => 0,
        'harga_member' => 5000
    ],
    [
        'name' => 'Member den<PERSON> (60 menit sisa)',
        'id_member' => 1,
        'id_paket' => 1,
        'waktu_mulai' => '2025-01-20 10:00:00',
        'durasi_sisa' => 60,
        'saldo' => 50000,
        'harga_member' => 5000
    ],
    [
        'name' => 'Member dengan <PERSON> (30 menit sisa)',
        'id_member' => 1,
        'id_paket' => 1,
        'waktu_mulai' => '2025-01-20 10:00:00',
        'durasi_sisa' => 30,
        'saldo' => 50000,
        'harga_member' => 5000
    ],
    [
        'name' => 'Member tanpa <PERSON>et (saldo 25000)',
        'id_member' => 1,
        'id_paket' => null,
        'waktu_mulai' => '2025-01-20 10:00:00',
        'durasi_sisa' => null,
        'saldo' => 25000,
        'harga_member' => 5000
    ],
    [
        'name' => 'Member tanpa Paket (saldo 10000)',
        'id_member' => 1,
        'id_paket' => null,
        'waktu_mulai' => '2025-01-20 10:00:00',
        'durasi_sisa' => null,
        'saldo' => 10000,
        'harga_member' => 5000
    ]
];

echo "=== DEBUG TIMER LOGIC ===\n\n";

foreach ($testCases as $case) {
    echo "Test Case: " . $case['name'] . "\n";
    echo "----------------------------------------\n";
    
    $startTime = new DateTime($case['waktu_mulai']);
    $currentTime = new DateTime(); // Waktu sekarang
    $interval = $startTime->diff($currentTime);
    
    $playerName = 'Personal';
    $isPersonal = true;
    $timer = sprintf('%02d:%02d:%02d', 
        $interval->h, 
        $interval->i, 
        $interval->s
    );
    
    echo "Waktu mulai: " . $case['waktu_mulai'] . "\n";
    echo "Waktu sekarang: " . $currentTime->format('Y-m-d H:i:s') . "\n";
    echo "Waktu berjalan: " . $timer . "\n";
    
    if ($case['id_member']) {
        $playerName = 'Member Test';
        $isPersonal = false;
        
        if ($case['id_paket'] && $case['durasi_sisa'] > 0) {
            // Member dengan paket - countdown dari durasi_sisa
            $remainingMinutes = $case['durasi_sisa'];
            $hours = floor($remainingMinutes / 60);
            $minutes = $remainingMinutes % 60;
            $timer = sprintf('%02d:%02d:00', $hours, $minutes);
            
            echo "Tipe: Member dengan Paket\n";
            echo "Durasi sisa: " . $case['durasi_sisa'] . " menit\n";
            echo "Timer (countdown): " . $timer . "\n";
        } else {
            // Member tanpa paket - countdown dari saldo
            $hargaPerJam = $case['harga_member'];
            $sisaSaldo = $case['saldo'];
            $sisaWaktuMenit = floor(($sisaSaldo / $hargaPerJam) * 60);
            
            // Kurangi waktu yang sudah berjalan
            $waktuBerjalanMenit = ($interval->h * 60) + $interval->i;
            $sisaWaktuMenit = max(0, $sisaWaktuMenit - $waktuBerjalanMenit);
            
            $hours = floor($sisaWaktuMenit / 60);
            $minutes = $sisaWaktuMenit % 60;
            $timer = sprintf('%02d:%02d:00', $hours, $minutes);
            
            echo "Tipe: Member tanpa Paket\n";
            echo "Saldo: Rp " . number_format($case['saldo']) . "\n";
            echo "Harga per jam: Rp " . number_format($hargaPerJam) . "\n";
            echo "Maksimal waktu: " . floor(($sisaSaldo / $hargaPerJam) * 60) . " menit\n";
            echo "Waktu berjalan: " . $waktuBerjalanMenit . " menit\n";
            echo "Sisa waktu: " . $sisaWaktuMenit . " menit\n";
            echo "Timer (countdown): " . $timer . "\n";
        }
    } else {
        echo "Tipe: Personal\n";
        echo "Timer (count-up): " . $timer . "\n";
    }
    
    echo "Player: " . $playerName . "\n";
    echo "Is Personal: " . ($isPersonal ? 'true' : 'false') . "\n";
    echo "\n";
}

echo "=== EXPECTED BEHAVIOR ===\n";
echo "1. Personal: Count-up timer (00:00:00 naik)\n";
echo "2. Member dengan Paket: Countdown dari durasi_sisa\n";
echo "3. Member tanpa Paket: Countdown dari saldo/harga_per_jam\n";
echo "4. Semua countdown harus turun setiap detik\n";
echo "5. Jika countdown mencapai 00:00:00, sesi harus berhenti\n\n";

echo "=== TESTING API ===\n";
echo "Test dengan: curl -X GET 'http://*************/playsphere/api/channel/status/[station_id]'\n";
echo "Periksa response JSON untuk field:\n";
echo "- timer: format HH:MM:SS\n";
echo "- isPersonal: true/false\n";
echo "- playerName: nama player\n";
echo "- status: in_use/available\n";
?>
