/* ===== SETTING PAGE STYLES ===== */

/* Setting Grid Layout */
.setting-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

/* Setting Card */
.setting-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 1rem;
  position: relative;
  overflow: hidden;
}

.setting-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
  border-color: #667eea;
}

.setting-card:active {
  transform: translateY(0);
}

/* Setting Icon */
.setting-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  flex-shrink: 0;
}

/* Setting Content */
.setting-content {
  flex: 1;
}

.setting-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.5rem;
}

.setting-description {
  color: #718096;
  font-size: 0.9rem;
  margin-bottom: 0.75rem;
  line-height: 1.4;
}

.setting-details {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.setting-details small {
  background: #f7fafc;
  color: #4a5568;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  border: 1px solid #e2e8f0;
}

/* Setting Arrow */
.setting-arrow {
  color: #a0aec0;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.setting-card:hover .setting-arrow {
  color: #667eea;
  transform: translateX(4px);
}

/* Icon Colors for Different Settings */
.setting-card:nth-child(1) .setting-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.setting-card:nth-child(2) .setting-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.setting-card:nth-child(3) .setting-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.setting-card:nth-child(4) .setting-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.setting-card:nth-child(5) .setting-icon {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.setting-card:nth-child(6) .setting-icon {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .setting-grid {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 1.25rem;
  }
}

@media (max-width: 768px) {
  .setting-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .setting-card {
    padding: 1.25rem;
  }
  
  .setting-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }
  
  .setting-title {
    font-size: 1rem;
  }
  
  .setting-description {
    font-size: 0.85rem;
  }
  
  .setting-details small {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
  }
}

@media (max-width: 576px) {
  .setting-card {
    padding: 1rem;
    gap: 0.75rem;
  }
  
  .setting-icon {
    width: 45px;
    height: 45px;
    font-size: 1.1rem;
  }
  
  .setting-title {
    font-size: 0.95rem;
  }
  
  .setting-description {
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
  }
  
  .setting-details {
    gap: 0.25rem;
  }
  
  .setting-details small {
    font-size: 0.65rem;
    padding: 0.15rem 0.3rem;
  }
}

/* Loading Animation */
.setting-card.loading {
  pointer-events: none;
  opacity: 0.7;
}

.setting-card.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Hover Effects */
.setting-card:hover .setting-icon {
  transform: scale(1.05);
}

.setting-card:hover .setting-title {
  color: #667eea;
}

.setting-card:hover .setting-details small {
  background: #edf2f7;
  border-color: #cbd5e0;
}

/* ===== SETTING RENTAL MODAL ===== */

/* Modal Header Gradient */
.bg-gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

/* Setting Sections */
.setting-section {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e9ecef;
}

.setting-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.section-header {
  margin-bottom: 1.5rem;
}

.section-title {
  color: #2d3748;
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
}

.section-description {
  color: #718096;
  font-size: 0.9rem;
  margin-bottom: 0;
}

/* Logo Upload Area */
.logo-upload-area {
  position: relative;
  width: 100%;
  height: 200px;
  border: 2px dashed #cbd5e0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.logo-upload-area:hover {
  border-color: #667eea;
  background-color: #f7fafc;
}

.logo-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.logo-preview img {
  max-width: 80%;
  max-height: 80%;
  object-fit: contain;
  border-radius: 8px;
}

.upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
}

.logo-upload-area:hover .upload-overlay {
  opacity: 1;
}

.upload-overlay i {
  font-size: 2rem;
  color: #667eea;
  margin-bottom: 0.5rem;
}

.upload-overlay p {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.25rem;
}

.upload-overlay small {
  color: #718096;
  font-size: 0.8rem;
}

/* Logo Info */
.logo-info {
  padding: 1rem;
  background: #f7fafc;
  border-radius: 8px;
  height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.logo-info h6 {
  color: #2d3748;
  font-weight: 600;
  margin-bottom: 1rem;
}

.logo-requirements {
  list-style: none;
  padding: 0;
  margin: 0;
}

.logo-requirements li {
  padding: 0.25rem 0;
  font-size: 0.9rem;
  color: #4a5568;
}

/* Form Styling */
.form-label {
  font-weight: 600;
  color: #2d3748;
  display: flex;
  align-items: center;
}

.form-control:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Preview Cards */
.preview-card {
  background: #f7fafc;
  border-radius: 12px;
  padding: 1rem;
  height: 180px;
}

.preview-title {
  color: #2d3748;
  font-weight: 600;
  margin-bottom: 1rem;
  text-align: center;
}

/* Header Preview */
.header-preview {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-logo img {
  width: 40px;
  height: 40px;
  object-fit: contain;
}

.header-text span {
  font-weight: 600;
  color: #2d3748;
  font-size: 1.1rem;
}

/* Receipt Preview */
.receipt-preview {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  font-family: 'Courier New', monospace;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.receipt-header {
  text-align: center;
  border-bottom: 1px solid #e2e8f0;
  padding-bottom: 0.75rem;
}

.receipt-logo img {
  width: 30px;
  height: 30px;
  object-fit: contain;
  margin-bottom: 0.5rem;
}

.receipt-name {
  font-weight: bold;
  font-size: 0.9rem;
  color: #2d3748;
  margin-bottom: 0.25rem;
}

.receipt-slogan {
  font-size: 0.7rem;
  color: #4a5568;
  font-style: italic;
  margin-bottom: 0.25rem;
}

.receipt-address {
  font-size: 0.75rem;
  color: #4a5568;
  margin-bottom: 0.25rem;
}

.receipt-phone {
  font-size: 0.75rem;
  color: #4a5568;
}

/* Dark Mode Support (Future) */
@media (prefers-color-scheme: dark) {
  .setting-card {
    background: #2d3748;
    border-color: #4a5568;
    color: white;
  }

  .setting-title {
    color: #f7fafc;
  }

  .setting-description {
    color: #a0aec0;
  }

  .setting-details small {
    background: #4a5568;
    color: #e2e8f0;
    border-color: #718096;
  }
}
