<?php
/**
 * Real-time timer test untuk debugging countdown
 * Simulasi API call setiap detik untuk melihat perubahan timer
 */

$baseUrl = 'http://192.168.1.200/playsphere';
$stationId = 10; // Ganti dengan station ID yang sesuai

echo "=== REAL-TIME TIMER TEST ===\n";
echo "Station ID: $stationId\n";
echo "URL: $baseUrl/api/channel/status/$stationId\n";
echo "Press Ctrl+C to stop\n\n";

$lastTimer = '';
$count = 0;

while (true) {
    $count++;
    echo "[$count] " . date('H:i:s') . " - ";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "$baseUrl/api/channel/status/$stationId");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode == 200) {
        $data = json_decode($response, true);
        
        if ($data && $data['success']) {
            $sessionData = $data['data'];
            $status = $sessionData['status'];
            $timer = $sessionData['timer'];
            $playerName = $sessionData['playerName'];
            $isPersonal = $sessionData['isPersonal'] ? 'Personal' : 'Member';
            
            echo "Status: $status | Timer: $timer | Player: $playerName ($isPersonal)";
            
            // Highlight jika timer berubah
            if ($timer != $lastTimer) {
                echo " [CHANGED]";
                $lastTimer = $timer;
            }
            
            // Alert jika countdown mencapai 00:00:00
            if (!$sessionData['isPersonal'] && $timer == '00:00:00') {
                echo " [TIME'S UP!]";
            }
            
            echo "\n";
            
            // Tampilkan detail lengkap setiap 10 detik
            if ($count % 10 == 0) {
                echo "--- Detail Response ---\n";
                echo json_encode($sessionData, JSON_PRETTY_PRINT) . "\n";
                echo "----------------------\n";
            }
            
        } else {
            echo "API Error: " . ($data['message'] ?? 'Unknown error') . "\n";
        }
    } else {
        echo "HTTP Error: $httpCode\n";
    }
    
    sleep(1); // Update setiap detik
}
?>
