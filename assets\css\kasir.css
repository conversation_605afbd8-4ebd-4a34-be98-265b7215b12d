/**
 * Kasir CSS
 * Styling khusus untuk halaman kasir dan Point of Sale (POS)
 * Desain modern dengan fokus pada kemudahan penggunaan
 * 
 * <AUTHOR> Team
 * @version 3.0
 */

/* ===== KASIR LAYOUT ===== */
.kasir-container {
  width: 100%;
  height: calc(100vh - 140px);
  padding: 0;
  margin: 0;
}

/* Panel utama untuk semua transaksi */
.kasir-main {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

/* ===== HEADER SECTIONS ===== */
.kasir-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.kasir-header i {
  font-size: 1.1rem;
  margin-right: 0.5rem;
}

.kasir-header-title {
  font-size: 1.1rem;
}

/* ===== TAB NAVIGATION ===== */
.kasir-tabs {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.kasir-tab {
  flex: 1;
  padding: 1rem;
  text-align: center;
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  color: #6c757d;
}

.kasir-tab.active {
  background: white;
  color: #495057;
  border-bottom: 3px solid #667eea;
}

.kasir-tab:hover {
  background: #e9ecef;
}

/* ===== CONTENT AREAS ===== */
.kasir-content {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  min-height: 0; /* Allow flex child to shrink */
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

/* ===== UNPAID SESSIONS - ULTRA COMPACT ===== */
.unpaid-sessions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 0.5rem;
  margin-top: 1rem;
}

.unpaid-session {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 6px;
  padding: 0.5rem;
  transition: all 0.3s ease;
  position: relative;
}

.unpaid-session:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.session-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.3rem;
}

.session-details {
  flex: 1;
}

.session-station {
  font-weight: 600;
  color: #856404;
  font-size: 0.8rem;
  line-height: 1.2;
  margin-bottom: 0.1rem;
}

.session-price {
  font-size: 0.9rem;
  font-weight: 700;
  color: #d63384;
  margin-bottom: 0.1rem;
}

.session-time {
  font-size: 0.7rem;
  color: #6c757d;
  margin-bottom: 0.3rem;
}

.session-cart-btn {
  background: #28a745;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.session-cart-btn:hover {
  background: #218838;
  transform: scale(1.1);
  color: white;
}

/* ===== MEMBER TABLE ===== */
/* Moved to MEMBER TABLE RESPONSIVE section to avoid conflicts */

.member-table {
  width: 100%;
  background: white;
}

.member-table th {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
  padding: 1rem;
  text-align: left;
  border: none;
}

.member-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e9ecef;
  vertical-align: middle;
}

.member-table tbody tr:hover {
  background-color: #f8f9fa;
}

.member-table tbody tr:last-child td {
  border-bottom: none;
}

.member-search-container {
  position: relative;
  max-width: 400px;
}

.member-search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 3rem;
  border: 2px solid #e9ecef;
  border-radius: 25px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.member-search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.member-search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  font-size: 1.1rem;
  z-index: 2;
}

/* Mobile responsive search */
@media (max-width: 768px) {
  .member-search-container {
    max-width: 100%;
    margin-bottom: 0.5rem;
  }

  .member-search-input {
    font-size: 0.9rem;
    padding: 0.6rem 0.8rem 0.6rem 2.5rem;
  }

  .member-search-icon {
    left: 0.8rem;
    font-size: 1rem;
  }
}

/* ===== PRODUCT GRID ===== */
.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.product-card {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.product-card:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.product-name {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #495057;
}

.product-price {
  font-size: 1.1rem;
  font-weight: 700;
  color: #28a745;
  margin-bottom: 0.5rem;
}

.product-stock {
  font-size: 0.9rem;
  color: #6c757d;
}

/* ===== FLOATING CART BUTTON ===== */
.floating-cart {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1000;
}

.cart-btn {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  border-radius: 50px;
  padding: 15px 25px;
  font-size: 1.1rem;
  font-weight: 600;
  box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 140px;
  justify-content: center;
}

.cart-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(40, 167, 69, 0.5);
  background: linear-gradient(135deg, #20c997 0%, #28a745 100%);
}

.cart-btn:active {
  transform: translateY(-1px);
}

.cart-badge {
  background: #dc3545;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: bold;
  margin-left: 5px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
  }
}

/* ===== CART MODAL STYLES ===== */
.cart-modal .modal-dialog {
  max-width: 600px;
}

.cart-items {
  max-height: 400px;
  overflow-y: auto;
  padding: 1rem;
}

.cart-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #e9ecef;
}

.cart-item:last-child {
  border-bottom: none;
}

.item-info {
  flex: 1;
}

.item-name {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.25rem;
}

.item-price {
  color: #6c757d;
  font-size: 0.9rem;
}

.item-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.qty-btn {
  width: 30px;
  height: 30px;
  border: 1px solid #dee2e6;
  background: white;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.qty-btn:hover {
  background: #f8f9fa;
  border-color: #adb5bd;
}

.qty-input {
  width: 50px;
  text-align: center;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 0.25rem;
}

.cart-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  font-size: 1.2rem;
  font-weight: 700;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.total-label {
  color: #495057;
}

.total-amount {
  color: #dc3545;
}

/* ===== SEARCH BAR ===== */
.search-container {
  position: relative;
  margin-bottom: 1rem;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 3rem;
  border: 2px solid #e9ecef;
  border-radius: 25px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  font-size: 1.1rem;
}

/* ===== BARCODE SCANNER ===== */
.barcode-container {
  background: #e3f2fd;
  border: 2px dashed #2196f3;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  margin-bottom: 1rem;
}

.barcode-icon {
  font-size: 3rem;
  color: #2196f3;
  margin-bottom: 1rem;
}

.barcode-text {
  color: #1976d2;
  font-weight: 600;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .unpaid-sessions-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }

  .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  }
}

@media (max-width: 992px) {
  .unpaid-sessions-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }

  .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }

  .floating-cart {
    bottom: 20px;
    right: 20px;
  }

  .cart-btn {
    padding: 12px 20px;
    font-size: 1rem;
    min-width: 120px;
  }
}

@media (max-width: 576px) {
  .kasir-content {
    padding: 1rem;
  }

  .unpaid-sessions-grid {
    grid-template-columns: 1fr;
  }

  .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 0.75rem;
  }

  .product-card {
    padding: 0.75rem;
  }

  .floating-cart {
    bottom: 15px;
    right: 15px;
  }

  .cart-btn {
    padding: 10px 16px;
    font-size: 0.9rem;
    min-width: 100px;
  }

  .member-table th,
  .member-table td {
    padding: 0.5rem;
    font-size: 0.9rem;
  }
}

/* ===== ANIMATIONS ===== */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.cart-item {
  animation: slideIn 0.3s ease;
}

/* ===== UTILITY CLASSES ===== */
.text-success {
  color: #28a745 !important;
}

.text-danger {
  color: #dc3545 !important;
}

.text-warning {
  color: #ffc107 !important;
}

.badge-stock {
  background: #28a745;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
}

.badge-stock-low {
  background: #ffc107;
  color: #212529;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
}

.badge-stock-empty {
  background: #dc3545;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
}

/* Produk disabled (stok habis) */
.product-disabled {
  opacity: 0.5;
  cursor: not-allowed !important;
  pointer-events: none;
}

.product-disabled:hover {
  transform: none !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Kategori produk */
.product-category {
  position: absolute;
  top: 8px;
  left: 8px;
  background-color: rgba(102, 126, 234, 0.9);
  color: white;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 0.65rem;
  font-weight: 500;
  text-transform: uppercase;
}

/* ===== CASH PAYMENT FIELDS ===== */
#cashPaymentFields {
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 1rem;
  background-color: #f8f9fa;
}

#cashPaymentFields.d-none {
  display: none !important;
}

/* ===== STRUK STYLING - THERMAL 58MM ===== */
.struk-container {
  font-family: 'Courier New', monospace;
  background: white;
  padding: 20px; /* Increased padding for better preview */
  border: 2px solid #333;
  border-radius: 8px;
  max-width: 340px; /* Slightly wider for better readability */
  margin: 0 auto;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  position: relative;
  font-size: 12px; /* Base font size increased */
}

.struk-container::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  height: 8px;
  background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
              linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
              linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
              linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
  background-size: 8px 8px;
  background-position: 0 0, 0 4px, 4px -4px, -4px 0px;
  border-radius: 8px 8px 0 0;
}

.struk-header {
  text-align: center;
  border-bottom: 2px solid #000;
  padding-bottom: 8px;
  margin-bottom: 12px;
}

.struk-logo {
  margin-bottom: 8px;
}

.struk-logo img {
  width: 40px;
  height: 40px;
  object-fit: contain;
  border-radius: 4px;
}

.struk-header h3 {
  margin: 0 0 5px 0;
  font-size: 18px; /* Increased from 16px */
  font-weight: bold;
  letter-spacing: 1px;
}

.struk-header .subtitle {
  font-size: 12px; /* Increased from 10px */
  margin: 2px 0;
  color: #555;
}

.struk-info {
  margin-bottom: 12px;
  font-size: 13px; /* Increased from 11px */
  line-height: 1.3;
}

.struk-info div {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1px;
}

.struk-separator {
  text-align: center;
  margin: 8px 0;
  font-size: 13px; /* Increased from 11px */
  color: #666;
}

.struk-items {
  border-top: 1px dashed #000;
  border-bottom: 1px dashed #000;
  padding: 8px 0;
  margin: 12px 0;
}

.struk-item {
  margin-bottom: 6px;
  font-size: 13px; /* Increased from 11px */
  line-height: 1.2;
}

.struk-item-name {
  font-weight: bold;
  margin-bottom: 1px;
  font-size: 13px;
}

.struk-item-detail {
  display: flex;
  justify-content: space-between;
  font-size: 12px; /* Increased from 10px */
  color: #555;
  margin-left: 5px;
}

.struk-item-price {
  display: flex;
  justify-content: space-between;
  font-weight: bold;
  margin-top: 2px;
}

.struk-summary {
  border-top: 1px solid #000;
  padding-top: 8px;
  margin-top: 8px;
  font-size: 13px; /* Increased from 11px */
}

.struk-summary div {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2px;
}

.struk-total {
  margin-top: 8px;
  font-size: 14px; /* Increased from 12px */
  font-weight: bold;
  border-top: 2px solid #000;
  border-bottom: 2px solid #000;
  padding: 5px 0;
}

.struk-total div {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2px;
}

.struk-payment {
  margin-top: 8px;
  font-size: 13px; /* Increased from 11px */
}

.struk-payment div {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2px;
}

.struk-change {
  font-weight: bold;
  font-size: 14px; /* Increased from 12px */
  border-top: 1px dashed #000;
  padding-top: 5px;
  margin-top: 5px;
}

.struk-change div {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2px;
}

.struk-footer {
  text-align: center;
  margin-top: 15px;
  font-size: 11px; /* Increased from 10px */
  border-top: 1px dashed #000;
  padding-top: 8px;
  color: #666;
}

.struk-footer .thank-you {
  font-weight: bold;
  margin-bottom: 3px;
  color: #000;
  font-size: 12px; /* Increased for emphasis */
}

/* ===== TRANSACTION LIST STYLING ===== */
#tab-daftar-transaksi .table {
  font-size: 0.9rem;
}

#tab-daftar-transaksi .table th {
  background-color: #343a40;
  color: white;
  font-weight: 600;
  border: none;
  padding: 12px 8px;
}

#tab-daftar-transaksi .table td {
  padding: 10px 8px;
  vertical-align: middle;
  border-color: #dee2e6;
}

#tab-daftar-transaksi .btn-group-sm .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
}

#tab-daftar-transaksi .badge {
  font-size: 0.75rem;
}

#filterTanggal {
  max-width: 150px;
}

.pagination .page-link {
  padding: 0.375rem 0.75rem;
  font-size: 0.9rem;
}

.pagination .page-item.active .page-link {
  background-color: #007bff;
  border-color: #007bff;
}

/* ===== MEMBER TABLE RESPONSIVE ===== */
.member-table-container {
  max-height: 400px;
  overflow-x: auto !important;  /* Force horizontal scroll */
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  background: white;
  margin-top: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  /* Ensure scrolling works on all devices */
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
}

.member-table {
  margin-bottom: 0 !important;
  min-width: 650px !important; /* Force minimum width to trigger horizontal scroll */
  width: 650px; /* Fixed width to ensure scroll on mobile */
}

.member-table thead th {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: #343a40 !important;
  color: white !important;
  border-bottom: 2px solid #495057;
  font-weight: 600;
  padding: 12px 8px;
  white-space: nowrap;
}

.member-table tbody td {
  padding: 10px 8px;
  vertical-align: middle;
  border-bottom: 1px solid #dee2e6;
  white-space: nowrap;
}

.member-table tbody tr:hover {
  background-color: #f8f9fa;
}

/* Mobile specific styles */
@media (max-width: 768px) {
  .member-table-container {
    max-height: 350px;
    overflow-x: scroll !important; /* Force scroll on mobile */
    -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
    /* Additional mobile scroll fixes */
    position: relative;
    width: 100%;
  }

  .member-table {
    min-width: 600px !important; /* Ensure it's wider than container */
    width: 600px !important; /* Fixed width for mobile scroll */
    font-size: 0.9rem;
  }

  .member-table thead th {
    padding: 8px 6px;
    font-size: 0.85rem;
  }

  .member-table tbody td {
    padding: 8px 6px;
    font-size: 0.85rem;
  }

  .member-table .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
  }

  .member-table .badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.4rem;
  }
}

@media (max-width: 576px) {
  .member-table-container {
    max-height: 300px;
    overflow-x: scroll !important; /* Force scroll on small mobile */
    overflow-y: auto;
  }

  .member-table {
    min-width: 550px !important; /* Ensure scroll on small screens */
    width: 550px !important; /* Fixed width for small mobile */
    font-size: 0.8rem;
  }

  .member-table thead th {
    padding: 6px 4px;
    font-size: 0.8rem;
  }

  .member-table tbody td {
    padding: 6px 4px;
    font-size: 0.8rem;
  }

  .member-table .btn-sm {
    padding: 0.2rem 0.4rem;
    font-size: 0.75rem;
  }

  .member-table .badge {
    font-size: 0.65rem;
    padding: 0.2rem 0.3rem;
  }
}

/* ===== SCROLLBAR STYLING FOR MEMBER TABLE ===== */
.member-table-container::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.member-table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.member-table-container::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.member-table-container::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Firefox scrollbar */
.member-table-container {
  scrollbar-width: thin;
  scrollbar-color: #888 #f1f1f1;
}

/* ===== MEMBER CARDS LAYOUT ===== */
.member-cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 0.75rem;
  margin-top: 1rem;
}

.member-card {
  border-radius: 8px;
  padding: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  min-height: 100px;
}

.member-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.member-card:active {
  transform: translateY(0);
}

/* Card Status Colors */
.member-card.status-active {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
}

.member-card.status-low {
  background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
  color: #212529;
}

.member-card.status-critical {
  background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
  color: white;
}

.member-card-id {
  font-size: 0.7rem;
  font-weight: 600;
  opacity: 0.9;
  margin-bottom: 0.3rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.member-card-name {
  font-size: 0.85rem;
  font-weight: 700;
  margin-bottom: 0.3rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  line-height: 1.2;
}

.member-card-saldo {
  font-size: 0.9rem;
  font-weight: 800;
  margin-bottom: 0;
  line-height: 1.1;
}

/* Icon sizing for compact cards */
.member-card-id i,
.member-card-name i {
  font-size: 0.8rem;
  flex-shrink: 0;
}

/* Ensure text doesn't wrap unnecessarily */
.member-card-name {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Responsive adjustments */
/* 1920x1080: 10-12 cards */
@media (min-width: 1921px) {
  .member-cards-container {
    grid-template-columns: repeat(auto-fill, minmax(155px, 1fr));
    gap: 0.75rem;
  }
}

/* 1500x...: 8 cards */
@media (max-width: 1920px) and (min-width: 1501px) {
  .member-cards-container {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 0.75rem;
  }
}

/* 1360x768 / 1366x768: Exactly 6 cards - Optimized for your screen */
@media (max-width: 1500px) and (min-width: 1200px) {
  .member-cards-container {
    grid-template-columns: repeat(6, 1fr); /* Force exactly 6 columns */
    gap: 0.5rem; /* Reduced gap for tighter fit */
    max-width: 1300px; /* Limit container width */
    margin: 1rem auto 0; /* Center the container */
  }

  .member-card {
    padding: 0.6rem;
    min-height: 85px;
    border-radius: 6px; /* Slightly smaller radius */
  }

  .member-card-id {
    font-size: 0.6rem;
    margin-bottom: 0.2rem;
    gap: 0.2rem;
  }

  .member-card-name {
    font-size: 0.75rem;
    margin-bottom: 0.2rem;
    gap: 0.2rem;
  }

  .member-card-saldo {
    font-size: 0.8rem;
  }

  .member-card-id i,
  .member-card-name i {
    font-size: 0.7rem;
  }
}

/* 900x600: 3-4 cards */
@media (max-width: 1199px) and (min-width: 769px) {
  .member-cards-container {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 0.7rem;
  }

  .member-card {
    padding: 0.7rem;
    min-height: 95px;
  }

  .member-card-id {
    font-size: 0.65rem;
  }

  .member-card-name {
    font-size: 0.8rem;
  }

  .member-card-saldo {
    font-size: 0.85rem;
  }
}

/* Mobile: 2 cards */
@media (max-width: 768px) {
  .member-cards-container {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 0.6rem;
  }

  .member-card {
    padding: 0.6rem;
    min-height: 85px;
  }

  .member-card-id {
    font-size: 0.6rem;
    margin-bottom: 0.2rem;
    gap: 0.2rem;
  }

  .member-card-name {
    font-size: 0.75rem;
    margin-bottom: 0.2rem;
    gap: 0.2rem;
  }

  .member-card-saldo {
    font-size: 0.8rem;
  }
}

/* Very small mobile */
@media (max-width: 480px) {
  .member-cards-container {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }

  .member-card {
    padding: 0.5rem;
    min-height: 80px;
  }

  .member-card-id {
    font-size: 0.55rem;
  }

  .member-card-name {
    font-size: 0.7rem;
  }

  .member-card-saldo {
    font-size: 0.75rem;
  }
}

/* Pagination styling */
.pagination .page-link {
  color: #667eea;
  border-color: #dee2e6;
}

.pagination .page-item.active .page-link {
  background-color: #667eea;
  border-color: #667eea;
}

.pagination .page-link:hover {
  color: #5a6fd8;
  background-color: #e9ecef;
  border-color: #dee2e6;
}
