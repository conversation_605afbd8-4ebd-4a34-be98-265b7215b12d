<?php
/**
 * Test script untuk API PlaySphere IoT
 * Gunakan script ini untuk test API endpoint sebelum menggunakan Wemos
 */

// Konfigurasi
$baseUrl = 'http://*************/playsphere';
$testIP = '************'; // IP Wemos yang akan ditest

echo "=== PlaySphere API Test ===\n";
echo "Base URL: $baseUrl\n";
echo "Test IP: $testIP\n\n";

// Test 1: Find Channel by IP
echo "1. Testing /api/channel/find\n";
echo "URL: $baseUrl/api/channel/find\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, "$baseUrl/api/channel/find");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'X-Forwarded-For: ' . $testIP,
    'Content-Type: application/json'
]);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response1 = curl_exec($ch);
$httpCode1 = curl_getinfo($ch, CURLINFO_HTTP_CODE);

echo "HTTP Code: $httpCode1\n";
echo "Response: $response1\n\n";

if ($httpCode1 == 200) {
    $data = json_decode($response1, true);
    if ($data && $data['success']) {
        $channelId = $data['channel_id'];
        
        // Test 2: Get Channel Status
        echo "2. Testing /api/channel/status/$channelId\n";
        echo "URL: $baseUrl/api/channel/status/$channelId\n";
        
        curl_setopt($ch, CURLOPT_URL, "$baseUrl/api/channel/status/$channelId");
        
        $response2 = curl_exec($ch);
        $httpCode2 = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        echo "HTTP Code: $httpCode2\n";
        echo "Response: $response2\n\n";
    } else {
        echo "Error: API returned success=false\n";
        echo "Message: " . ($data['message'] ?? 'Unknown error') . "\n\n";
    }
} else {
    echo "Error: HTTP $httpCode1\n";
    echo "Response: $response1\n\n";
}

curl_close($ch);

// Test 3: Database Check
echo "3. Database Check\n";
echo "Silakan jalankan query berikut di database:\n";
echo "SELECT s.id, s.nama_station, s.ip_address, k.nama_konsol \n";
echo "FROM station s \n";
echo "JOIN konsol k ON k.id = s.id_konsol \n";
echo "WHERE s.ip_address = '$testIP';\n\n";

echo "=== Test Selesai ===\n";
echo "Jika API test berhasil, Wemos seharusnya bisa terhubung.\n";
echo "Jika gagal, periksa:\n";
echo "1. IP address di database station\n";
echo "2. Route API di Routes.php\n";
echo "3. Controller Api.php\n";
echo "4. Database connection\n";
?>
