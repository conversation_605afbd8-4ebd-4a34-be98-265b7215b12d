<?php

namespace App\Models;

use CodeIgniter\Model;

class TransaksiKasirDetailModel extends Model
{
    protected $table = 'transaksi_kasir_detail';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'transaksi_kasir_id', 'jenis_item', 'item_id', 'nama_item',
        'harga_satuan', 'quantity', 'subtotal', 'detail_data'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    /**
     * Get detail by transaction ID
     */
    public function getByTransactionId($transactionId)
    {
        return $this->where('transaksi_kasir_id', $transactionId)
                   ->orderBy('id', 'ASC')
                   ->findAll();
    }

    /**
     * Get summary by jenis item
     */
    public function getSummaryByJenis($startDate, $endDate = null)
    {
        if (!$endDate) {
            $endDate = $startDate;
        }

        return $this->select('jenis_item, COUNT(*) as total_item, SUM(subtotal) as total_nilai')
                   ->join('transaksi_kasir', 'transaksi_kasir.id = transaksi_kasir_detail.transaksi_kasir_id')
                   ->where('DATE(transaksi_kasir.tanggal_transaksi) >=', $startDate)
                   ->where('DATE(transaksi_kasir.tanggal_transaksi) <=', $endDate)
                   ->groupBy('jenis_item')
                   ->findAll();
    }
}
