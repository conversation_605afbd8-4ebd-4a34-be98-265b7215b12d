<?php
/**
 * Produk Controller
 * Controller untuk manajemen produk dan stok kasir
 * Menangani CRUD produk, kate<PERSON>i, dan monitoring stok
 *
 * <AUTHOR> Team
 * @version 3.0
 */

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\ProdukModel;

class Produk extends BaseController
{
    protected $produkModel;

    public function __construct()
    {
        $this->produkModel = new ProdukModel();
    }

    /**
     * Halaman utama manajemen produk
     */
    public function index()
    {
        $data = [
            'title' => 'Manajemen Stok Produk',
            'produk' => $this->produkModel->where('status', 'aktif')->findAll()
        ];

        return view('produk/index', $data);
    }

    /**
     * Get all products for AJAX
     */
    public function getAllProduk()
    {
        try {
            log_message('info', 'getAllProduk called');

            $produk = $this->produkModel->where('status', 'aktif')->findAll();

            log_message('info', 'Found ' . count($produk) . ' products');

            return $this->response->setJSON([
                'success' => true,
                'data' => $produk,
                'count' => count($produk)
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Error in getAllProduk: ' . $e->getMessage());

            return $this->response->setJSON([
                'success' => false,
                'message' => 'Gagal mengambil data produk: ' . $e->getMessage(),
                'error' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Simpan produk baru
     */
    public function simpan()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(405)->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ]);
        }

        try {
            $validation = \Config\Services::validation();

            $validation->setRules([
                'kode_produk' => 'required|min_length[3]|max_length[20]|is_unique[produk.kode_produk]',
                'nama_produk' => 'required|min_length[3]|max_length[100]',
                'kategori' => 'required|min_length[3]|max_length[50]',
                'harga_beli' => 'required|numeric|greater_than[0]',
                'harga_jual' => 'required|numeric|greater_than[0]',
                'stok' => 'required|integer|greater_than_equal_to[0]',
                'barcode' => 'permit_empty|max_length[50]',
                'deskripsi' => 'permit_empty|max_length[255]'
            ]);

            if (!$validation->withRequest($this->request)->run()) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Validasi gagal',
                    'errors' => $validation->getErrors()
                ]);
            }

            $data = [
                'kode_produk' => $this->request->getPost('kode_produk'),
                'nama_produk' => $this->request->getPost('nama_produk'),
                'kategori' => $this->request->getPost('kategori'),
                'harga_beli' => $this->request->getPost('harga_beli'),
                'harga_jual' => $this->request->getPost('harga_jual'),
                'stok' => $this->request->getPost('stok'),
                'barcode' => $this->request->getPost('barcode') ?: null,
                'deskripsi' => $this->request->getPost('deskripsi') ?: null,
                'status' => 'aktif'
            ];

            $id = $this->produkModel->insert($data);

            if ($id) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Produk berhasil ditambahkan',
                    'data' => array_merge($data, ['id' => $id])
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Gagal menyimpan produk'
                ]);
            }

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Update produk
     */
    public function update($id)
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(405)->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ]);
        }

        try {
            $produk = $this->produkModel->find($id);
            if (!$produk) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Produk tidak ditemukan'
                ]);
            }

            $validation = \Config\Services::validation();

            $validation->setRules([
                'kode_produk' => "required|min_length[3]|max_length[20]|is_unique[produk.kode_produk,id,{$id}]",
                'nama_produk' => 'required|min_length[3]|max_length[100]',
                'kategori' => 'required|min_length[3]|max_length[50]',
                'harga_beli' => 'required|numeric|greater_than[0]',
                'harga_jual' => 'required|numeric|greater_than[0]',
                'stok' => 'required|integer|greater_than_equal_to[0]',
                'barcode' => 'permit_empty|max_length[50]',
                'deskripsi' => 'permit_empty|max_length[255]'
            ]);

            if (!$validation->withRequest($this->request)->run()) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Validasi gagal',
                    'errors' => $validation->getErrors()
                ]);
            }

            $data = [
                'kode_produk' => $this->request->getPost('kode_produk'),
                'nama_produk' => $this->request->getPost('nama_produk'),
                'kategori' => $this->request->getPost('kategori'),
                'harga_beli' => $this->request->getPost('harga_beli'),
                'harga_jual' => $this->request->getPost('harga_jual'),
                'stok' => $this->request->getPost('stok'),
                'barcode' => $this->request->getPost('barcode') ?: null,
                'deskripsi' => $this->request->getPost('deskripsi') ?: null
            ];

            if ($this->produkModel->update($id, $data)) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Produk berhasil diupdate',
                    'data' => array_merge($data, ['id' => $id])
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Gagal mengupdate produk'
                ]);
            }

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Hapus produk (soft delete)
     */
    public function hapus($id)
    {
        try {
            $produk = $this->produkModel->find($id);
            if (!$produk) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Produk tidak ditemukan'
                ]);
            }

            if ($this->produkModel->update($id, ['status' => 'nonaktif'])) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Produk berhasil dihapus'
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Gagal menghapus produk'
                ]);
            }

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Update stok produk
     */
    public function updateStok($id)
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(405)->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ]);
        }

        try {
            $produk = $this->produkModel->find($id);
            if (!$produk) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Produk tidak ditemukan'
                ]);
            }

            $stokBaru = $this->request->getPost('stok');
            $keterangan = $this->request->getPost('keterangan') ?: 'Update stok manual';

            if (!is_numeric($stokBaru) || $stokBaru < 0) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Stok harus berupa angka positif'
                ]);
            }

            if ($this->produkModel->update($id, ['stok' => $stokBaru])) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Stok berhasil diupdate',
                    'data' => [
                        'id' => $id,
                        'stok_lama' => $produk['stok'],
                        'stok_baru' => $stokBaru,
                        'keterangan' => $keterangan
                    ]
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Gagal mengupdate stok'
                ]);
            }

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get produk dengan stok rendah
     */
    public function getStokRendah()
    {
        try {
            $batasStok = $this->request->getGet('batas') ?: 10;
            $produkStokRendah = $this->produkModel->getStokRendah($batasStok);

            return $this->response->setJSON([
                'success' => true,
                'data' => $produkStokRendah,
                'count' => count($produkStokRendah)
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Gagal mengambil data stok rendah: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get all categories
     */
    public function getKategori()
    {
        try {
            $categories = $this->produkModel->select('kategori')
                                          ->where('status', 'aktif')
                                          ->groupBy('kategori')
                                          ->findAll();

            $categoryList = array_column($categories, 'kategori');

            return $this->response->setJSON([
                'success' => true,
                'data' => $categoryList
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Gagal mengambil data kategori: ' . $e->getMessage()
            ]);
        }
    }
}
