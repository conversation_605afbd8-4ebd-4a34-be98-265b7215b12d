<?php
/**
 * Paket Index View
 * Halaman manajemen paket rental gaming
 * Menampilkan daftar paket, form tambah/edit, dan fungsi hapus
 *
 * <AUTHOR> Team
 * @version 3.0
 */
?>
<?= $this->extend('layouts/main') ?>
<?= $this->section('content') ?>

<!-- CSS dan JS Dependencies -->
<link href="<?= base_url('assets/sweetalert2/sweetalert2.min.css') ?>" rel="stylesheet">
<script src="<?= base_url('assets/sweetalert2/sweetalert2.all.min.js') ?>"></script>
<link rel="stylesheet" href="<?= base_url('assets/css/bootstrap-icons.min.css') ?>">
<link rel="stylesheet" href="<?= base_url('assets/css/paket.css') ?>">

<!-- ===== MAIN CONTENT ===== -->
<div class="container-fluid">
  <!-- Header dengan judul dan tombol tambah -->
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h4>Daftar Paket Rental</h4>
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalTambahPaket">
      <i class="bi bi-plus-circle me-1"></i>Tambah Paket
    </button>
  </div>

  <!-- Grid untuk menampilkan card paket -->
  <div class="row gx-4 gy-4">
    <?php foreach ($paket as $p):
      // Parsing durasi dari format HH:MM:SS
      $waktu = explode(':', $p['durasi']);
      $jam = (int)$waktu[0];
      $menit = (int)$waktu[1];
    ?>
      <!-- Card individual untuk setiap paket -->
      <div class="col-xl-3 col-lg-4 col-md-6 col-12">
        <div class="card-paket">
          <!-- Nama paket -->
          <div class="title"><?= esc($p['nama_paket']) ?></div>

          <!-- Durasi paket dengan ikon -->
          <div class="durasi">
            <i class="bi bi-clock"></i>
            <?= $jam ?> Jam <?= $menit > 0 ? $menit . ' Menit' : '' ?>
          </div>

          <!-- Harga paket dengan ikon -->
          <div class="harga">
            <i class="bi bi-cash-stack"></i>
            Rp <?= number_format($p['harga'], 0, ',', '.') ?>
          </div>

          <!-- Keterangan konsol dan deskripsi -->
          <div class="keterangan mt-1">
            <i class="bi bi-controller"></i> <?= esc($p['nama_konsol']) ?><br>
            <?= esc($p['keterangan']) ?>
          </div>

          <!-- Tombol aksi di kanan bawah -->
          <div class="btn-card">
            <button class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#modalEdit<?= $p['id'] ?>" title="Edit">
              <i class="bi bi-pencil"></i>
            </button>
            <a href="<?= base_url('paket/hapus/' . $p['id']) ?>" class="btn btn-danger btn-hapus" title="Hapus">
              <i class="bi bi-trash"></i>
            </a>
          </div>
        </div>
      </div>

      <!-- Modal Edit Paket -->
      <div class="modal fade" id="modalEdit<?= $p['id'] ?>" tabindex="-1">
        <div class="modal-dialog">
          <form action="<?= base_url('paket/update/' . $p['id']) ?>" method="post" class="modal-content ajax-form">
            <div class="modal-header">
              <h5 class="modal-title">Edit Paket</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <div class="mb-3">
                <label>Nama Paket</label>
                <input type="text" name="nama_paket" value="<?= esc($p['nama_paket']) ?>" class="form-control" required>
              </div>
              <div class="mb-3">
                <label>Nama Konsol</label>
                <select name="id_konsol" class="form-select" required>
                  <?php foreach ($konsol as $k): ?>
                    <option value="<?= $k['id'] ?>" <?= $k['id'] == $p['id_konsol'] ? 'selected' : '' ?>><?= esc($k['nama_konsol']) ?></option>
                  <?php endforeach; ?>
                </select>
              </div>
              <div class="mb-3 d-flex gap-2">
                <div class="flex-fill">
                  <label>Jam</label>
                  <input type="number" name="jam" value="<?= $jam ?>" class="form-control" min="0" required>
                </div>
                <div class="flex-fill">
                  <label>Menit</label>
                  <input type="number" name="menit" value="<?= $menit ?>" class="form-control" min="0" max="59" required>
                </div>
              </div>
              <div class="mb-3">
                <label>Harga</label>
                <input type="number" name="harga" value="<?= esc($p['harga']) ?>" class="form-control" required>
              </div>
              <div class="mb-3">
                <label>Keterangan</label>
                <textarea name="keterangan" class="form-control" required><?= esc($p['keterangan']) ?></textarea>
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
              <button type="submit" class="btn btn-primary">Simpan</button>
            </div>
          </form>
        </div>
      </div>
    <?php endforeach; ?>
  </div>
</div>

<!-- Modal Tambah Paket -->
<div class="modal fade" id="modalTambahPaket" tabindex="-1">
  <div class="modal-dialog">
    <form action="<?= base_url('paket/simpan') ?>" method="post" class="modal-content ajax-form">
      <div class="modal-header">
        <h5 class="modal-title">Tambah Paket</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="mb-3">
          <label>Nama Paket</label>
          <input type="text" name="nama_paket" class="form-control" required>
        </div>
        <div class="mb-3">
          <label>Nama Konsol</label>
          <select name="id_konsol" class="form-select" required>
            <option value="">Pilih Konsol</option>
            <?php foreach ($konsol as $k): ?>
              <option value="<?= $k['id'] ?>"><?= esc($k['nama_konsol']) ?></option>
            <?php endforeach; ?>
          </select>
        </div>
        <div class="mb-3 d-flex gap-2">
          <div class="flex-fill">
            <label>Jam</label>
            <input type="number" name="jam" class="form-control" min="0" required>
          </div>
          <div class="flex-fill">
            <label>Menit</label>
            <input type="number" name="menit" class="form-control" min="0" max="59" required>
          </div>
        </div>
        <div class="mb-3">
          <label>Harga</label>
          <input type="number" name="harga" class="form-control" required>
        </div>
        <div class="mb-3">
          <label>Keterangan</label>
          <textarea name="keterangan" class="form-control" required></textarea>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
        <button type="submit" class="btn btn-primary">Simpan</button>
      </div>
    </form>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const forms = document.querySelectorAll('form.ajax-form');
    forms.forEach(form => {
      form.addEventListener('submit', async (e) => {
        e.preventDefault();
        const formData = new FormData(form);

        // Durasi gabungan
        if (form.querySelector('[name="jam"]') && form.querySelector('[name="menit"]')) {
          const jam = form.querySelector('[name="jam"]').value.padStart(2, '0');
          const menit = form.querySelector('[name="menit"]').value.padStart(2, '0');
          formData.set('durasi', `${jam}:${menit}:00`);
        }

        // Tambahkan harga
        if (form.querySelector('[name="harga"]')) {
          const harga = form.querySelector('[name="harga"]').value;
          formData.set('harga', harga);
        }

        try {
          const response = await fetch(form.action, {
            method: 'POST',
            body: formData
          });
          if (response.ok) {
            const modal = bootstrap.Modal.getInstance(form.closest('.modal'));
            if (modal) modal.hide();
            Swal.fire({
              icon: 'success',
              title: 'Berhasil!',
              text: 'Data berhasil disimpan.',
              timer: 1800,
              showConfirmButton: false
            }).then(() => location.reload());
          } else {
            const text = await response.text();
            Swal.fire('Gagal!', text || 'Terjadi kesalahan.', 'error');
          }
        } catch (err) {
          Swal.fire('Error!', err.message, 'error');
        }
      });
    });

    document.querySelectorAll('.btn-hapus').forEach(btn => {
      btn.addEventListener('click', function(e) {
        e.preventDefault();
        const href = this.getAttribute('href');
        Swal.fire({
          title: 'Yakin?',
          text: 'Data akan dihapus!',
          icon: 'warning',
          showCancelButton: true,
          confirmButtonColor: '#d33',
          cancelButtonColor: '#6c757d',
          confirmButtonText: 'Ya, hapus!',
          cancelButtonText: 'Batal'
        }).then((result) => {
          if (result.isConfirmed) {
            window.location.href = href;
          }
        });
      });
    });
  });
</script>

<script src="<?= base_url('assets/js/paket.js') ?>"></script>

<?= $this->endSection() ?>
