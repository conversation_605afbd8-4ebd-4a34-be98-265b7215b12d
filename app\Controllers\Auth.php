<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\OperatorModel;

class Auth extends BaseController
{
    protected $operatorModel;

    public function __construct()
    {
        $this->operatorModel = new OperatorModel();
    }

    /**
     * <PERSON><PERSON> login
     */
    public function login()
    {
        // <PERSON><PERSON> sudah login, redirect ke dashboard
        if (session()->get('logged_in')) {
            return redirect()->to('/dashboard');
        }

        return view('auth/login');
    }

    /**
     * Process login
     */
    public function processLogin()
    {
        if (!$this->request->isAJAX() || $this->request->getMethod() !== 'POST') {
            return $this->response->setStatusCode(405)->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ]);
        }

        $username = $this->request->getPost('username');
        $password = $this->request->getPost('password');

        // Validasi input
        if (empty($username) || empty($password)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Username dan password harus diisi'
            ]);
        }

        // Verifikasi login
        $operator = $this->operatorModel->verifyLogin($username, $password);

        if (!$operator) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Username atau password salah, atau akun tidak aktif'
            ]);
        }

        // Set session
        $sessionData = [
            'operator_id' => $operator['id'],
            'username' => $operator['username'],
            'nama' => $operator['nama'],
            'role' => $operator['role'],
            'logged_in' => true
        ];

        session()->set($sessionData);

        return $this->response->setJSON([
            'success' => true,
            'message' => 'Login berhasil',
            'redirect' => base_url('/dashboard'),
            'user' => [
                'nama' => $operator['nama'],
                'role' => $operator['role']
            ]
        ]);
    }

    /**
     * Logout
     */
    public function logout()
    {
        session()->destroy();
        return redirect()->to('/login')->with('message', 'Anda telah logout');
    }

    /**
     * Check if user is logged in (for AJAX)
     */
    public function checkAuth()
    {
        $isLoggedIn = session()->get('logged_in');
        $role = session()->get('role');

        return $this->response->setJSON([
            'logged_in' => $isLoggedIn,
            'role' => $role,
            'user' => [
                'nama' => session()->get('nama'),
                'username' => session()->get('username')
            ]
        ]);
    }

    /**
     * Check role permission
     */
    public function hasPermission($requiredRole = null)
    {
        if (!session()->get('logged_in')) {
            return false;
        }

        $userRole = session()->get('role');

        // Admin has access to everything
        if ($userRole === 'admin') {
            return true;
        }

        // If no specific role required, just check if logged in
        if ($requiredRole === null) {
            return true;
        }

        // Check specific role
        return $userRole === $requiredRole;
    }

    /**
     * Middleware untuk cek login
     */
    public function requireLogin()
    {
        if (!session()->get('logged_in')) {
            // Check if request is AJAX using service
            $request = \Config\Services::request();
            if ($request && $request->isAJAX()) {
                $response = \Config\Services::response();
                return $response->setStatusCode(401)->setJSON([
                    'success' => false,
                    'message' => 'Silakan login terlebih dahulu',
                    'redirect' => base_url('/login')
                ]);
            } else {
                return redirect()->to('/login')->with('error', 'Silakan login terlebih dahulu');
            }
        }
        return null;
    }

    /**
     * Middleware untuk cek role admin
     */
    public function requireAdmin()
    {
        $loginCheck = $this->requireLogin();
        if ($loginCheck) return $loginCheck;

        if (session()->get('role') !== 'admin') {
            // Check if request is AJAX using service
            $request = \Config\Services::request();
            if ($request && $request->isAJAX()) {
                $response = \Config\Services::response();
                return $response->setStatusCode(403)->setJSON([
                    'success' => false,
                    'message' => 'Akses ditolak. Hanya admin yang dapat mengakses halaman ini.'
                ]);
            } else {
                return redirect()->to('/dashboard')->with('error', 'Akses ditolak. Hanya admin yang dapat mengakses halaman ini.');
            }
        }
        return null;
    }

    /**
     * Get allowed menus based on role
     */
    public function getAllowedMenus($role)
    {
        $allMenus = [
            'dashboard' => 'Dashboard',
            'member' => 'Member',
            'kasir' => 'Kasir',
            'konsol' => 'Konsol',
            'paket' => 'Paket',
            'produk' => 'Produk',
            'laporan' => 'Laporan',
            'setting' => 'Setting'
        ];

        if ($role === 'admin') {
            return $allMenus;
        } elseif ($role === 'operator') {
            return [
                'dashboard' => 'Dashboard',
                'member' => 'Member',
                'kasir' => 'Kasir'
            ];
        }

        return [];
    }

    /**
     * Static method untuk cek login - lebih aman untuk dipanggil dari controller lain
     */
    public static function checkLogin()
    {
        if (!session()->get('logged_in')) {
            return redirect()->to('/login')->with('error', 'Silakan login terlebih dahulu');
        }
        return null;
    }

    /**
     * Static method untuk cek admin - lebih aman untuk dipanggil dari controller lain
     */
    public static function checkAdmin()
    {
        $loginCheck = self::checkLogin();
        if ($loginCheck) return $loginCheck;

        if (session()->get('role') !== 'admin') {
            return redirect()->to('/dashboard')->with('error', 'Akses ditolak. Hanya admin yang dapat mengakses halaman ini.');
        }
        return null;
    }
}
