<?php
/**
 * Konsol Index View
 * Halaman manajemen konsol gaming dan station
 * Menampilkan daftar konsol, station, dan pengaturan harga
 *
 * <AUTHOR> Team
 * @version 3.0
 */
?>
<?= $this->extend('layouts/main') ?>
<?= $this->section('content') ?>

<!-- CSS dan JS Dependencies -->
<link href="<?= base_url('assets/sweetalert2/sweetalert2.min.css') ?>" rel="stylesheet">
<script src="<?= base_url('assets/sweetalert2/sweetalert2.all.min.js') ?>"></script>
<link rel="stylesheet" href="<?= base_url('assets/css/bootstrap-icons.min.css') ?>">
<link rel="stylesheet" href="<?= base_url('assets/css/konsol.css') ?>">

<div class="container-fluid">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h4>Data Konsol</h4>
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalTambahKonsol">Tambah Konsol</button>
  </div>

  <div class="row gx-3 gy-4">
    <?php foreach ($konsol as $k): ?>
      <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 col-12">
        <div class="card-konsol">
          <h5 class="card-title" title="<?= esc($k['nama_konsol']) ?>"><?= esc($k['nama_konsol']) ?></h5>
          <div class="price-info">Personal: Rp <?= number_format($k['harga_personal'], 0, ',', '.') ?></div>
          <div class="price-info">Member: Rp <?= number_format($k['harga_member'], 0, ',', '.') ?></div>
          <div class="btn-icon-group">
            <button class="btn-icon edit" data-bs-toggle="modal" data-bs-target="#modalEditKonsol<?= $k['id'] ?>" title="Edit">
              <i class="bi bi-pencil"></i>
            </button>
            <a href="<?= base_url('konsol/hapus-konsol/' . $k['id']) ?>" class="btn-icon hapus hapus-konsol-<?= $k['id'] ?>" title="Hapus">
              <i class="bi bi-trash"></i>
            </a>
          </div>
        </div>
      </div>
    <?php endforeach; ?>
  </div>

  <div class="d-flex justify-content-between align-items-center mt-5 mb-4">
    <h4>Data Station</h4>
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalTambahStation">Tambah Station</button>
  </div>

  <div class="row gx-3 gy-4">
    <?php foreach ($station as $s): ?>
      <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 col-12">
        <div class="card-station">
          <i class="bi bi-wifi signal-icon"></i>
          <div class="fw-bold mb-2 text-white text-start w-100"> <?= esc($s['nama_station']) ?> </div>
          <div class="card-title"> <?= esc($s['nama_konsol']) ?> </div>
          <p class="card-subtext">IP: <?= esc($s['ip_address']) ?></p>
          <div class="btn-icon-group justify-content-center">
            <button class="btn-icon edit" data-bs-toggle="modal" data-bs-target="#modalEditStation<?= $s['id'] ?>" title="Edit">
              <i class="bi bi-pencil"></i>
            </button>
            <a href="<?= base_url('konsol/hapus-station/' . $s['id']) ?>" class="btn-icon hapus hapus-station-<?= $s['id'] ?>" title="Hapus">
              <i class="bi bi-trash"></i>
            </a>
          </div>
        </div>
      </div>
    <?php endforeach; ?>
  </div>
</div>

<!-- Modal Tambah Konsol -->
<div class="modal fade" id="modalTambahKonsol" tabindex="-1">
  <div class="modal-dialog">
    <form action="<?= base_url('konsol/simpan-konsol') ?>" method="post" class="modal-content ajax-form">
      <div class="modal-header">
        <h5 class="modal-title">Tambah Konsol</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="mb-3">
          <label>Nama Konsol</label>
          <input type="text" name="nama_konsol" class="form-control" required>
        </div>
        <div class="mb-3">
          <label>Harga Personal</label>
          <input type="number" name="harga_personal" class="form-control" required>
        </div>
        <div class="mb-3">
          <label>Harga Member</label>
          <input type="number" name="harga_member" class="form-control" required>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
        <button type="submit" class="btn btn-primary">Simpan</button>
      </div>
    </form>
  </div>
</div>

<!-- Modal Tambah Station -->
<div class="modal fade" id="modalTambahStation" tabindex="-1">
  <div class="modal-dialog">
    <form action="<?= base_url('konsol/simpan-station') ?>" method="post" class="modal-content ajax-form">
      <div class="modal-header">
        <h5 class="modal-title">Tambah Station</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="mb-3">
          <label>Nama Station</label>
          <input type="text" name="nama_station" class="form-control" required>
        </div>
        <div class="mb-3">
          <label>Nama Konsol</label>
          <select name="id_konsol" class="form-select" required>
            <option value="">Pilih Konsol</option>
            <?php foreach ($konsol as $k): ?>
              <option value="<?= $k['id'] ?>"><?= esc($k['nama_konsol']) ?></option>
            <?php endforeach; ?>
          </select>
        </div>
        <div class="mb-3">
          <label>IP Address</label>
          <input type="text" name="ip_address" class="form-control" placeholder="___.___.___.___" required>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
        <button type="submit" class="btn btn-primary">Simpan</button>
      </div>
    </form>
  </div>
</div>

<!-- Modal Edit Konsol Dinamis -->
<?php foreach ($konsol as $k): ?>
<div class="modal fade" id="modalEditKonsol<?= $k['id'] ?>" tabindex="-1">
  <div class="modal-dialog">
    <form action="<?= base_url('konsol/update-konsol/' . $k['id']) ?>" method="post" class="modal-content ajax-form">
      <div class="modal-header">
        <h5 class="modal-title">Edit Konsol</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="mb-3">
          <label>Nama Konsol</label>
          <input type="text" name="nama_konsol" value="<?= esc($k['nama_konsol']) ?>" class="form-control" required>
        </div>
        <div class="mb-3">
          <label>Harga Personal</label>
          <input type="number" name="harga_personal" value="<?= esc($k['harga_personal']) ?>" class="form-control" required>
        </div>
        <div class="mb-3">
          <label>Harga Member</label>
          <input type="number" name="harga_member" value="<?= esc($k['harga_member']) ?>" class="form-control" required>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
        <button type="submit" class="btn btn-primary">Simpan</button>
      </div>
    </form>
  </div>
</div>
<?php endforeach; ?>

<!-- Modal Edit Station Dinamis -->
<?php foreach ($station as $s): ?>
<div class="modal fade" id="modalEditStation<?= $s['id'] ?>" tabindex="-1">
  <div class="modal-dialog">
    <form action="<?= base_url('konsol/update-station/' . $s['id']) ?>" method="post" class="modal-content ajax-form">
      <div class="modal-header">
        <h5 class="modal-title">Edit Station</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="mb-3">
          <label>Nama Station</label>
          <input type="text" name="nama_station" value="<?= esc($s['nama_station']) ?>" class="form-control" required>
        </div>
        <div class="mb-3">
          <label>Nama Konsol</label>
          <select name="id_konsol" class="form-select" required>
            <?php foreach ($konsol as $k): ?>
              <option value="<?= $k['id'] ?>" <?= $k['id'] == $s['id_konsol'] ? 'selected' : '' ?>><?= esc($k['nama_konsol']) ?></option>
            <?php endforeach; ?>
          </select>
        </div>
        <div class="mb-3">
          <label>IP Address</label>
          <input type="text" name="ip_address" value="<?= esc($s['ip_address']) ?>" class="form-control" placeholder="___.___.___.___" required>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
        <button type="submit" class="btn btn-primary">Simpan</button>
      </div>
    </form>
  </div>
</div>
<?php endforeach; ?>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const forms = document.querySelectorAll('form.ajax-form');
    forms.forEach(form => {
      form.addEventListener('submit', async (e) => {
        e.preventDefault();
        const formData = new FormData(form);
        try {
          const response = await fetch(form.action, {
            method: 'POST',
            body: formData
          });
          if (response.ok) {
            const modal = bootstrap.Modal.getInstance(form.closest('.modal'));
            if (modal) modal.hide();
            Swal.fire({
              icon: 'success',
              title: 'Berhasil!',
              text: 'Data berhasil disimpan.',
              timer: 2000,
              showConfirmButton: false
            }).then(() => location.reload());
          } else {
            const text = await response.text();
            Swal.fire('Gagal!', text || 'Terjadi kesalahan.', 'error');
          }
        } catch (error) {
          Swal.fire('Error!', error.message, 'error');
        }
      });
    });

    document.querySelectorAll('a.hapus').forEach(btn => {
      btn.addEventListener('click', function(e) {
        e.preventDefault();
        const href = this.getAttribute('href');
        Swal.fire({
          title: 'Yakin?',
          text: 'Data akan dihapus!',
          icon: 'warning',
          showCancelButton: true,
          confirmButtonColor: '#e53935',
          cancelButtonColor: '#6c757d',
          confirmButtonText: 'Ya, hapus!',
          cancelButtonText: 'Batal'
        }).then((result) => {
          if (result.isConfirmed) {
            window.location.href = href;
          }
        });
      });
    });
  });
</script>

<script src="<?= base_url('assets/js/konsol.js') ?>"></script>

<?= $this->endSection() ?>
