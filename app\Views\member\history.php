<?= $this->extend('layout/template') ?>

<?= $this->section('content') ?>
<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Detail Member</h5>
                    <a href="/member" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table">
                                <tr>
                                    <th width="150">Kode Member</th>
                                    <td><?= $member['member_code'] ?></td>
                                </tr>
                                <tr>
                                    <th>Nama</th>
                                    <td><?= $member['name'] ?></td>
                                </tr>
                                <tr>
                                    <th>No. Telepon</th>
                                    <td><?= $member['phone'] ?></td>
                                </tr>
                                <tr>
                                    <th>Saldo</th>
                                    <td>Rp <?= number_format($member['balance']) ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Riwayat Top-up -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Riwayat Top-up</h5>
                </div>
                <div class="card-body">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Tanggal</th>
                                <th>Jumlah</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($topup_history as $topup): ?>
                            <tr>
                                <td><?= date('d/m/Y H:i', strtotime($topup['transaction_date'])) ?></td>
                                <td>Rp <?= number_format($topup['amount']) ?></td>
                            </tr>
                            <?php endforeach; ?>
                            <?php if (empty($topup_history)): ?>
                            <tr>
                                <td colspan="2" class="text-center">Belum ada riwayat top-up</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>

<!-- Riwayat Billing -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">Riwayat Billing</h5>
    </div>
    <div class="card-body">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Tanggal</th>
                    <th>Channel</th>
                    <th>Paket/Durasi</th>
                    <th>Biaya</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($billing_history)): ?>
                    <?php foreach ($billing_history as $billing): ?>
                    <tr>
                        <td><?= date('d/m/Y H:i', strtotime($billing['start_time'])) ?></td>
                        <td><?= esc($billing['channel_number']) ?></td>
                        <td><?= esc($billing['package_name']) ?></td>
                        <td>Rp <?= number_format($billing['total_price'], 0, ',', '.') ?></td>
                        <td>
                            <span class="badge bg-<?= $billing['status'] == 'completed' ? 'success' : 'warning' ?>">
                                <?= ucfirst($billing['status']) ?>
                            </span>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="5" class="text-center">Belum ada riwayat billing</td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>