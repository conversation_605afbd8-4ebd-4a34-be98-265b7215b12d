<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateOperator extends Migration
{
    public function up()
    {
        // Tabel operator
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'username' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'unique' => true,
                'comment' => 'Username untuk login'
            ],
            'nama' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => false,
                'comment' => 'Nama lengkap operator'
            ],
            'no_wa' => [
                'type' => 'VARCHAR',
                'constraint' => 15,
                'null' => false,
                'comment' => 'Nomor WhatsApp operator'
            ],
            'password' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => false,
                'comment' => 'Password yang sudah di-hash'
            ],
            'role' => [
                'type' => 'ENUM',
                'constraint' => ['admin', 'operator'],
                'default' => 'operator',
                'null' => false,
                'comment' => 'Role: admin atau operator'
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['aktif', 'nonaktif'],
                'default' => 'aktif',
                'null' => false,
                'comment' => 'Status aktif/nonaktif'
            ],
            'last_login' => [
                'type' => 'DATETIME',
                'null' => true,
                'comment' => 'Waktu login terakhir'
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addUniqueKey('username');
        $this->forge->addKey('role');
        $this->forge->addKey('status');
        $this->forge->createTable('operator');

        // Insert default admin
        $this->db->table('operator')->insert([
            'username' => 'admin',
            'nama' => 'Administrator',
            'no_wa' => '081234567890',
            'password' => password_hash('admin123', PASSWORD_DEFAULT),
            'role' => 'admin',
            'status' => 'aktif',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);

        // Insert default operator
        $this->db->table('operator')->insert([
            'username' => 'operator',
            'nama' => 'Operator Default',
            'no_wa' => '081234567891',
            'password' => password_hash('operator123', PASSWORD_DEFAULT),
            'role' => 'operator',
            'status' => 'aktif',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }

    public function down()
    {
        $this->forge->dropTable('operator');
    }
}
