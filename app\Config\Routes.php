<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */

$routes->setAutoRoute(false);

// Dashboard
$routes->get('/', 'Dashboard::index');
$routes->get('dashboard', 'Dashboard::index');
$routes->post('dashboard/startPersonal/(:num)', 'Dashboard::startPersonal/$1');
$routes->post('dashboard/mulai', 'Dashboard::mulai');
$routes->post('dashboard/mulaiPersonal', 'Dashboard::mulaiPersonal');
$routes->post('dashboard/stop', 'Dashboard::stop');
$routes->post('dashboard/updateHarga', 'Dashboard::updateHarga');
$routes->get('dashboard/getSessionStatus/(:num)', 'Dashboard::getSessionStatus/$1');
$routes->get('dashboard/getSessionStatus', 'Dashboard::getSessionStatus');
$routes->get('dashboard/getSessionInfo/(:num)', 'Dashboard::getSessionInfo/$1');
$routes->get('dashboard/getAllMembers', 'Dashboard::getAllMembers');
$routes->get('dashboard/getAllPakets', 'Dashboard::getAllPakets');
$routes->get('dashboard/searchMember', 'Dashboard::searchMember');
$routes->get('dashboard/getPaketByKonsol/(:num)', 'Dashboard::getPaketByKonsol/$1');

// Konsol
$routes->get('/konsol', 'Konsol::index');
$routes->post('/konsol/simpan-konsol', 'Konsol::simpanKonsol');
$routes->post('/konsol/simpan-station', 'Konsol::simpanStation');
$routes->get('/konsol/edit-konsol/(:num)', 'Konsol::editKonsol/$1');
$routes->post('/konsol/update-konsol/(:num)', 'Konsol::updateKonsol/$1');
$routes->get('/konsol/edit-station/(:num)', 'Konsol::editStation/$1');
$routes->post('/konsol/update-station/(:num)', 'Konsol::updateStation/$1');
$routes->get('/konsol/hapus-konsol/(:num)', 'Konsol::hapusKonsol/$1');
$routes->get('/konsol/hapus-station/(:num)', 'Konsol::hapusStation/$1');

// Paket
$routes->get('paket', 'Paket::index');
$routes->post('paket/simpan', 'Paket::simpan');
$routes->post('paket/update/(:num)', 'Paket::update/$1');
$routes->get('paket/hapus/(:num)', 'Paket::hapus/$1');

// Member
$routes->get('member', 'Member::index');
$routes->post('member/simpan', 'Member::simpan');
$routes->post('member/update/(:num)', 'Member::update/$1');
$routes->post('member/topup/(:num)', 'Member::topup/$1');
$routes->get('member/hapus/(:num)', 'Member::hapus/$1');
$routes->get('member/cetakRiwayat/(:num)', 'Member::cetakRiwayat/$1');
$routes->get('member/cetakRiwayatPenggunaan/(:num)', 'Member::cetakRiwayatPenggunaan/$1');
$routes->get('dashboard/getSesiAktif', 'Dashboard::getSesiAktif');

// Move Station Routes
$routes->get('dashboard/getAvailableStationsForMove/(:num)', 'Dashboard::getAvailableStationsForMove/$1');
$routes->post('dashboard/moveSession', 'Dashboard::moveSession');
$routes->post('dashboard/addPackageToSession', 'Dashboard::addPackageToSession');
$routes->post('dashboard/updateMemberSaldo', 'Dashboard::updateMemberSaldo');
$routes->post('dashboard/updateMemberNonPackageSaldo', 'Dashboard::updateMemberNonPackageSaldo');

// Kasir Routes
$routes->get('kasir', 'Kasir::index');
$routes->post('kasir/bayarSesi', 'Kasir::bayarSesi');
$routes->post('kasir/topupMember', 'Kasir::topupMember');
$routes->post('kasir/processCheckout', 'Kasir::processCheckout');
$routes->get('kasir/getUnpaidSessions', 'Kasir::getUnpaidSessions');
$routes->get('kasir/getTransaksiList', 'Kasir::getTransaksiList');
$routes->get('kasir/cetakStruk/(:num)', 'Kasir::cetakStruk/$1');

// Setting Routes
$routes->get('setting', 'Setting::index');
$routes->post('setting/saveRental', 'Setting::saveRental');
$routes->get('setting/getRentalSettings', 'Setting::getRentalSettings');

// Produk Routes
$routes->get('produk', 'Produk::index');
$routes->get('produk/getAllProduk', 'Produk::getAllProduk');
$routes->get('produk/getKategori', 'Produk::getKategori');
$routes->post('produk/simpan', 'Produk::simpan');
$routes->post('produk/update/(:num)', 'Produk::update/$1');
$routes->post('produk/hapus/(:num)', 'Produk::hapus/$1');
$routes->post('produk/updateStok/(:num)', 'Produk::updateStok/$1');
$routes->get('produk/getStokRendah', 'Produk::getStokRendah');

// Laporan Routes
$routes->get('laporan', 'Laporan::index');
$routes->get('laporan/test', function() {
    return view('test_laporan');
});
$routes->get('laporan/debug', 'Laporan::debug');
$routes->get('laporan/debugProduk', 'Laporan::debugProduk');
$routes->get('laporan/checkProdukToday', 'Laporan::checkProdukToday');
$routes->get('laporan/createSampleProdukTransaction', 'Laporan::createSampleProdukTransaction');
$routes->get('laporan/debugAllProdukToday', 'Laporan::debugAllProdukToday');
$routes->get('laporan/checkJenisItem', 'Laporan::checkJenisItem');
$routes->get('laporan/debugPaket', 'Laporan::debugPaket');
$routes->get('laporan/debugMember', 'Laporan::debugMember');

// Auth routes
$routes->get('login', 'Auth::login');
$routes->post('auth/processLogin', 'Auth::processLogin');
$routes->get('logout', 'Auth::logout');
$routes->get('auth/checkAuth', 'Auth::checkAuth');

// Setting Operator routes
$routes->get('setting/operator', 'Setting::operator');
$routes->get('setting/getOperators', 'Setting::getOperators');
$routes->post('setting/createOperator', 'Setting::createOperator');
$routes->get('setting/getOperator/(:num)', 'Setting::getOperator/$1');
$routes->post('setting/updateOperator/(:num)', 'Setting::updateOperator/$1');
$routes->post('setting/deleteOperator/(:num)', 'Setting::deleteOperator/$1');
$routes->post('setting/toggleStatus/(:num)', 'Setting::toggleStatus/$1');
$routes->get('laporan/laporanKonsol', 'Laporan::laporanKonsol');
$routes->get('laporan/laporanStation', 'Laporan::laporanStation');
$routes->get('laporan/laporanMember', 'Laporan::laporanMember');
$routes->get('laporan/laporanPaket', 'Laporan::laporanPaket');
$routes->get('laporan/laporanTopup', 'Laporan::laporanTopup');
$routes->get('laporan/laporanPendapatan', 'Laporan::laporanPendapatan');
$routes->get('laporan/laporanProduk', 'Laporan::laporanProduk');

// API Routes for IoT Devices
$routes->get('api/channel/find', 'Api::findChannelByIp');
$routes->get('api/channel/status/(:num)', 'Api::getChannelStatus/$1');

// Test route
$routes->get('test-produk', function() {
    return view('test_produk');
});
