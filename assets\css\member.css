/**
 * Member CSS
 * Styling untuk halaman manajemen member
 * Termasuk card member, tombol aksi, dan responsive design
 * 
 * <AUTHOR>
 * @version 3.0
 */

/* ===== STYLING CARD MEMBER ===== */
/* Card utama untuk menampilkan data member */
.card-member {
  border-radius: 16px;
  background: linear-gradient(135deg, #26a69a, #80cbc4); /* Gradient hijau */
  color: white;
  padding: 20px;
  box-shadow: 0 6px 16px rgba(0,0,0,0.1);
  position: relative;
  transition: transform 0.2s ease;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  min-height: 200px; /* Minimum height untuk konsistensi */
}

/* Efek hover untuk card member */
.card-member:hover {
  transform: translateY(-5px);
}

/* Styling untuk ID member */
.card-member .title {
  font-size: 0.95rem;
  font-weight: bold;
  margin-bottom: 10px;
}

/* Styling untuk informasi member (nama, WA, saldo) */
.card-member .info {
  font-size: 0.95rem;
  margin-bottom: 5px;
}

/* Container tombol di bagian bawah card */
.card-member .btn-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
  margin-top: auto; /* Dorong ke bawah */
  padding-top: 15px;
}

/* Styling tombol history */
.history-buttons {
  display: flex;
  gap: 4px;
}

.history-buttons button {
  color: #212529 !important;
  background-color: #f1f1f1 !important;
  border-color: #ccc !important;
  font-weight: 500;
}

.history-buttons button:hover {
  background-color: #e0e0e0 !important;
  border-color: #bbb !important;
}

/* Styling tombol action */
.action-buttons {
  display: flex;
  gap: 4px;
}

/* ===== RESPONSIVE DESIGN ===== */
/* Custom responsive grid sama seperti dashboard */
@media (min-width: 1400px) {
  .col-xxl-1-5 {
    flex: 0 0 auto;
    width: 12.5%; /* 8 columns for 1920x1080 and larger */
  }
}

@media (min-width: 1600px) {
  .col-xxl-1-5 {
    width: 11.11%; /* 9 columns for very large screens */
  }
}

@media (min-width: 1920px) {
  .col-xxl-1-5 {
    width: 10%; /* 10 columns for ultra-wide screens */
  }
}

/* Responsive adjustments for member card content */
@media (max-width: 576px) {
  .card-member {
    min-height: 140px;
    padding: 12px;
  }
  
  .card-member .title {
    font-size: 0.9rem;
  }
  
  .card-member .info {
    font-size: 0.8rem;
  }
  
  .card-member .btn-footer {
    flex-direction: column;
    gap: 8px;
  }
}

@media (min-width: 577px) and (max-width: 768px) {
  .card-member {
    min-height: 150px;
    padding: 14px;
  }
}

@media (min-width: 1200px) {
  .card-member {
    min-height: 170px;
    padding: 18px;
  }
}

@media (min-width: 1400px) {
  .card-member {
    min-height: 165px;
    padding: 14px;
  }
  
  .card-member .title {
    font-size: 0.95rem;
    margin-bottom: 0.2rem;
  }
  
  .card-member .info {
    font-size: 0.85rem;
    line-height: 1.2;
  }
}

/* Tombol 1 baris untuk resolusi 500px ke atas */
@media (min-width: 500px) {
  .card-member .btn-footer {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 6px;
    padding-top: 12px;
  }

  .card-member .btn-footer .history-buttons {
    display: flex;
    gap: 3px;
  }

  .card-member .btn-footer .history-buttons .btn {
    font-size: 0.75rem;
    padding: 4px 6px;
    min-width: 28px;
    height: 28px;
  }

  .card-member .btn-footer .action-buttons {
    display: flex;
    gap: 3px;
  }

  .card-member .btn-footer .action-buttons .btn {
    font-size: 0.75rem;
    padding: 4px 6px;
    min-width: 28px;
    height: 28px;
  }
}

/* Penyesuaian untuk resolusi menengah */
@media (min-width: 768px) and (max-width: 1199px) {
  .card-member .btn-footer .history-buttons .btn {
    font-size: 0.7rem;
    padding: 3px 5px;
    min-width: 26px;
    height: 26px;
  }

  .card-member .btn-footer .action-buttons .btn {
    font-size: 0.7rem;
    padding: 3px 5px;
    min-width: 26px;
    height: 26px;
  }
}

/* Penyesuaian untuk resolusi 1200px-1399px (termasuk 1360x768) */
@media (min-width: 1200px) and (max-width: 1399px) {
  .card-member .btn-footer {
    gap: 4px;
    padding-top: 10px;
  }

  .card-member .btn-footer .history-buttons {
    gap: 2px;
  }

  .card-member .btn-footer .history-buttons .btn {
    font-size: 0.65rem;
    padding: 2px 4px;
    min-width: 24px;
    height: 24px;
  }

  .card-member .btn-footer .action-buttons {
    gap: 2px;
  }

  .card-member .btn-footer .action-buttons .btn {
    font-size: 0.65rem;
    padding: 2px 4px;
    min-width: 24px;
    height: 24px;
  }
}

/* Resolusi tinggi 1400px+ - tetap 1 baris tapi lebih kecil */
@media (min-width: 1400px) {
  .card-member .btn-footer {
    gap: 3px;
    padding-top: 8px;
  }

  .card-member .btn-footer .history-buttons {
    gap: 2px;
  }

  .card-member .btn-footer .history-buttons .btn {
    font-size: 0.6rem;
    padding: 2px 3px;
    min-width: 22px;
    height: 22px;
  }

  .card-member .btn-footer .action-buttons {
    gap: 2px;
  }

  .card-member .btn-footer .action-buttons .btn {
    font-size: 0.6rem;
    padding: 2px 3px;
    min-width: 22px;
    height: 22px;
  }
}

@media (min-width: 1600px) {
  .card-member .btn-footer .history-buttons .btn {
    font-size: 0.55rem;
    padding: 1px 2px;
    min-width: 20px;
    height: 20px;
  }

  .card-member .btn-footer .action-buttons .btn {
    font-size: 0.55rem;
    padding: 1px 2px;
    min-width: 20px;
    height: 20px;
  }
}

@media (min-width: 1920px) {
  .card-member .btn-footer .history-buttons .btn {
    font-size: 0.5rem;
    padding: 1px 2px;
    min-width: 18px;
    height: 18px;
  }

  .card-member .btn-footer .action-buttons .btn {
    font-size: 0.5rem;
    padding: 1px 2px;
    min-width: 18px;
    height: 18px;
  }
}

/* Container responsive adjustments */
.container-fluid {
  padding-left: 15px;
  padding-right: 15px;
}

@media (min-width: 1400px) {
  .container-fluid {
    padding-left: 30px;
    padding-right: 30px;
  }
}

/* Row gap adjustments */
.row.g-3 {
  --bs-gutter-x: 1rem;
  --bs-gutter-y: 1.5rem;
}

@media (max-width: 576px) {
  .row.g-3 {
    --bs-gutter-x: 0.75rem;
    --bs-gutter-y: 1rem;
  }
}

@media (min-width: 1200px) {
  .row.g-3 {
    --bs-gutter-x: 1.25rem;
    --bs-gutter-y: 1.75rem;
  }
}
