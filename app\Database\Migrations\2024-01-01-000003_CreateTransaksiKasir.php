<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateTransaksiKasir extends Migration
{
    public function up()
    {
        // Tabel transaksi kasir (header)
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'nomor_transaksi' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => false,
                'comment' => 'Nomor transaksi unik (TRX-YYYYMMDD-XXXX)'
            ],
            'tanggal_transaksi' => [
                'type' => 'DATETIME',
                'null' => false,
                'comment' => 'Tanggal dan waktu transaksi'
            ],
            'total_item' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 0,
                'comment' => 'Jumlah total item dalam transaksi'
            ],
            'total_harga' => [
                'type' => 'DECIMAL',
                'constraint' => '12,2',
                'default' => 0,
                'comment' => 'Total harga semua item'
            ],
            'metode_bayar' => [
                'type' => 'ENUM',
                'constraint' => ['tunai', 'qris', 'debit', 'transfer'],
                'null' => false,
                'comment' => 'Metode pembayaran yang digunakan'
            ],
            'jumlah_bayar' => [
                'type' => 'DECIMAL',
                'constraint' => '12,2',
                'null' => true,
                'comment' => 'Jumlah uang yang dibayarkan (untuk tunai)'
            ],
            'kembalian' => [
                'type' => 'DECIMAL',
                'constraint' => '12,2',
                'default' => 0,
                'comment' => 'Kembalian yang diberikan'
            ],
            'kasir' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
                'comment' => 'Nama kasir yang melayani'
            ],
            'keterangan' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Keterangan tambahan transaksi'
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addUniqueKey('nomor_transaksi');
        $this->forge->addKey('tanggal_transaksi');
        $this->forge->createTable('transaksi_kasir');

        // Tabel detail transaksi kasir
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'transaksi_kasir_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'comment' => 'Foreign key ke tabel transaksi_kasir'
            ],
            'jenis_item' => [
                'type' => 'ENUM',
                'constraint' => ['session_payment', 'member_topup', 'product_sale'],
                'null' => false,
                'comment' => 'Jenis item: pembayaran sesi, top-up member, penjualan produk'
            ],
            'item_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => true,
                'comment' => 'ID item terkait (sesi_id, member_id, produk_id)'
            ],
            'nama_item' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => false,
                'comment' => 'Nama/deskripsi item'
            ],
            'harga_satuan' => [
                'type' => 'DECIMAL',
                'constraint' => '10,2',
                'null' => false,
                'comment' => 'Harga per satuan item'
            ],
            'quantity' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 1,
                'comment' => 'Jumlah item'
            ],
            'subtotal' => [
                'type' => 'DECIMAL',
                'constraint' => '10,2',
                'null' => false,
                'comment' => 'Subtotal (harga_satuan * quantity)'
            ],
            'detail_data' => [
                'type' => 'JSON',
                'null' => true,
                'comment' => 'Data detail tambahan dalam format JSON'
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('transaksi_kasir_id');
        $this->forge->addKey('jenis_item');
        $this->forge->addForeignKey('transaksi_kasir_id', 'transaksi_kasir', 'id', 'CASCADE', 'CASCADE');
        $this->forge->createTable('transaksi_kasir_detail');
    }

    public function down()
    {
        $this->forge->dropTable('transaksi_kasir_detail');
        $this->forge->dropTable('transaksi_kasir');
    }
}
