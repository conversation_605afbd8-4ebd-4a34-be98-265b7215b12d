<?php
/**
 * Dashboard Controller
 * Controller utama untuk halaman monitoring dan kontrol station gaming
 * Menangani operasi CRUD sesi gaming, monitoring real-time, dan manajemen station
 *
 * <AUTHOR> Team
 * @version 3.0
 */

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\StationModel;
use App\Models\SesiModel;
use App\Models\KonsolModel;
use App\Models\MemberModel;
use App\Models\PaketModel;

class Dashboard extends BaseController
{
    /**
     * Menambahkan CORS headers untuk API requests
     * Diperlukan untuk komunikasi AJAX dari frontend
     */
    private function addCorsHeaders()
    {
        $this->response->setHeader('Access-Control-Allow-Origin', '*');
        $this->response->setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
        $this->response->setHeader('Access-Control-Allow-Headers', 'Content-Type, X-Requested-With');
    }

    /**
     * <PERSON>aman utama dashboard
     * Menampilkan monitoring real-time semua station gaming
     *
     * @return string View dashboard dengan data station
     */
    public function index()
    {
        // Check if user is logged in
        $authCheck = \App\Controllers\Auth::checkLogin();
        if ($authCheck) return $authCheck;

        // Inisialisasi model yang diperlukan
        $stationModel = new StationModel();
        $konsolModel  = new KonsolModel();
        $sesiModel    = new SesiModel();
        $memberModel  = new MemberModel();

        // Ambil data semua station dengan informasi konsol dan harga
        $stations = $stationModel
            ->select('station.*, konsol.nama_konsol, konsol.harga_personal, konsol.harga_member')
            ->join('konsol', 'konsol.id = station.id_konsol')
            ->findAll();

        $sesiBerjalan = $sesiModel->getRunningSessionsWithDetails();

        $sesiMap = [];
        foreach ($sesiBerjalan as $sesi) {
            $sesiMap[$sesi['station_id']] = $sesi;
        }

        foreach ($stations as &$s) {
            $s['mulai'] = null;
            $s['status'] = 'tidak_aktif';
            $s['jenis_user'] = null;
            $s['nama_member'] = null;
            $s['nama_paket'] = null;
            $s['durasi_sisa'] = null;

            if (isset($sesiMap[$s['id']])) {
                $sesi = $sesiMap[$s['id']];
                $s['mulai'] = $sesi['waktu_mulai'];
                $s['status'] = 'berjalan';
                $s['jenis_user'] = $sesi['jenis_user'];
                $s['nama_member'] = $sesi['nama_member'] ?? null;
                $s['nama_paket'] = $sesi['nama_paket'] ?? null;
                $s['durasi_sisa'] = $sesi['durasi_sisa'];
            }
        }

        return view('dashboard/index', ['station' => $stations]);
    }

    public function mulai()
    {
        if (!$this->request->isAJAX() || $this->request->getMethod() !== 'POST') {
            return $this->response->setStatusCode(405)->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ]);
        }

        try {
            $stationId  = $this->request->getPost('station_id');
            $jenisUser  = $this->request->getPost('jenis_user');
            $idMember   = $this->request->getPost('id_member');
            $idPaket    = $this->request->getPost('id_paket') ?: $this->request->getPost('paket_id');

            // Debug: log received data
            log_message('debug', 'Received POST data: ' . json_encode($this->request->getPost()));
            log_message('debug', 'Station ID: ' . $stationId . ', Jenis User: ' . $jenisUser . ', ID Member: ' . $idMember . ', ID Paket: ' . $idPaket);

            if (empty($stationId) || empty($jenisUser)) {
                return $this->response->setStatusCode(400)->setJSON([
                    'success' => false,
                    'message' => 'Data tidak lengkap'
                ]);
            }

            $sesiModel = new SesiModel();
            $stationModel = new StationModel();
            $konsolModel = new KonsolModel();
            $memberModel = new MemberModel();
            $paketModel = new PaketModel();

            $existingSesi = $sesiModel
                ->where('station_id', $stationId)
                ->where('status', 'berjalan')
                ->first();

            if ($existingSesi) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Station sudah memiliki sesi yang berjalan'
                ]);
            }

            $station = $stationModel
                ->select('station.*, konsol.harga_personal, konsol.harga_member')
                ->join('konsol', 'konsol.id = station.id_konsol')
                ->find($stationId);

            if (!$station) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Station tidak ditemukan'
                ]);
            }

            $hargaAwal = 0;
            $durasiSisa = null;

            if ($jenisUser === 'member') {
                $member = $memberModel->find($idMember);
                if (!$member) {
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => 'Member tidak ditemukan'
                    ]);
                }

                // Check if member is already being used in another station
                $activeSesi = $sesiModel
                    ->where('id_member', $idMember)
                    ->where('status', 'berjalan')
                    ->first();

                if ($activeSesi) {
                    $activeStation = $stationModel->find($activeSesi['station_id']);
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => "Member sedang digunakan di {$activeStation['nama_station']}"
                    ]);
                }

                if ($idPaket) {
                    $paket = $paketModel->find($idPaket);
                    log_message('debug', 'Paket data: ' . json_encode($paket));
                    
                    if (!$paket) {
                        return $this->response->setJSON([
                            'success' => false,
                            'message' => 'Paket tidak ditemukan'
                        ]);
                    }
                    
                    if ($member['saldo'] < $paket['harga']) {
                        return $this->response->setJSON([
                            'success' => false,
                            'message' => 'Saldo tidak cukup untuk paket ini'
                        ]);
                    }

                    // Log before saldo update
                    log_message('debug', 'Before saldo update - Member ID: ' . $member['id'] . ', Current saldo: ' . $member['saldo'] . ', Paket harga: ' . $paket['harga']);
                    
                    $memberModel->update($member['id'], [
                        'saldo' => $member['saldo'] - $paket['harga']
                    ]);

                    // Log raw duration value from package
                    log_message('debug', 'Raw package duration value: ' . $paket['durasi']);
                    
                    // Parse duration - handle both "HH:mm" format and plain minutes
                    $durasi = $paket['durasi'];
                    if (strpos($durasi, ':') !== false) {
                        // Format is "HH:mm"
                        list($hours, $minutes) = explode(':', $durasi);
                        $durasiMenit = (intval($hours) * 60) + intval($minutes);
                    } else {
                        // Plain minutes format
                        $durasiMenit = intval($durasi);
                    }
                    
                    // Convert to seconds (1 minute = 60 seconds)
                    $durasiSisa = $durasiMenit * 60;
                    $hargaAwal = intval($paket['harga']);
                    
                    // Log parsed values
                    log_message('debug', 'Parsed duration (minutes): ' . $durasiMenit);
                    log_message('debug', 'Calculated duration (seconds): ' . $durasiSisa);

                } else {
                    // Member non-paket - konversi saldo ke waktu
                    $hargaPerJam = $station['harga_member'];
                    $hargaPerMenit = round($hargaPerJam / 60);

                    if ($member['saldo'] < $hargaPerMenit) {
                        return $this->response->setJSON([
                            'success' => false,
                            'message' => 'Saldo tidak cukup untuk memulai sesi non-paket'
                        ]);
                    }

                    // Konversi saldo ke waktu dalam detik (exact calculation)
                    $hargaPerDetik = $hargaPerMenit / 60;
                    $durasiSisa = floor($member['saldo'] / $hargaPerDetik); // Durasi dalam detik
                    $waktuDalamMenit = $durasiSisa / 60; // Untuk logging

                    log_message('debug', "Dashboard::mulai - Member non-paket: Saldo {$member['saldo']}, Harga per menit: {$hargaPerMenit}, Harga per detik: {$hargaPerDetik}, Waktu: {$waktuDalamMenit} menit, Durasi: {$durasiSisa} detik");

                    $hargaAwal = 0; // Don't deduct saldo initially for non-package sessions
                }
            } else {
                $hargaPerJam = $station['harga_personal'];
                $hargaPer15mnt = round($hargaPerJam / 4);
                $hargaAwal = $hargaPer15mnt;
            }

            $data = [
                'station_id' => $stationId,
                'jenis_user' => $jenisUser,
                'waktu_mulai' => date('Y-m-d H:i:s'),
                'harga_total' => $hargaAwal,
                'status' => 'berjalan',
                'id_member' => $idMember ?? null,
                'id_paket' => $idPaket ?? null,
                'durasi_sisa' => $durasiSisa,
                'sisa_saldo' => 0 // Default for personal sessions
            ];

            // Set sisa_saldo for member sessions
            if ($jenisUser === 'member' && $idMember) {
                $memberModel = new MemberModel();
                $member = $memberModel->find($idMember);
                if ($member) {
                    $data['sisa_saldo'] = $member['saldo'];
                }
            }

            $inserted = $sesiModel->insert($data);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Sesi dimulai',
                'session_id' => $inserted,
                'station_id' => $stationId,
                'harga_awal' => $hargaAwal
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Exception in mulai(): ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return $this->response->setStatusCode(500)->setJSON([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ]);
        }
    }

    public function stop()
    {
        // Pastikan ini adalah request POST dan AJAX
        if (!$this->request->isAJAX() || $this->request->getMethod() !== 'POST') {
            return $this->response->setStatusCode(405)->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ]);
        }

        try {
            $stationId = $this->request->getPost('station_id');
            $harga = $this->request->getPost('harga_total');

            // Validasi input
            if (empty($stationId)) {
                return $this->response->setStatusCode(400)->setJSON([
                    'success' => false,
                    'message' => 'Station ID harus diisi'
                ]);
            }

            // Debug log
            log_message('debug', "Stop request - Station ID: {$stationId}, Harga Final: {$harga}");

            $sesiModel = new SesiModel();

            $sesi = $sesiModel
                ->where('station_id', $stationId)
                ->where('status', 'berjalan')
                ->first();

            if (!$sesi) {
                log_message('debug', "No running session found for station: {$stationId}");
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Session tidak ditemukan atau sudah berhenti'
                ]);
            }

            // Update data dengan waktu berhenti dan harga final
            $updateData = [
                'waktu_berhenti' => date('Y-m-d H:i:s'),
                'status' => 'berhenti',
                'status_bayar' => 'belum' // Set status bayar untuk sesi personal
            ];

            // For member non-package sessions, update member saldo
            if ($sesi['jenis_user'] === 'member' && empty($sesi['id_paket'])) {
                // Calculate actual usage time
                $waktuMulai = strtotime($sesi['waktu_mulai']);
                $waktuBerhenti = time();
                $elapsedSeconds = $waktuBerhenti - $waktuMulai;

                // Get station and konsol info for pricing
                $stationModel = new StationModel();
                $station = $stationModel
                    ->select('station.*, konsol.harga_member')
                    ->join('konsol', 'konsol.id = station.id_konsol')
                    ->where('station.id', $stationId)
                    ->first();

                $hargaPerJam = $station['harga_member'] ?? 0;
                $hargaPerMenit = round($hargaPerJam / 60);
                $hargaPerDetik = $hargaPerMenit / 60;

                // Calculate total deduction
                $totalDeduction = $hargaPerDetik * $elapsedSeconds;

                // Get initial saldo (stored in harga_total for non-package sessions)
                $initialSaldo = $sesi['harga_total'] ?? 0;

                // If harga_total is 0, get from member table
                if ($initialSaldo == 0) {
                    $memberModel = new MemberModel();
                    $member = $memberModel->find($sesi['id_member']);
                    $initialSaldo = $member ? $member['saldo'] : 0;
                }

                // Calculate final saldo
                $finalSaldo = max(0, $initialSaldo - $totalDeduction);

                // Update member saldo
                $memberModel = new MemberModel();
                $memberModel->update($sesi['id_member'], ['saldo' => $finalSaldo]);

                // Store total deduction in harga_total
                $updateData['harga_total'] = $totalDeduction;

                log_message('debug', "Member non-paket session stopped - Initial saldo: {$initialSaldo}, Elapsed: {$elapsedSeconds}s, Deducted: {$totalDeduction}, Final saldo: {$finalSaldo}");
            } else {
                // For other session types (personal), use provided harga or current harga_total
                if (!empty($harga) && $harga > 0) {
                    $updateData['harga_total'] = $harga;
                } else {
                    // If no harga provided, keep current harga_total from database
                    $updateData['harga_total'] = $sesi['harga_total'] ?? 0;
                }
            }

            $updated = $sesiModel->update($sesi['id'], $updateData);

            if ($updated) {
                log_message('debug', "Session stopped successfully. ID: {$sesi['id']}, Final price: {$harga}");
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Session berhasil dihentikan',
                    'harga_final' => $harga,
                    'session_id' => $sesi['id'],
                    'waktu_mulai' => $sesi['waktu_mulai'],
                    'waktu_berhenti' => date('Y-m-d H:i:s')
                ]);
            } else {
                $errors = $sesiModel->errors();
                log_message('error', "Update failed. Errors: " . json_encode($errors));
                return $this->response->setStatusCode(500)->setJSON([
                    'success' => false,
                    'message' => 'Gagal mengupdate data di database',
                    'errors' => $errors
                ]);
            }

        } catch (\Exception $e) {
            log_message('error', "Exception in stop(): " . $e->getMessage());
            return $this->response->setStatusCode(500)->setJSON([
                'success' => false,
                'message' => 'Terjadi kesalahan sistem: ' . $e->getMessage()
            ]);
        }
    }


    // Method tambahan untuk monitoring (opsional)
    public function getSessionStatus($stationId = null)
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(405)->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ]);
        }

        try {
            $sesiModel = new SesiModel();

            if ($stationId) {
                // Get specific station session with details
                $sesi = $sesiModel
                    ->select('sesi.*, member.nama as nama_member, member.saldo as member_saldo, paket.nama_paket, paket.durasi, konsol.harga_personal, konsol.harga_member, station.nama_station')
                    ->join('member', 'member.id = sesi.id_member', 'left')
                    ->join('paket', 'paket.id = sesi.id_paket', 'left')
                    ->join('station', 'station.id = sesi.station_id')
                    ->join('konsol', 'konsol.id = station.id_konsol')
                    ->where('sesi.station_id', $stationId)
                    ->where('sesi.status', 'berjalan')
                    ->first();

                if ($sesi && $sesi['jenis_user'] === 'member') {
                    if (empty($sesi['id_paket'])) {
                        // Handle non-package member session
                        $waktuMulai = strtotime($sesi['waktu_mulai']);
                        $now = time();
                        $elapsedSeconds = $now - $waktuMulai;

                        $hargaPerJam = $sesi['harga_member'] ?? 0;
                        $hargaPerMenit = round($hargaPerJam / 60);
                        $totalDeduction = $hargaPerMenit * floor($elapsedSeconds / 60);

                        $memberModel = new MemberModel();
                        $member = $memberModel->find($sesi['id_member']);
                        $initialSaldo = $member ? $member['saldo'] : 0;

                        $currentSaldo = max(0, $initialSaldo - $totalDeduction);
                        $sesi['member_saldo'] = $currentSaldo;

                        if ($member) {
                            $memberModel->update($sesi['id_member'], ['saldo' => $currentSaldo]);
                        }
                    } else {
                        // Handle package member session
                        // Use stored durasi_sisa which includes any added packages
                        $waktuMulai = strtotime($sesi['waktu_mulai']);
                        $now = time();
                        $elapsedSeconds = $now - $waktuMulai;
                        
                        // Get stored durasi_sisa (which may include added packages)
                        $storedDurasiSisa = intval($sesi['durasi_sisa'] ?? 0);
                        
                        // Calculate remaining duration based on stored value
                        $adjustedDurasiSisa = max(0, $storedDurasiSisa - $elapsedSeconds);
                        
                        // IMPORTANT: Update the session data with current remaining time
                        // This ensures frontend gets the correct remaining time including any added packages
                        $sesi['durasi_sisa'] = $adjustedDurasiSisa;
                        
                        // Log for debugging
                        log_message('debug', "Session {$sesi['id']} - Stored duration: {$storedDurasiSisa}s, Elapsed: {$elapsedSeconds}s, Remaining: {$adjustedDurasiSisa}s");
                        
                        // Auto-stop session if time has expired
                        if ($adjustedDurasiSisa <= 0) {
                            log_message('info', "Auto-stopping expired session {$sesi['id']} for station {$stationId}");
                            $sesiModel->update($sesi['id'], [
                                'waktu_berhenti' => date('Y-m-d H:i:s'),
                                'status' => 'berhenti'
                            ]);
                            return $this->response->setJSON([
                                'success' => true,
                                'session' => null,
                                'expired' => true
                            ]);
                        }
                    }
                }

                return $this->response->setJSON([
                    'success' => true,
                    'session' => $sesi
                ]);
            } else {
                // Get all running sessions with details using the model method
                $sessions = $sesiModel->getRunningSessionsWithDetails();

                // Add member_saldo for each session
                $memberModel = new MemberModel();
                foreach ($sessions as &$session) {
                    if ($session['id_member']) {
                        $member = $memberModel->find($session['id_member']);
                        $session['member_saldo'] = $member ? $member['saldo'] : 0;
                    }
                }

                // member_saldo and durasi_sisa are already calculated in SesiModel
                // No additional processing needed here

                return $this->response->setJSON([
                    'success' => true,
                    'sessions' => $sessions,
                    'count' => count($sessions)
                ]);
            }

        } catch (\Exception $e) {
            log_message('error', "Exception in getSessionStatus(): " . $e->getMessage());
            return $this->response->setStatusCode(500)->setJSON([
                'success' => false,
                'message' => 'Terjadi kesalahan sistem: ' . $e->getMessage()
            ]);
        }
    }

    public function getAllMembers()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(405)->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ]);
        }

        try {
            $memberModel = new MemberModel();
            $members = $memberModel->findAll();

            return $this->response->setJSON([
                'success' => true,
                'members' => $members
            ]);

        } catch (\Exception $e) {
            return $this->response->setStatusCode(500)->setJSON([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ]);
        }
    }

    public function getAllPakets()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(405)->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ]);
        }

        try {
            $paketModel = new PaketModel();
            $pakets = $paketModel->findAll();

            return $this->response->setJSON([
                'success' => true,
                'pakets' => $pakets
            ]);

        } catch (\Exception $e) {
            return $this->response->setStatusCode(500)->setJSON([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ]);
        }
    }

    public function getSesiAktif()
    {
        $ip = $this->request->getIPAddress();
        $stationModel = new \App\Models\StationModel();
        $sesiModel = new \App\Models\SesiModel();
        $station = $stationModel->where('ip_address', $ip)->first();
        
        if (!$station) {
            return $this->response->setJSON(null);
        }

        $sesi = $sesiModel->where('station_id', $station['id'])
                         ->where('status', 'berjalan')
                         ->orderBy('id', 'DESC')
                         ->first();

        if ($sesi) {
            $konsolModel = new \App\Models\KonsolModel();
            $konsol = $konsolModel->find($station['id_konsol']);
            return $this->response->setJSON([
                'waktu_mulai' => $sesi['waktu_mulai'],
                'harga_personal' => $konsol['harga_personal'],
                'sesi_id' => $sesi['id']
            ]);
        }

        return $this->response->setJSON(null);
    }

public function updateHarga()
{
    try {
        // Support both JSON and form data
        $harga = $this->request->getPost('harga_total') ?? $this->request->getJSON(true)['harga_total'] ?? 0;
        $stationId = $this->request->getPost('station_id') ?? $this->request->getJSON(true)['station_id'] ?? null;

        // Debug log
        log_message('debug', "UpdateHarga request - Station ID: {$stationId}, Harga: {$harga}");

        if (!$stationId || !is_numeric($harga)) {
            log_message('error', "UpdateHarga validation failed - Station ID: {$stationId}, Harga: {$harga}");
            return $this->response->setStatusCode(400)->setJSON([
                'success' => false,
                'message' => 'Invalid data: station_id and harga_total required'
            ]);
        }

        $sesiModel = new \App\Models\SesiModel();
        $sesi = $sesiModel->where('station_id', $stationId)
                          ->where('status', 'berjalan')
                          ->orderBy('id', 'DESC')
                          ->first();

        if ($sesi) {
            // Only update if it's a personal session (not member)
            if ($sesi['jenis_user'] === 'personal') {
                $updateResult = $sesiModel->update($sesi['id'], ['harga_total' => $harga]);

                if ($updateResult) {
                    log_message('debug', "UpdateHarga success - Session ID: {$sesi['id']}, New harga: {$harga}");
                    return $this->response->setJSON([
                        'success' => true,
                        'message' => 'Harga updated successfully',
                        'session_id' => $sesi['id'],
                        'new_harga' => $harga
                    ]);
                } else {
                    log_message('error', "UpdateHarga database update failed - Session ID: {$sesi['id']}");
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => 'Database update failed'
                    ]);
                }
            } else {
                log_message('debug', "UpdateHarga skipped - Not a personal session: {$sesi['jenis_user']}");
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Only personal sessions can be updated'
                ]);
            }
        }

        log_message('error', "UpdateHarga no session found - Station ID: {$stationId}");
        return $this->response->setJSON([
            'success' => false,
            'message' => 'No running session found'
        ]);

    } catch (\Exception $e) {
        log_message('error', 'UpdateHarga error: ' . $e->getMessage());
        return $this->response->setStatusCode(500)->setJSON([
            'success' => false,
            'message' => 'Server error: ' . $e->getMessage()
        ]);
    }
}

public function updateMemberSaldo()
{
    $json = $this->request->getJSON(true);
    $stationId = $json['station_id'] ?? null;
    $saldoBaru = $json['saldo_baru'] ?? 0;

    if (!$stationId || !is_numeric($saldoBaru)) {
        return $this->response->setStatusCode(400)->setJSON(['status' => 'invalid data']);
    }

    $sesiModel = new \App\Models\SesiModel();
    $memberModel = new \App\Models\MemberModel();
    
    // Ambil sesi yang berjalan
    $sesi = $sesiModel->where('station_id', $stationId)
                      ->where('status', 'berjalan')
                      ->orderBy('id', 'DESC')
                      ->first();

    if ($sesi && $sesi['id_member']) {
        // Update saldo member di database dan sisa_saldo di sesi
        $memberModel->update($sesi['id_member'], ['saldo' => $saldoBaru]);
        $sesiModel->update($sesi['id'], ['sisa_saldo' => $saldoBaru]);
        return $this->response->setJSON(['status' => 'ok', 'saldo_baru' => $saldoBaru]);
    }

    return $this->response->setJSON(['status' => 'no session or member']);
}

    public function searchMember()
    {
        if ($this->request->getMethod() === 'OPTIONS') {
            return $this->response->setStatusCode(200);
        }

        try {
            $query = $this->request->getGet('q');
            
            if (empty($query) || strlen($query) < 2) {
                return $this->response->setJSON([
                    'success' => true,
                    'members' => []
                ]);
            }

            $memberModel = new MemberModel();
            $members = $memberModel
                ->like('nama', $query)
                ->orLike('id_member', $query)
                ->limit(10)
                ->findAll();

            return $this->response->setJSON([
                'success' => true,
                'members' => $members
            ]);

        } catch (\Exception $e) {
            return $this->response->setStatusCode(500)->setJSON([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ]);
        }
    }

    public function getPaketByKonsol($konsolId = null)
    {
        if ($this->request->getMethod() === 'OPTIONS') {
            return $this->response->setStatusCode(200);
        }

        try {
            if (!$konsolId) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Konsol ID required'
                ]);
            }

            $paketModel = new PaketModel();
            $pakets = $paketModel->getByKonsol($konsolId);

            return $this->response->setJSON([
                'success' => true,
                'pakets' => $pakets
            ]);

        } catch (\Exception $e) {
            return $this->response->setStatusCode(500)->setJSON([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ]);
        }
    }

    public function getAvailableStationsForMove($stationId = null)
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(405)->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ]);
        }

        try {
            if (!$stationId) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Station ID required'
                ]);
            }

            $stationModel = new StationModel();
            $sesiModel = new SesiModel();

            // Get current station info
            $currentStation = $stationModel
                ->select('station.*, konsol.nama_konsol')
                ->join('konsol', 'konsol.id = station.id_konsol')
                ->find($stationId);

            if (!$currentStation) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Station not found'
                ]);
            }

            // Get all stations with same konsol type
            $availableStations = $stationModel
                ->select('station.*, konsol.nama_konsol')
                ->join('konsol', 'konsol.id = station.id_konsol')
                ->where('station.id_konsol', $currentStation['id_konsol'])
                ->where('station.id !=', $stationId)
                ->findAll();

            // Filter out stations that are currently in use
            $runningSessions = $sesiModel
                ->select('station_id')
                ->where('status', 'berjalan')
                ->findAll();

            $usedStationIds = array_column($runningSessions, 'station_id');

            $availableStations = array_filter($availableStations, function($station) use ($usedStationIds) {
                return !in_array($station['id'], $usedStationIds);
            });

            return $this->response->setJSON([
                'success' => true,
                'stations' => array_values($availableStations),
                'current_konsol' => $currentStation['nama_konsol']
            ]);

        } catch (\Exception $e) {
            return $this->response->setStatusCode(500)->setJSON([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ]);
        }
    }

    public function moveSession()
    {
        if (!$this->request->isAJAX() || $this->request->getMethod() !== 'POST') {
            return $this->response->setStatusCode(405)->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ]);
        }

        try {
            $fromStationId = $this->request->getPost('from_station_id');
            $toStationId = $this->request->getPost('to_station_id');

            if (empty($fromStationId) || empty($toStationId)) {
                return $this->response->setStatusCode(400)->setJSON([
                    'success' => false,
                    'message' => 'From and To Station IDs are required'
                ]);
            }

            $sesiModel = new SesiModel();
            $stationModel = new StationModel();

            // Get current running session
            $currentSession = $sesiModel
                ->where('station_id', $fromStationId)
                ->where('status', 'berjalan')
                ->first();

            if (!$currentSession) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'No running session found on source station'
                ]);
            }

            // Verify both stations exist and have same konsol type
            $fromStation = $stationModel
                ->select('station.*, konsol.id as konsol_id')
                ->join('konsol', 'konsol.id = station.id_konsol')
                ->find($fromStationId);

            $toStation = $stationModel
                ->select('station.*, konsol.id as konsol_id')
                ->join('konsol', 'konsol.id = station.id_konsol')
                ->find($toStationId);

            if (!$fromStation || !$toStation) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Station not found'
                ]);
            }

            if ($fromStation['konsol_id'] !== $toStation['konsol_id']) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Stations must have the same konsol type'
                ]);
            }

            // Check if destination station is available
            $existingSession = $sesiModel
                ->where('station_id', $toStationId)
                ->where('status', 'berjalan')
                ->first();

            if ($existingSession) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Destination station is already in use'
                ]);
            }

            // Update session to new station
            $updated = $sesiModel->update($currentSession['id'], [
                'station_id' => $toStationId
            ]);

            if ($updated) {
                log_message('info', "Session {$currentSession['id']} moved from station {$fromStationId} to station {$toStationId}");
                
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Session moved successfully',
                    'session_id' => $currentSession['id'],
                    'from_station' => $fromStationId,
                    'to_station' => $toStationId
                ]);
            } else {
                return $this->response->setStatusCode(500)->setJSON([
                    'success' => false,
                    'message' => 'Failed to update session'
                ]);
            }

        } catch (\Exception $e) {
            log_message('error', "Exception in moveSession(): " . $e->getMessage());
            return $this->response->setStatusCode(500)->setJSON([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ]);
        }
    }

    public function addPackageToSession()
    {
        if (!$this->request->isAJAX() || $this->request->getMethod() !== 'POST') {
            return $this->response->setStatusCode(405)->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ]);
        }

        try {
            $stationId = $this->request->getPost('station_id');
            $paketId = $this->request->getPost('paket_id');

            if (empty($stationId) || empty($paketId)) {
                return $this->response->setStatusCode(400)->setJSON([
                    'success' => false,
                    'message' => 'Station ID and Package ID are required'
                ]);
            }

            $sesiModel = new SesiModel();
            $paketModel = new PaketModel();
            $memberModel = new MemberModel();

            // Get current running session
            $currentSession = $sesiModel
                ->where('station_id', $stationId)
                ->where('status', 'berjalan')
                ->first();

            if (!$currentSession) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'No running session found'
                ]);
            }

            // Verify it's a member session
            if ($currentSession['jenis_user'] !== 'member' || empty($currentSession['id_member'])) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Only member sessions can add packages'
                ]);
            }

            // Get package details
            $paket = $paketModel->find($paketId);
            if (!$paket) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Package not found'
                ]);
            }

            // Get member details
            $member = $memberModel->find($currentSession['id_member']);
            if (!$member) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Member not found'
                ]);
            }

            // Check if member has enough saldo
            if ($member['saldo'] < $paket['harga']) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Saldo tidak cukup untuk paket ini'
                ]);
            }

            // Deduct saldo
            $memberModel->update($member['id'], [
                'saldo' => $member['saldo'] - $paket['harga']
            ]);

            // Parse package duration with more accurate handling
            $durasi = $paket['durasi'];
            if (strpos($durasi, ':') !== false) {
                // Format is "HH:MM:SS" or "HH:MM"
                $parts = explode(':', $durasi);

                if (count($parts) == 3) {
                    // HH:MM:SS format
                    $hours = intval($parts[0]);
                    $minutes = intval($parts[1]);
                    $seconds = intval($parts[2]);
                } else {
                    // HH:MM format
                    $hours = intval($parts[0]);
                    $minutes = intval($parts[1]);
                    $seconds = 0;
                }

                // Calculate total seconds
                $additionalDurasiSeconds = ($hours * 3600) + ($minutes * 60) + $seconds;

                log_message('debug', "Parsed duration - Hours: {$hours}, Minutes: {$minutes}, Seconds: {$seconds}, Total seconds: {$additionalDurasiSeconds}");
            } else {
                // Plain minutes format
                $minutes = intval($durasi);
                $additionalDurasiSeconds = $minutes * 60;

                log_message('debug', "Parsed duration - Minutes: {$minutes}, Total seconds: {$additionalDurasiSeconds}");
            }

            // Simply add the new package duration to the stored duration
            // The stored durasi_sisa already represents the total time allocated to this session
            $storedDurasiSisa = intval($currentSession['durasi_sisa'] ?? 0);
            $newDurasiSisa = $storedDurasiSisa + $additionalDurasiSeconds;

            log_message('debug', "Add package - Stored duration: {$storedDurasiSisa}s, Additional: {$additionalDurasiSeconds}s, New total: {$newDurasiSisa}s");

            // Keep the original package ID and just add the duration
            $updated = $sesiModel->update($currentSession['id'], [
                'durasi_sisa' => $newDurasiSisa,
                'harga_total' => $currentSession['harga_total'] + $paket['harga']
            ]);

            // Log for debugging
            log_message('debug', "Package added - Station: {$stationId}, Stored duration: {$storedDurasiSisa}s, Additional: {$additionalDurasiSeconds}s, New total: {$newDurasiSisa}s");

            if ($updated) {
                log_message('info', "Package {$paketId} added to session {$currentSession['id']} on station {$stationId}");
                
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Paket berhasil ditambahkan',
                    'package_name' => $paket['nama_paket'],
                    'new_duration' => $newDurasiSisa,
                    'additional_duration' => $additionalDurasiSeconds,
                    'new_saldo' => $member['saldo'] - $paket['harga']
                ]);
            } else {
                return $this->response->setStatusCode(500)->setJSON([
                    'success' => false,
                    'message' => 'Failed to update session'
                ]);
            }

        } catch (\Exception $e) {
            log_message('error', "Exception in addPackageToSession(): " . $e->getMessage());
            return $this->response->setStatusCode(500)->setJSON([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ]);
        }
    }

    public function getSessionInfo($stationId)
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(405)->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ]);
        }

        try {
            $sesiModel = new SesiModel();
            $memberModel = new MemberModel();

            // Get specific station session with details
            $session = $sesiModel
                ->select('sesi.*, member.nama as nama_member, member.saldo as member_saldo, paket.nama_paket, paket.durasi, konsol.harga_personal, konsol.harga_member, station.nama_station')
                ->join('member', 'member.id = sesi.id_member', 'left')
                ->join('paket', 'paket.id = sesi.id_paket', 'left')
                ->join('station', 'station.id = sesi.station_id')
                ->join('konsol', 'konsol.id = station.id_konsol')
                ->where('sesi.station_id', $stationId)
                ->where('sesi.status', 'berjalan')
                ->first();

            if ($session && $session['jenis_user'] === 'member') {
                if (empty($session['id_paket'])) {
                    // Handle non-package member session - calculate saldo based on stored initial saldo and elapsed time
                    $waktuMulai = strtotime($session['waktu_mulai']);
                    $now = time();
                    $elapsedSeconds = $now - $waktuMulai;

                    $hargaPerJam = $session['harga_member'] ?? 0;
                    $hargaPerMenit = round($hargaPerJam / 60);
                    $hargaPerDetik = $hargaPerMenit / 60;

                    // Get initial saldo from session start (stored in harga_total for non-package sessions)
                    $initialSaldo = $session['harga_total'] ?? 0;

                    // If harga_total is 0, this might be the first call, get from member table
                    if ($initialSaldo == 0) {
                        $member = $memberModel->find($session['id_member']);
                        $initialSaldo = $member ? $member['saldo'] : 0;

                        // Store initial saldo in harga_total for future reference
                        $sesiModel->update($session['id'], ['harga_total' => $initialSaldo]);
                        log_message('debug', "Stored initial saldo {$initialSaldo} for member non-paket session {$session['id']}");
                    }

                    $totalDeduction = $hargaPerDetik * $elapsedSeconds;
                    $currentSaldo = max(0, $initialSaldo - $totalDeduction);
                    $session['member_saldo'] = $currentSaldo;

                    // Calculate remaining duration based on current saldo
                    $remainingSeconds = floor($currentSaldo / $hargaPerDetik);

                    // Update durasi_sisa based on remaining saldo
                    $session['durasi_sisa'] = $remainingSeconds;

                    log_message('debug', "Member non-paket {$session['id_member']} - Initial saldo: {$initialSaldo}, Elapsed: {$elapsedSeconds}s, Deducted: {$totalDeduction}, Current saldo: {$currentSaldo}, Remaining: {$remainingSeconds}s");

                    // If saldo is 0 or negative, stop the session
                    if ($currentSaldo <= 0) {
                        $memberModel->update($session['id_member'], ['saldo' => 0]);
                        $sesiModel->update($session['id'], [
                            'status' => 'selesai',
                            'waktu_berhenti' => date('Y-m-d H:i:s'),
                            'harga_total' => $initialSaldo // Total amount deducted
                        ]);
                        log_message('debug', "Auto-stopped session {$session['id']} due to insufficient saldo");
                    }

                } else {
                    // Handle package member session
                    $waktuMulai = strtotime($session['waktu_mulai']);
                    $now = time();
                    $elapsedSeconds = $now - $waktuMulai;

                    $storedDurasiSisa = intval($session['durasi_sisa'] ?? 0);
                    $adjustedDurasiSisa = max(0, $storedDurasiSisa - $elapsedSeconds);
                    $session['durasi_sisa'] = $adjustedDurasiSisa;
                }
            }

            if ($session) {
                return $this->response->setJSON([
                    'success' => true,
                    'session' => $session
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Session not found'
                ]);
            }

        } catch (\Exception $e) {
            log_message('error', "Exception in getSessionInfo(): " . $e->getMessage());
            return $this->response->setStatusCode(500)->setJSON([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ]);
        }
    }

    public function updateMemberNonPackageSaldo()
    {
        if (!$this->request->isAJAX() || $this->request->getMethod() !== 'POST') {
            return $this->response->setStatusCode(405)->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ]);
        }

        try {
            $stationId = $this->request->getPost('station_id');

            if (!$stationId) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Station ID diperlukan'
                ]);
            }

            $sesiModel = new SesiModel();
            $memberModel = new MemberModel();
            $stationModel = new StationModel();

            // Get current running session
            $session = $sesiModel
                ->where('station_id', $stationId)
                ->where('status', 'berjalan')
                ->first();

            if (!$session || $session['jenis_user'] !== 'member' || !empty($session['id_paket'])) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Bukan sesi member non-paket'
                ]);
            }

            // Get station and konsol info for pricing
            $station = $stationModel
                ->select('station.*, konsol.harga_member')
                ->join('konsol', 'konsol.id = station.id_konsol')
                ->where('station.id', $stationId)
                ->first();

            $hargaPerJam = $station['harga_member'] ?? 0;
            $hargaPerMenit = round($hargaPerJam / 60);
            $hargaPerDetik = $hargaPerMenit / 60;

            // Calculate elapsed time
            $waktuMulai = strtotime($session['waktu_mulai']);
            $now = time();
            $elapsedSeconds = $now - $waktuMulai;

            // Get initial saldo (stored in harga_total for non-package sessions)
            $initialSaldo = $session['harga_total'] ?? 0;

            // If harga_total is 0, get from member table and store it
            if ($initialSaldo == 0) {
                $member = $memberModel->find($session['id_member']);
                $initialSaldo = $member ? $member['saldo'] : 0;

                // Store initial saldo in harga_total for future reference
                $sesiModel->update($session['id'], ['harga_total' => $initialSaldo]);
            }

            // Calculate current saldo
            $totalDeduction = $hargaPerDetik * $elapsedSeconds;
            $currentSaldo = max(0, $initialSaldo - $totalDeduction);

            // Update member saldo in database
            $memberModel->update($session['id_member'], ['saldo' => $currentSaldo]);

            // Calculate remaining duration
            $remainingSeconds = floor($currentSaldo / $hargaPerDetik);

            // Update session durasi_sisa
            $sesiModel->update($session['id'], ['durasi_sisa' => $remainingSeconds]);

            log_message('debug', "Updated member {$session['id_member']} saldo to {$currentSaldo} (elapsed: {$elapsedSeconds}s, deducted: {$totalDeduction})");

            // If saldo is 0 or negative, stop the session
            if ($currentSaldo <= 0) {
                $sesiModel->update($session['id'], [
                    'status' => 'selesai',
                    'waktu_berhenti' => date('Y-m-d H:i:s'),
                    'harga_total' => $initialSaldo // Total amount deducted
                ]);
                log_message('debug', "Auto-stopped session {$session['id']} due to insufficient saldo");

                return $this->response->setJSON([
                    'success' => true,
                    'session_stopped' => true,
                    'current_saldo' => 0,
                    'remaining_seconds' => 0
                ]);
            }

            return $this->response->setJSON([
                'success' => true,
                'current_saldo' => $currentSaldo,
                'remaining_seconds' => $remainingSeconds,
                'elapsed_seconds' => $elapsedSeconds,
                'total_deduction' => $totalDeduction
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error updating member non-package saldo: ' . $e->getMessage());
            return $this->response->setStatusCode(500)->setJSON([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ]);
        }
    }


}