<?= $this->extend('layouts/main') ?>

<?= $this->section('title') ?>Manajemen Stok Produk<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- CSS dan JS Dependencies -->
<link href="<?= base_url('assets/sweetalert2/sweetalert2.min.css') ?>" rel="stylesheet">
<script src="<?= base_url('assets/sweetalert2/sweetalert2.all.min.js') ?>"></script>
<link rel="stylesheet" href="<?= base_url('assets/css/bootstrap-icons.min.css') ?>">
<link rel="stylesheet" href="<?= base_url('assets/css/datatables.min.css') ?>">
<script src="<?= base_url('assets/js/datatables.min.js') ?>"></script>

<style>
.product-stats {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    color: white;
    padding: 20px;
    margin-bottom: 20px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.product-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;
}

.product-card:hover {
    transform: translateY(-2px);
}

.stock-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
}

.stock-high {
    background-color: #28a745;
    color: white;
}

.stock-medium {
    background-color: #ffc107;
    color: #212529;
}

.stock-low {
    background-color: #dc3545;
    color: white;
}

.btn-action {
    border-radius: 8px;
    padding: 8px 15px;
    margin: 2px;
}

.table-responsive {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modal-content {
    border-radius: 15px;
    border: none;
}

.form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}
</style>

<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <!-- Header -->
      <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h2 class="mb-1">
            <i class="bi bi-box-seam text-primary me-2"></i>Manajemen Stok Produk
          </h2>
          <p class="text-muted mb-0">Kelola produk dan stok untuk penjualan kasir</p>
        </div>
        <div>
          <button class="btn btn-primary" onclick="showAddProductModal()">
            <i class="bi bi-plus-circle me-1"></i>Tambah Produk
          </button>
          <a href="<?= base_url('setting') ?>" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left me-1"></i>Kembali
          </a>
        </div>
      </div>

      <!-- Statistics Cards -->
      <div class="product-stats">
        <div class="row">
          <div class="col-md-3">
            <div class="stat-item">
              <div class="stat-number" id="totalProduk">0</div>
              <div class="stat-label">Total Produk</div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="stat-item">
              <div class="stat-number" id="totalStok">0</div>
              <div class="stat-label">Total Stok</div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="stat-item">
              <div class="stat-number" id="stokRendah">0</div>
              <div class="stat-label">Stok Rendah</div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="stat-item">
              <div class="stat-number" id="totalKategori">0</div>
              <div class="stat-label">Kategori</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Filter dan Search -->
      <div class="card mb-4">
        <div class="card-body">
          <div class="row g-3">
            <div class="col-md-4">
              <label class="form-label">Cari Produk</label>
              <input type="text" class="form-control" id="searchProduk" placeholder="Nama atau kode produk...">
            </div>
            <div class="col-md-3">
              <label class="form-label">Filter Kategori</label>
              <select class="form-select" id="filterKategori">
                <option value="">Semua Kategori</option>
              </select>
            </div>
            <div class="col-md-3">
              <label class="form-label">Filter Stok</label>
              <select class="form-select" id="filterStok">
                <option value="">Semua Stok</option>
                <option value="rendah">Stok Rendah (≤10)</option>
                <option value="sedang">Stok Sedang (11-50)</option>
                <option value="tinggi">Stok Tinggi (>50)</option>
              </select>
            </div>
            <div class="col-md-2">
              <label class="form-label">&nbsp;</label>
              <button class="btn btn-outline-primary w-100" onclick="refreshData()">
                <i class="bi bi-arrow-clockwise me-1"></i>Refresh
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Tabel Produk -->
      <div class="card">
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover" id="tableProduk">
              <thead class="table-dark">
                <tr>
                  <th>Kode</th>
                  <th>Nama Produk</th>
                  <th>Kategori</th>
                  <th>Harga Beli</th>
                  <th>Harga Jual</th>
                  <th>Stok</th>
                  <th>Status Stok</th>
                  <th>Aksi</th>
                </tr>
              </thead>
              <tbody id="produkTableBody">
                <!-- Data akan dimuat via AJAX -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal Tambah/Edit Produk -->
<div class="modal fade" id="modalProduk" tabindex="-1" aria-labelledby="modalProdukLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header bg-gradient-primary text-white">
        <h5 class="modal-title" id="modalProdukLabel">
          <i class="bi bi-box-seam me-2"></i>Tambah Produk
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="formProduk">
          <input type="hidden" id="produkId" name="produk_id">
          
          <div class="row g-3">
            <div class="col-md-6">
              <label for="kodeProduk" class="form-label">
                <i class="bi bi-upc-scan me-1"></i>Kode Produk
              </label>
              <input type="text" class="form-control" id="kodeProduk" name="kode_produk" required>
              <div class="form-text">Kode unik untuk identifikasi produk</div>
            </div>

            <div class="col-md-6">
              <label for="namaProduk" class="form-label">
                <i class="bi bi-tag me-1"></i>Nama Produk
              </label>
              <input type="text" class="form-control" id="namaProduk" name="nama_produk" required>
            </div>

            <div class="col-md-6">
              <label for="kategoriProduk" class="form-label">
                <i class="bi bi-collection me-1"></i>Kategori
              </label>
              <div class="input-group">
                <select class="form-select" id="kategoriProduk" name="kategori" required>
                  <option value="">Pilih Kategori</option>
                  <!-- Options will be loaded dynamically -->
                </select>
                <button type="button" class="btn btn-outline-primary" onclick="showAddKategoriModal()">
                  <i class="bi bi-plus"></i>
                </button>
              </div>
              <div class="form-text">Pilih kategori atau tambah kategori baru</div>
            </div>

            <div class="col-md-6">
              <label for="barcodeProduk" class="form-label">
                <i class="bi bi-upc me-1"></i>Barcode (Opsional)
              </label>
              <input type="text" class="form-control" id="barcodeProduk" name="barcode">
            </div>

            <div class="col-md-4">
              <label for="hargaBeli" class="form-label">
                <i class="bi bi-currency-dollar me-1"></i>Harga Beli
              </label>
              <input type="number" class="form-control" id="hargaBeli" name="harga_beli" min="0" step="100" required>
            </div>

            <div class="col-md-4">
              <label for="hargaJual" class="form-label">
                <i class="bi bi-cash me-1"></i>Harga Jual
              </label>
              <input type="number" class="form-control" id="hargaJual" name="harga_jual" min="0" step="100" required>
            </div>

            <div class="col-md-4">
              <label for="stokProduk" class="form-label">
                <i class="bi bi-box me-1"></i>Stok Awal
              </label>
              <input type="number" class="form-control" id="stokProduk" name="stok" min="0" required>
            </div>

            <div class="col-12">
              <label for="deskripsiProduk" class="form-label">
                <i class="bi bi-card-text me-1"></i>Deskripsi (Opsional)
              </label>
              <textarea class="form-control" id="deskripsiProduk" name="deskripsi" rows="3"></textarea>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          <i class="bi bi-x-circle me-1"></i>Batal
        </button>
        <button type="button" class="btn btn-primary" onclick="saveProduk()">
          <i class="bi bi-check-circle me-1"></i>Simpan
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Modal Update Stok -->
<div class="modal fade" id="modalUpdateStok" tabindex="-1" aria-labelledby="modalUpdateStokLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header bg-gradient-warning text-dark">
        <h5 class="modal-title" id="modalUpdateStokLabel">
          <i class="bi bi-arrow-up-circle me-2"></i>Update Stok
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="formUpdateStok">
          <input type="hidden" id="updateStokId">
          
          <div class="mb-3">
            <label class="form-label">Produk</label>
            <input type="text" class="form-control" id="updateStokNama" readonly>
          </div>

          <div class="mb-3">
            <label class="form-label">Stok Saat Ini</label>
            <input type="text" class="form-control" id="updateStokLama" readonly>
          </div>

          <div class="mb-3">
            <label for="updateStokBaru" class="form-label">Stok Baru</label>
            <input type="number" class="form-control" id="updateStokBaru" name="stok" min="0" required>
          </div>

          <div class="mb-3">
            <label for="updateStokKeterangan" class="form-label">Keterangan</label>
            <textarea class="form-control" id="updateStokKeterangan" name="keterangan" rows="2" placeholder="Alasan update stok..."></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          <i class="bi bi-x-circle me-1"></i>Batal
        </button>
        <button type="button" class="btn btn-warning" onclick="saveUpdateStok()">
          <i class="bi bi-check-circle me-1"></i>Update Stok
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Modal Tambah Kategori -->
<div class="modal fade" id="modalTambahKategori" tabindex="-1" aria-labelledby="modalTambahKategoriLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header bg-gradient-info text-white">
        <h5 class="modal-title" id="modalTambahKategoriLabel">
          <i class="bi bi-plus-circle me-2"></i>Tambah Kategori Baru
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="formTambahKategori">
          <div class="mb-3">
            <label for="namaKategoriBaru" class="form-label">
              <i class="bi bi-collection me-1"></i>Nama Kategori
            </label>
            <input type="text" class="form-control" id="namaKategoriBaru" name="nama_kategori" required>
            <div class="form-text">Contoh: Makanan, Minuman, Snack, Aksesoris</div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          <i class="bi bi-x-circle me-1"></i>Batal
        </button>
        <button type="button" class="btn btn-info" onclick="saveKategoriBaru()">
          <i class="bi bi-check-circle me-1"></i>Tambah Kategori
        </button>
      </div>
    </div>
  </div>
</div>

<script>
let produkData = [];
let isEditMode = false;

// Load data saat halaman dimuat
document.addEventListener('DOMContentLoaded', function() {
  loadProdukData();
  loadKategoriData();
  setupEventListeners();
});

// Setup event listeners
function setupEventListeners() {
  // Search dan filter
  document.getElementById('searchProduk').addEventListener('input', filterTable);
  document.getElementById('filterKategori').addEventListener('change', filterTable);
  document.getElementById('filterStok').addEventListener('change', filterTable);
  
  // Auto calculate profit margin
  document.getElementById('hargaBeli').addEventListener('input', calculateMargin);
  document.getElementById('hargaJual').addEventListener('input', calculateMargin);

  // Auto generate kode produk when category changes
  document.getElementById('kategoriProduk').addEventListener('change', function() {
    if (this.value && !isEditMode) {
      generateKodeProduk();
    }
  });
}

// Load data produk
function loadProdukData() {
  fetch('<?= base_url('produk/getAllProduk') ?>')
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        produkData = data.data;
        updateStatistics();
        updateKategoriFilter();
        renderTable();
      } else {
        Swal.fire('Error', data.message, 'error');
      }
    })
    .catch(error => {
      console.error('Error:', error);
      Swal.fire('Error', 'Gagal memuat data produk', 'error');
    });
}

// Update statistik
function updateStatistics() {
  const totalProduk = produkData.length;
  const totalStok = produkData.reduce((sum, item) => sum + parseInt(item.stok), 0);
  const stokRendah = produkData.filter(item => parseInt(item.stok) <= 10).length;
  const totalKategori = [...new Set(produkData.map(item => item.kategori))].length;

  document.getElementById('totalProduk').textContent = totalProduk;
  document.getElementById('totalStok').textContent = totalStok.toLocaleString('id-ID').replace(/,/g, '.');
  document.getElementById('stokRendah').textContent = stokRendah;
  document.getElementById('totalKategori').textContent = totalKategori;
}

// Update filter kategori
function updateKategoriFilter() {
  const kategoriSet = new Set(produkData.map(item => item.kategori));
  const filterKategori = document.getElementById('filterKategori');

  // Clear existing options except first
  filterKategori.innerHTML = '<option value="">Semua Kategori</option>';

  kategoriSet.forEach(kategori => {
    const option = document.createElement('option');
    option.value = kategori;
    option.textContent = kategori;
    filterKategori.appendChild(option);
  });
}

// Render tabel
function renderTable() {
  const tbody = document.getElementById('produkTableBody');
  tbody.innerHTML = '';

  produkData.forEach(produk => {
    const stok = parseInt(produk.stok);
    let stockBadge = '';
    let stockClass = '';

    if (stok <= 10) {
      stockBadge = 'Rendah';
      stockClass = 'badge bg-danger';
    } else if (stok <= 50) {
      stockBadge = 'Sedang';
      stockClass = 'badge bg-warning text-dark';
    } else {
      stockBadge = 'Tinggi';
      stockClass = 'badge bg-success';
    }

    const row = `
      <tr>
        <td><strong>${produk.kode_produk}</strong></td>
        <td>${produk.nama_produk}</td>
        <td><span class="badge bg-secondary">${produk.kategori}</span></td>
        <td>Rp ${parseInt(produk.harga_beli).toLocaleString('id-ID').replace(/,/g, '.')}</td>
        <td>Rp ${parseInt(produk.harga_jual).toLocaleString('id-ID').replace(/,/g, '.')}</td>
        <td><strong>${stok}</strong></td>
        <td><span class="${stockClass}">${stockBadge}</span></td>
        <td>
          <button class="btn btn-sm btn-warning btn-action" onclick="showUpdateStokModal(${produk.id})" title="Update Stok">
            <i class="bi bi-arrow-up-circle"></i>
          </button>
          <button class="btn btn-sm btn-primary btn-action" onclick="editProduk(${produk.id})" title="Edit">
            <i class="bi bi-pencil"></i>
          </button>
          <button class="btn btn-sm btn-danger btn-action" onclick="hapusProduk(${produk.id})" title="Hapus">
            <i class="bi bi-trash"></i>
          </button>
        </td>
      </tr>
    `;
    tbody.innerHTML += row;
  });
}

// Filter tabel
function filterTable() {
  const searchTerm = document.getElementById('searchProduk').value.toLowerCase();
  const kategoriFilter = document.getElementById('filterKategori').value;
  const stokFilter = document.getElementById('filterStok').value;

  let filteredData = produkData;

  // Filter berdasarkan search
  if (searchTerm) {
    filteredData = filteredData.filter(item =>
      item.nama_produk.toLowerCase().includes(searchTerm) ||
      item.kode_produk.toLowerCase().includes(searchTerm)
    );
  }

  // Filter berdasarkan kategori
  if (kategoriFilter) {
    filteredData = filteredData.filter(item => item.kategori === kategoriFilter);
  }

  // Filter berdasarkan stok
  if (stokFilter) {
    filteredData = filteredData.filter(item => {
      const stok = parseInt(item.stok);
      switch (stokFilter) {
        case 'rendah': return stok <= 10;
        case 'sedang': return stok > 10 && stok <= 50;
        case 'tinggi': return stok > 50;
        default: return true;
      }
    });
  }

  // Render filtered data
  const tbody = document.getElementById('produkTableBody');
  tbody.innerHTML = '';

  filteredData.forEach(produk => {
    const stok = parseInt(produk.stok);
    let stockBadge = '';
    let stockClass = '';

    if (stok <= 10) {
      stockBadge = 'Rendah';
      stockClass = 'badge bg-danger';
    } else if (stok <= 50) {
      stockBadge = 'Sedang';
      stockClass = 'badge bg-warning text-dark';
    } else {
      stockBadge = 'Tinggi';
      stockClass = 'badge bg-success';
    }

    const row = `
      <tr>
        <td><strong>${produk.kode_produk}</strong></td>
        <td>${produk.nama_produk}</td>
        <td><span class="badge bg-secondary">${produk.kategori}</span></td>
        <td>Rp ${parseInt(produk.harga_beli).toLocaleString('id-ID').replace(/,/g, '.')}</td>
        <td>Rp ${parseInt(produk.harga_jual).toLocaleString('id-ID').replace(/,/g, '.')}</td>
        <td><strong>${stok}</strong></td>
        <td><span class="${stockClass}">${stockBadge}</span></td>
        <td>
          <button class="btn btn-sm btn-warning btn-action" onclick="showUpdateStokModal(${produk.id})" title="Update Stok">
            <i class="bi bi-arrow-up-circle"></i>
          </button>
          <button class="btn btn-sm btn-primary btn-action" onclick="editProduk(${produk.id})" title="Edit">
            <i class="bi bi-pencil"></i>
          </button>
          <button class="btn btn-sm btn-danger btn-action" onclick="hapusProduk(${produk.id})" title="Hapus">
            <i class="bi bi-trash"></i>
          </button>
        </td>
      </tr>
    `;
    tbody.innerHTML += row;
  });
}

// Show modal tambah produk
function showAddProductModal() {
  isEditMode = false;
  document.getElementById('modalProdukLabel').innerHTML = '<i class="bi bi-box-seam me-2"></i>Tambah Produk';
  document.getElementById('formProduk').reset();
  document.getElementById('produkId').value = '';

  const modal = new bootstrap.Modal(document.getElementById('modalProduk'));
  modal.show();
}

// Edit produk
function editProduk(id) {
  const produk = produkData.find(item => item.id == id);
  if (!produk) {
    Swal.fire('Error', 'Produk tidak ditemukan', 'error');
    return;
  }

  isEditMode = true;
  document.getElementById('modalProdukLabel').innerHTML = '<i class="bi bi-pencil me-2"></i>Edit Produk';

  // Fill form
  document.getElementById('produkId').value = produk.id;
  document.getElementById('kodeProduk').value = produk.kode_produk;
  document.getElementById('namaProduk').value = produk.nama_produk;

  // Set kategori dropdown
  const kategoriSelect = document.getElementById('kategoriProduk');
  let kategoriFound = false;
  for (let option of kategoriSelect.options) {
    if (option.value === produk.kategori) {
      option.selected = true;
      kategoriFound = true;
      break;
    }
  }

  // If category not found in dropdown, add it
  if (!kategoriFound && produk.kategori) {
    const option = document.createElement('option');
    option.value = produk.kategori;
    option.textContent = produk.kategori;
    option.selected = true;
    kategoriSelect.appendChild(option);
  }

  document.getElementById('barcodeProduk').value = produk.barcode || '';
  document.getElementById('hargaBeli').value = produk.harga_beli;
  document.getElementById('hargaJual').value = produk.harga_jual;
  document.getElementById('stokProduk').value = produk.stok;
  document.getElementById('deskripsiProduk').value = produk.deskripsi || '';

  const modal = new bootstrap.Modal(document.getElementById('modalProduk'));
  modal.show();
}

// Simpan produk
function saveProduk() {
  const form = document.getElementById('formProduk');
  const formData = new FormData(form);

  // Validasi
  if (!form.checkValidity()) {
    form.reportValidity();
    return;
  }

  const url = isEditMode ?
    `<?= base_url('produk/update') ?>/${document.getElementById('produkId').value}` :
    '<?= base_url('produk/simpan') ?>';

  Swal.fire({
    title: 'Menyimpan...',
    text: 'Sedang menyimpan data produk',
    allowOutsideClick: false,
    didOpen: () => {
      Swal.showLoading();
    }
  });

  fetch(url, {
    method: 'POST',
    body: formData,
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => response.json())
  .then(data => {
    Swal.close();
    if (data.success) {
      Swal.fire({
        title: 'Berhasil!',
        text: data.message,
        icon: 'success',
        confirmButtonText: 'OK'
      }).then(() => {
        const modal = bootstrap.Modal.getInstance(document.getElementById('modalProduk'));
        modal.hide();
        loadProdukData(); // Reload data
      });
    } else {
      if (data.errors) {
        let errorMsg = '';
        for (let field in data.errors) {
          errorMsg += data.errors[field] + '\n';
        }
        Swal.fire('Validasi Gagal', errorMsg, 'error');
      } else {
        Swal.fire('Error', data.message, 'error');
      }
    }
  })
  .catch(error => {
    Swal.close();
    console.error('Error:', error);
    Swal.fire('Error', 'Terjadi kesalahan sistem', 'error');
  });
}

// Hapus produk
function hapusProduk(id) {
  const produk = produkData.find(item => item.id == id);
  if (!produk) {
    Swal.fire('Error', 'Produk tidak ditemukan', 'error');
    return;
  }

  Swal.fire({
    title: 'Konfirmasi Hapus',
    text: `Yakin ingin menghapus produk "${produk.nama_produk}"?`,
    icon: 'warning',
    showCancelButton: true,
    confirmButtonColor: '#d33',
    cancelButtonColor: '#3085d6',
    confirmButtonText: 'Ya, Hapus!',
    cancelButtonText: 'Batal'
  }).then((result) => {
    if (result.isConfirmed) {
      fetch(`<?= base_url('produk/hapus') ?>/${id}`, {
        method: 'POST',
        headers: {
          'X-Requested-With': 'XMLHttpRequest'
        }
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          Swal.fire('Berhasil!', data.message, 'success');
          loadProdukData(); // Reload data
        } else {
          Swal.fire('Error', data.message, 'error');
        }
      })
      .catch(error => {
        console.error('Error:', error);
        Swal.fire('Error', 'Terjadi kesalahan sistem', 'error');
      });
    }
  });
}

// Show modal update stok
function showUpdateStokModal(id) {
  const produk = produkData.find(item => item.id == id);
  if (!produk) {
    Swal.fire('Error', 'Produk tidak ditemukan', 'error');
    return;
  }

  document.getElementById('updateStokId').value = produk.id;
  document.getElementById('updateStokNama').value = produk.nama_produk;
  document.getElementById('updateStokLama').value = produk.stok;
  document.getElementById('updateStokBaru').value = produk.stok;
  document.getElementById('updateStokKeterangan').value = '';

  const modal = new bootstrap.Modal(document.getElementById('modalUpdateStok'));
  modal.show();
}

// Simpan update stok
function saveUpdateStok() {
  const id = document.getElementById('updateStokId').value;
  const stokBaru = document.getElementById('updateStokBaru').value;
  const keterangan = document.getElementById('updateStokKeterangan').value;

  if (!stokBaru || stokBaru < 0) {
    Swal.fire('Error', 'Stok harus berupa angka positif', 'error');
    return;
  }

  const formData = new FormData();
  formData.append('stok', stokBaru);
  formData.append('keterangan', keterangan);

  Swal.fire({
    title: 'Mengupdate...',
    text: 'Sedang mengupdate stok produk',
    allowOutsideClick: false,
    didOpen: () => {
      Swal.showLoading();
    }
  });

  fetch(`<?= base_url('produk/updateStok') ?>/${id}`, {
    method: 'POST',
    body: formData,
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => response.json())
  .then(data => {
    Swal.close();
    if (data.success) {
      Swal.fire({
        title: 'Berhasil!',
        text: data.message,
        icon: 'success',
        confirmButtonText: 'OK'
      }).then(() => {
        const modal = bootstrap.Modal.getInstance(document.getElementById('modalUpdateStok'));
        modal.hide();
        loadProdukData(); // Reload data
      });
    } else {
      Swal.fire('Error', data.message, 'error');
    }
  })
  .catch(error => {
    Swal.close();
    console.error('Error:', error);
    Swal.fire('Error', 'Terjadi kesalahan sistem', 'error');
  });
}

// Calculate profit margin
function calculateMargin() {
  const hargaBeli = parseFloat(document.getElementById('hargaBeli').value) || 0;
  const hargaJual = parseFloat(document.getElementById('hargaJual').value) || 0;

  if (hargaBeli > 0 && hargaJual > 0) {
    const margin = ((hargaJual - hargaBeli) / hargaBeli * 100).toFixed(1);
    const profit = hargaJual - hargaBeli;

    // You can add a margin display element if needed
    console.log(`Margin: ${margin}%, Profit: Rp ${profit.toLocaleString()}`);
  }
}

// Refresh data
function refreshData() {
  Swal.fire({
    title: 'Memuat ulang...',
    text: 'Sedang memuat ulang data produk',
    allowOutsideClick: false,
    didOpen: () => {
      Swal.showLoading();
    }
  });

  loadProdukData();

  setTimeout(() => {
    Swal.close();
    Swal.fire({
      title: 'Berhasil!',
      text: 'Data berhasil dimuat ulang',
      icon: 'success',
      timer: 1500,
      showConfirmButton: false
    });
  }, 1000);
}

// Format currency input
function formatCurrency(input) {
  let value = input.value.replace(/[^\d]/g, '');
  if (value) {
    input.value = parseInt(value).toLocaleString();
  }
}

// Load kategori data
function loadKategoriData() {
  fetch('<?= base_url('produk/getKategori') ?>')
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        updateKategoriDropdown(data.data);
      } else {
        console.error('Failed to load categories:', data.message);
      }
    })
    .catch(error => {
      console.error('Error loading categories:', error);
    });
}

// Update kategori dropdown
function updateKategoriDropdown(categories) {
  const select = document.getElementById('kategoriProduk');

  // Clear existing options except first
  select.innerHTML = '<option value="">Pilih Kategori</option>';

  // Add categories
  categories.forEach(kategori => {
    const option = document.createElement('option');
    option.value = kategori;
    option.textContent = kategori;
    select.appendChild(option);
  });
}

// Show modal tambah kategori
function showAddKategoriModal() {
  document.getElementById('namaKategoriBaru').value = '';
  const modal = new bootstrap.Modal(document.getElementById('modalTambahKategori'));
  modal.show();
}

// Save kategori baru
function saveKategoriBaru() {
  const namaKategori = document.getElementById('namaKategoriBaru').value.trim();

  if (!namaKategori) {
    Swal.fire('Error', 'Nama kategori harus diisi', 'error');
    return;
  }

  // Check if category already exists
  const existingOptions = Array.from(document.getElementById('kategoriProduk').options);
  const exists = existingOptions.some(option => option.value.toLowerCase() === namaKategori.toLowerCase());

  if (exists) {
    Swal.fire('Error', 'Kategori sudah ada', 'error');
    return;
  }

  // Add to dropdown
  const select = document.getElementById('kategoriProduk');
  const option = document.createElement('option');
  option.value = namaKategori;
  option.textContent = namaKategori;
  option.selected = true;
  select.appendChild(option);

  // Close modal
  const modal = bootstrap.Modal.getInstance(document.getElementById('modalTambahKategori'));
  modal.hide();

  Swal.fire({
    title: 'Berhasil!',
    text: `Kategori "${namaKategori}" berhasil ditambahkan`,
    icon: 'success',
    timer: 1500,
    showConfirmButton: false
  });
}

// Auto-generate kode produk
function generateKodeProduk() {
  const kategori = document.getElementById('kategoriProduk').value.toUpperCase();
  const timestamp = Date.now().toString().slice(-4);
  const kode = kategori.substring(0, 3) + timestamp;
  document.getElementById('kodeProduk').value = kode;
}
</script>

<?= $this->endSection() ?>
