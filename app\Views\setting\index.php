<?= $this->extend('layouts/main') ?>

<?= $this->section('title') ?>Setting<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- CSS dan JS Dependencies -->
<link href="<?= base_url('assets/sweetalert2/sweetalert2.min.css') ?>" rel="stylesheet">
<script src="<?= base_url('assets/sweetalert2/sweetalert2.all.min.js') ?>"></script>
<link rel="stylesheet" href="<?= base_url('assets/css/bootstrap-icons.min.css') ?>">
<link rel="stylesheet" href="<?= base_url('assets/css/setting.css') ?>">

<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <!-- Header -->
      <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h2 class="mb-1">
            <i class="bi bi-gear text-primary me-2"></i>Setting
          </h2>
          <p class="text-muted mb-0">Kelola pengaturan sistem PlaySphere</p>
        </div>
      </div>

      <!-- Setting Menu Grid -->
      <div class="setting-grid">
        
        <!-- Setting Operator -->
        <div class="setting-card" onclick="window.location.href='<?= base_url('setting/operator') ?>'">
          <div class="setting-icon">
            <i class="bi bi-people-fill"></i>
          </div>
          <div class="setting-content">
            <h5 class="setting-title">Setting Operator</h5>
            <p class="setting-description">Kelola admin dan operator sistem</p>
            <div class="setting-details">
              <small><i class="bi bi-person-badge me-1"></i>Manajemen Akun</small>
              <small><i class="bi bi-shield-check me-1"></i>Role & Permission</small>
              <small><i class="bi bi-toggle-on me-1"></i>Status Aktif/Nonaktif</small>
            </div>
          </div>
          <div class="setting-arrow">
            <i class="bi bi-chevron-right"></i>
          </div>
        </div>

        <!-- Setting Rental -->
        <div class="setting-card" onclick="openSettingModal('rental')">
          <div class="setting-icon">
            <i class="bi bi-shop"></i>
          </div>
          <div class="setting-content">
            <h5 class="setting-title">Setting Rental</h5>
            <p class="setting-description">Informasi dan identitas rental</p>
            <div class="setting-details">
              <small><i class="bi bi-image me-1"></i>Logo Rental</small>
              <small><i class="bi bi-building me-1"></i>Nama Rental</small>
              <small><i class="bi bi-geo-alt me-1"></i>Alamat Rental</small>
            </div>
          </div>
          <div class="setting-arrow">
            <i class="bi bi-chevron-right"></i>
          </div>
        </div>

        <!-- Setting Stok Produk -->
        <!--<div class="setting-card" onclick="openSettingModal('stok')">
          <div class="setting-icon">
            <i class="bi bi-box-seam"></i>
          </div>
          <div class="setting-content">
            <h5 class="setting-title">Manajemen Stok</h5>
            <p class="setting-description">Kelola stok produk penjualan kasir</p>
            <div class="setting-details">
              <small><i class="bi bi-plus-circle me-1"></i>Tambah Produk</small>
              <small><i class="bi bi-pencil me-1"></i>Edit Produk</small>
              <small><i class="bi bi-graph-up me-1"></i>Monitor Stok</small>
            </div>
          </div>
          <div class="setting-arrow">
            <i class="bi bi-chevron-right"></i>
          </div>
        </div>-->

        <!-- Setting Printer -->
        <div class="setting-card" onclick="openSettingModal('printer')">
          <div class="setting-icon">
            <i class="bi bi-printer"></i>
          </div>
          <div class="setting-content">
            <h5 class="setting-title">Setting Printer</h5>
            <p class="setting-description">Konfigurasi printer struk thermal 58mm</p>
            <div class="setting-details">
              <small><i class="bi bi-bluetooth me-1"></i>Bluetooth</small>
              <small><i class="bi bi-usb-symbol me-1"></i>USB</small>
              <small><i class="bi bi-ethernet me-1"></i>LAN</small>
              <small><i class="bi bi-receipt me-1"></i>Footer Struk</small>
            </div>
          </div>
          <div class="setting-arrow">
            <i class="bi bi-chevron-right"></i>
          </div>
        </div>

        <!-- Setting Tema -->
        <div class="setting-card" onclick="openSettingModal('tema')">
          <div class="setting-icon">
            <i class="bi bi-palette"></i>
          </div>
          <div class="setting-content">
            <h5 class="setting-title">Setting Tema</h5>
            <p class="setting-description">Pilih tema tampilan sistem</p>
            <div class="setting-details">
              <small><i class="bi bi-sun me-1"></i>Light Mode</small>
              <small><i class="bi bi-moon me-1"></i>Dark Mode</small>
              <small><i class="bi bi-circle-half me-1"></i>Auto</small>
            </div>
          </div>
          <div class="setting-arrow">
            <i class="bi bi-chevron-right"></i>
          </div>
        </div>

        <!-- Setting Lainnya -->
        <div class="setting-card" onclick="openSettingModal('lainnya')">
          <div class="setting-icon">
            <i class="bi bi-three-dots"></i>
          </div>
          <div class="setting-content">
            <h5 class="setting-title">Setting Lainnya</h5>
            <p class="setting-description">Pengaturan tambahan sistem</p>
            <div class="setting-details">
              <small><i class="bi bi-clock me-1"></i>Timezone</small>
              <small><i class="bi bi-currency-dollar me-1"></i>Currency</small>
              <small><i class="bi bi-shield-lock me-1"></i>Security</small>
            </div>
          </div>
          <div class="setting-arrow">
            <i class="bi bi-chevron-right"></i>
          </div>
        </div>

      </div>
    </div>
  </div>
</div>

<!-- Setting Rental Modal -->
<div class="modal fade" id="modalSettingRental" tabindex="-1" aria-labelledby="modalSettingRentalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header bg-gradient-primary text-white">
        <h5 class="modal-title" id="modalSettingRentalLabel">
          <i class="bi bi-shop me-2"></i>Setting Rental
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="formSettingRental" enctype="multipart/form-data">

          <!-- Logo Upload Section -->
          <div class="setting-section">
            <div class="section-header">
              <h6 class="section-title">
                <i class="bi bi-image text-primary me-2"></i>Logo Rental
              </h6>
              <p class="section-description">Upload logo yang akan ditampilkan di header dan struk</p>
            </div>

            <div class="row">
              <div class="col-md-6">
                <div class="logo-upload-area" id="logoUploadArea">
                  <div class="logo-preview" id="logoPreview">
                    <img id="currentLogo" src="<?= base_url('assets/images/default-logo.svg') ?>" alt="Current Logo">
                  </div>
                  <div class="upload-overlay">
                    <i class="bi bi-cloud-upload"></i>
                    <p>Klik untuk upload logo</p>
                    <small>Format: JPG, PNG, SVG (Max: 2MB)</small>
                  </div>
                  <input type="file" id="logoFile" name="logo" accept="image/*" style="display: none;">
                </div>
              </div>
              <div class="col-md-6">
                <div class="logo-info">
                  <h6>Rekomendasi Logo:</h6>
                  <ul class="logo-requirements">
                    <li><i class="bi bi-check-circle text-success me-1"></i>Ukuran: 200x200px</li>
                    <li><i class="bi bi-check-circle text-success me-1"></i>Format: PNG dengan background transparan</li>
                    <li><i class="bi bi-check-circle text-success me-1"></i>Resolusi tinggi untuk print</li>
                    <li><i class="bi bi-check-circle text-success me-1"></i>Desain simple dan jelas</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <!-- Rental Information Section -->
          <div class="setting-section">
            <div class="section-header">
              <h6 class="section-title">
                <i class="bi bi-building text-primary me-2"></i>Informasi Rental
              </h6>
              <p class="section-description">Data rental yang akan ditampilkan di sistem dan struk</p>
            </div>

            <div class="row g-3">
              <div class="col-md-6">
                <label for="namaRental" class="form-label">
                  <i class="bi bi-shop me-1"></i>Nama Rental
                </label>
                <input type="text" class="form-control" id="namaRental" name="nama_rental"
                       placeholder="Contoh: PlaySphere Gaming Center" required>
                <div class="form-text">Akan ditampilkan di header dan struk</div>
              </div>

              <div class="col-md-6">
                <label for="sloganRental" class="form-label">
                  <i class="bi bi-chat-quote me-1"></i>Slogan Rental
                </label>
                <input type="text" class="form-control" id="sloganRental" name="slogan_rental"
                       placeholder="Contoh: Gaming Center & Internet Cafe" required>
                <div class="form-text">Slogan di bawah nama rental pada struk</div>
              </div>

              <div class="col-md-6">
                <label for="whatsappRental" class="form-label">
                  <i class="bi bi-whatsapp me-1"></i>No. WhatsApp
                </label>
                <input type="text" class="form-control" id="whatsappRental" name="whatsapp_rental"
                       placeholder="Contoh: 08123456789" required>
                <div class="form-text">Untuk kontak customer di struk</div>
              </div>

              <div class="col-md-6">
                <!-- Empty column for layout balance -->
              </div>

              <div class="col-12">
                <label for="alamatRental" class="form-label">
                  <i class="bi bi-geo-alt me-1"></i>Alamat Rental
                </label>
                <textarea class="form-control" id="alamatRental" name="alamat_rental" rows="3"
                          placeholder="Contoh: Jl. Gaming Center No. 123, Kota Gaming, 12345" required></textarea>
                <div class="form-text">Alamat lengkap untuk struk dan informasi</div>
              </div>
            </div>
          </div>

          <!-- Preview Section -->
          <div class="setting-section">
            <div class="section-header">
              <h6 class="section-title">
                <i class="bi bi-eye text-primary me-2"></i>Preview
              </h6>
              <p class="section-description">Pratinjau tampilan di header dan struk</p>
            </div>

            <div class="row">
              <div class="col-md-6">
                <div class="preview-card">
                  <h6 class="preview-title">Header Layout</h6>
                  <div class="header-preview">
                    <div class="header-logo">
                      <img id="previewHeaderLogo" src="<?= base_url('assets/images/default-logo.svg') ?>" alt="Logo">
                    </div>
                    <div class="header-text">
                      <span id="previewHeaderNama">PlaySphere Gaming</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="col-md-6">
                <div class="preview-card">
                  <h6 class="preview-title">Struk Receipt</h6>
                  <div class="receipt-preview">
                    <div class="receipt-header">
                      <div class="receipt-logo">
                        <img id="previewReceiptLogo" src="<?= base_url('assets/images/default-logo.svg') ?>" alt="Logo">
                      </div>
                      <div class="receipt-info">
                        <div class="receipt-name" id="previewReceiptNama">PLAYSPHERE GAMING</div>
                        <div class="receipt-slogan" id="previewReceiptSlogan">Gaming Center & Internet Cafe</div>
                        <div class="receipt-address" id="previewReceiptAlamat">Jl. Gaming Center No. 123</div>
                        <div class="receipt-phone" id="previewReceiptPhone">WA: 08123456789</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          <i class="bi bi-x-circle me-1"></i>Batal
        </button>
        <button type="button" class="btn btn-primary" onclick="saveSettingRental()">
          <i class="bi bi-check-circle me-1"></i>Simpan Pengaturan
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Placeholder for Other Setting Modals -->
<div id="settingModals">
  <!-- Other modals will be loaded here dynamically -->
</div>

<script>
// Function to open setting modal
function openSettingModal(type) {
  console.log('Opening setting modal:', type);

  if (type === 'rental') {
    // Load current rental settings
    loadCurrentRentalSettings();
    // Show rental modal
    const modal = new bootstrap.Modal(document.getElementById('modalSettingRental'));
    modal.show();
  } else if (type === 'stok') {
    // Redirect to product management page
    window.location.href = '<?= base_url('produk') ?>';
  } else {
    // Show loading for other modals
    Swal.fire({
      title: 'Loading...',
      text: 'Memuat pengaturan ' + type,
      allowOutsideClick: false,
      didOpen: () => {
        Swal.showLoading();
      }
    });

    // Simulate loading (replace with actual modal loading)
    setTimeout(() => {
      Swal.close();
      Swal.fire({
        title: 'Coming Soon',
        text: 'Fitur setting ' + type + ' akan segera tersedia',
        icon: 'info',
        confirmButtonText: 'OK'
      });
    }, 1000);
  }
}

// Load current rental settings
function loadCurrentRentalSettings() {
  // Load from server
  fetch('<?= base_url('setting/getRentalSettings') ?>', {
    method: 'GET',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      const settings = data.data;
      document.getElementById('namaRental').value = settings.nama_rental || 'PlaySphere Gaming Center';
      document.getElementById('sloganRental').value = settings.slogan_rental || 'Gaming Center & Internet Cafe';
      document.getElementById('whatsappRental').value = settings.whatsapp_rental || '08123456789';
      document.getElementById('alamatRental').value = settings.alamat_rental || 'Jl. Gaming Center No. 123, Kota Gaming, 12345';

      // Update logo if exists
      if (settings.logo_rental) {
        document.getElementById('currentLogo').src = '<?= base_url() ?>' + settings.logo_rental;
      }

      // Update previews
      updatePreviews();
    } else {
      console.error('Failed to load rental settings:', data.message);
      // Use default values
      document.getElementById('namaRental').value = 'PlaySphere Gaming Center';
      document.getElementById('sloganRental').value = 'Gaming Center & Internet Cafe';
      document.getElementById('whatsappRental').value = '08123456789';
      document.getElementById('alamatRental').value = 'Jl. Gaming Center No. 123, Kota Gaming, 12345';
      updatePreviews();
    }
  })
  .catch(error => {
    console.error('Error loading rental settings:', error);
    // Use default values
    document.getElementById('namaRental').value = 'PlaySphere Gaming Center';
    document.getElementById('sloganRental').value = 'Gaming Center & Internet Cafe';
    document.getElementById('whatsappRental').value = '08123456789';
    document.getElementById('alamatRental').value = 'Jl. Gaming Center No. 123, Kota Gaming, 12345';
    updatePreviews();
  });
}

// Handle logo upload
document.addEventListener('DOMContentLoaded', function() {
  const logoUploadArea = document.getElementById('logoUploadArea');
  const logoFile = document.getElementById('logoFile');
  const currentLogo = document.getElementById('currentLogo');

  // Click to upload
  logoUploadArea.addEventListener('click', function() {
    logoFile.click();
  });

  // Handle file selection
  logoFile.addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
      // Validate file
      if (!file.type.startsWith('image/')) {
        Swal.fire('Error', 'File harus berupa gambar', 'error');
        return;
      }

      if (file.size > 2 * 1024 * 1024) { // 2MB
        Swal.fire('Error', 'Ukuran file maksimal 2MB', 'error');
        return;
      }

      // Preview image
      const reader = new FileReader();
      reader.onload = function(e) {
        currentLogo.src = e.target.result;
        updatePreviews();
      };
      reader.readAsDataURL(file);
    }
  });

  // Real-time preview updates
  document.getElementById('namaRental').addEventListener('input', updatePreviews);
  document.getElementById('sloganRental').addEventListener('input', updatePreviews);
  document.getElementById('whatsappRental').addEventListener('input', updatePreviews);
  document.getElementById('alamatRental').addEventListener('input', updatePreviews);
});

// Update preview displays
function updatePreviews() {
  const namaRental = document.getElementById('namaRental').value || 'PlaySphere Gaming';
  const sloganRental = document.getElementById('sloganRental').value || 'Gaming Center & Internet Cafe';
  const whatsappRental = document.getElementById('whatsappRental').value || '08123456789';
  const alamatRental = document.getElementById('alamatRental').value || 'Jl. Gaming Center No. 123';
  const logoSrc = document.getElementById('currentLogo').src;

  // Update header preview
  document.getElementById('previewHeaderLogo').src = logoSrc;
  document.getElementById('previewHeaderNama').textContent = namaRental;

  // Update receipt preview
  document.getElementById('previewReceiptLogo').src = logoSrc;
  document.getElementById('previewReceiptNama').textContent = namaRental.toUpperCase();
  document.getElementById('previewReceiptSlogan').textContent = sloganRental;
  document.getElementById('previewReceiptAlamat').textContent = alamatRental;
  document.getElementById('previewReceiptPhone').textContent = 'WA: ' + whatsappRental;
}

// Save rental settings
function saveSettingRental() {
  // Get form data
  const formData = new FormData(document.getElementById('formSettingRental'));

  // Validate required fields
  const namaRental = document.getElementById('namaRental').value.trim();
  const sloganRental = document.getElementById('sloganRental').value.trim();
  const whatsappRental = document.getElementById('whatsappRental').value.trim();
  const alamatRental = document.getElementById('alamatRental').value.trim();

  if (!namaRental || !sloganRental || !whatsappRental || !alamatRental) {
    Swal.fire('Error', 'Semua field harus diisi', 'error');
    return;
  }

  // Validate WhatsApp format
  const whatsappRegex = /^[0-9]{10,15}$/;
  if (!whatsappRegex.test(whatsappRental)) {
    Swal.fire('Error', 'Format No. WhatsApp tidak valid (10-15 digit angka)', 'error');
    return;
  }

  // Show loading
  Swal.fire({
    title: 'Menyimpan...',
    text: 'Sedang menyimpan pengaturan rental',
    allowOutsideClick: false,
    didOpen: () => {
      Swal.showLoading();
    }
  });

  // Send to server
  fetch('<?= base_url('setting/saveRental') ?>', {
    method: 'POST',
    body: formData,
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => {
    if (!response.ok) {
      throw new Error('Network response was not ok: ' + response.status);
    }
    return response.json();
  })
  .then(data => {
    Swal.close();
    if (data.success) {
      Swal.fire({
        title: 'Berhasil!',
        text: 'Pengaturan rental berhasil disimpan',
        icon: 'success',
        confirmButtonText: 'OK'
      }).then(() => {
        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('modalSettingRental'));
        modal.hide();

        // Refresh page to show changes
        location.reload();
      });
    } else {
      Swal.fire('Error', data.message || 'Gagal menyimpan pengaturan', 'error');
    }
  })
  .catch(error => {
    Swal.close();
    console.error('Error:', error);
    Swal.fire('Error', 'Terjadi kesalahan sistem: ' + error.message, 'error');
  });
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
  console.log('Setting page loaded');
});
</script>

<?= $this->endSection() ?>
