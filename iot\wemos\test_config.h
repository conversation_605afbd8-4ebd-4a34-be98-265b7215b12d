// test_config.h - Configuration file for testing PlaySphere IoT Device
// Copy this file and rename to config.h for custom configurations

#ifndef TEST_CONFIG_H
#define TEST_CONFIG_H

// WiFi Configuration
#define WIFI_SSID "PlaySphere"
#define WIFI_PASSWORD "1234554321"

// Server Configuration  
#define SERVER_HOST "*************"
#define SERVER_PORT 80

// Hardware Pin Configuration
#define RELAY_PIN D1  // GPIO 5

// TFT Display Pin Configuration (for reference)
// These are defined in User_Setup.h for TFT_eSPI library
// SCL: D5 (GPIO 14) 
// SDA: D7 (GPIO 13) 
// RES: D4 (GPIO 2) 
// DC: D3 (GPIO 0) 
// BLK: D8 (GPIO 15)

// Display Configuration
#define SCREEN_WIDTH 240
#define SCREEN_HEIGHT 240
#define DISPLAY_ROTATION 2

// Update Intervals (milliseconds)
#define STATUS_UPDATE_INTERVAL 1000    // 1 second
#define SERVER_RETRY_INTERVAL 5000     // 5 seconds  
#define DEBUG_OUTPUT_INTERVAL 30000    // 30 seconds
#define WIFI_RETRY_INTERVAL 10000      // 10 seconds

// Color Definitions (16-bit RGB565 format)
#define COLOR_BLACK        0x0000
#define COLOR_WHITE        0xFFFF
#define COLOR_RED          0xF800
#define COLOR_GREEN        0x07E0
#define COLOR_BLUE         0x001F
#define COLOR_YELLOW       0xFFE0
#define COLOR_MAGENTA      0xF81F
#define COLOR_CYAN         0x07FF
#define COLOR_ORANGE       0xFD20
#define COLOR_PURPLE       0x780F
#define COLOR_GRAY         0x8410
#define COLOR_DARK_GRAY    0x4208
#define COLOR_LIGHT_BLUE   0x841F

// Custom Card Colors
#define CARD_GREEN   0x2589  // Active station
#define CARD_BLUE    0x4D9F  // Paused station  
#define CARD_GRAY    0x6B6D  // Standby station
#define STATUS_RED   0xF800  // Stop button
#define STATUS_BLUE  0x001F  // Action button

// Text Colors
#define TEXT_WHITE   0xFFFF
#define TEXT_BLACK   0x0000

// API Endpoints
#define API_CHANNEL_FIND "/api/channel/find"
#define API_CHANNEL_STATUS "/api/channel/status/"

// Device Information
#define DEVICE_NAME "PlaySphere IoT"
#define DEVICE_VERSION "1.0.0"
#define STARTUP_LOGO "LANGIT INOVASI"

// Debug Configuration
#define ENABLE_SERIAL_DEBUG true
#define SERIAL_BAUD_RATE 115200

// Display Text Configuration
#define TEXT_CONNECTING_WIFI "Menghubungkan WiFi"
#define TEXT_WIFI_CONNECTED "WiFi Terhubung"
#define TEXT_WIFI_DISCONNECTED "WiFi Terputus"
#define TEXT_CONNECTING_SERVER "Menghubungi Server..."
#define TEXT_SERVER_CONNECTED "Server Terhubung"
#define TEXT_SERVER_ERROR "Server Error"
#define TEXT_CHANNEL_NOT_FOUND "Channel tidak terdaftar"
#define TEXT_READY "PlaySphere Ready"
#define TEXT_PAUSED "PAUSED"
#define TEXT_TIME_UP "WAKTU HABIS"

// Button Configuration
#define BUTTON_WIDTH 25
#define BUTTON_HEIGHT 20
#define BUTTON_RADIUS 8
#define BUTTON_Y_POSITION 200

// Status Indicators
#define STATUS_SAIPUL "SAIPUL"
#define STATUS_DAMAR "DAMAR"
#define INDICATOR_RADIUS 4

// Animation Configuration
#define SPLASH_DELAY 3000
#define LOADING_DOT_DELAY 500
#define BLINK_DELAY 500
#define BLINK_CYCLES 3

// Memory Management
#define JSON_BUFFER_SIZE 1024
#define HTTP_TIMEOUT 10000  // 10 seconds

// Error Handling
#define MAX_WIFI_RETRIES 5
#define MAX_SERVER_RETRIES 3
#define ERROR_DISPLAY_TIME 5000

#endif // TEST_CONFIG_H
