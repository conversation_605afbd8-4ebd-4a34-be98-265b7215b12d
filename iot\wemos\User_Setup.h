// User_Setup.h for TFT_eSPI library
// Configuration for ST7789 240x240 TFT with Wemos D1 Mini

// Define to disable loading of fonts and smooth fonts
#define DISABLE_ALL_LIBRARY_WARNINGS

// Driver selection
#define ST7789_DRIVER      // Full configuration option, define additional parameters below for this display

// For ST7789, ST7735, ILI9163 and other ST77XX based displays
// These are the pins used for the ESP8266 Wemos D1 Mini
#define TFT_MOSI D7  // SDA (GPIO 13)
#define TFT_SCLK D5  // SCL (GPIO 14)
#define TFT_CS   -1  // Not connected
#define TFT_DC   D3  // DC (GPIO 0)
#define TFT_RST  D4  // RES (GPIO 2)
#define TFT_BL   D8  // BLK (GPIO 15) - Backlight control

// For ST7789 displays, it is best to use Hardware SPI
#define TFT_WIDTH  240
#define TFT_HEIGHT 240

// The ST7789 display can be configured to use different color orders
#define TFT_RGB_ORDER TFT_RGB  // Colour order Red-Green-Blue
//#define TFT_RGB_ORDER TFT_BGR  // Colour order Blue-Green-Red

// Generic ESP8266 setup
#define LOAD_GLCD   // Font 1. Original Adafruit 8 pixel font needs ~1820 bytes in FLASH
#define LOAD_FONT2  // Font 2. Small 16 pixel high font, needs ~3534 bytes in FLASH, 96 characters
#define LOAD_FONT4  // Font 4. Medium 26 pixel high font, needs ~5848 bytes in FLASH, 96 characters
#define LOAD_FONT6  // Font 6. Large 48 pixel high font, needs ~2666 bytes in FLASH, only characters 1234567890:-.apm
#define LOAD_FONT7  // Font 7. 7 segment 48 pixel high font, needs ~2438 bytes in FLASH, only characters 1234567890:-.
#define LOAD_FONT8  // Font 8. Large 75 pixel high font needs ~3256 bytes in FLASH, only characters 1234567890:-.
#define LOAD_GFXFF  // FreeFonts. Include access to the 48 Adafruit_GFX free fonts FF1 to FF48 and custom fonts

// Comment out the #define below to stop the SPIFFS filing system and smooth font code being loaded
// this will save ~20kbytes of FLASH
#define SMOOTH_FONT

// SPI frequency for the display
#define SPI_FREQUENCY  27000000

// Optional reduced SPI frequency for reading TFT
#define SPI_READ_FREQUENCY  20000000

// The XPT2046 requires a lower SPI clock rate of 2.5MHz so we define that here:
#define SPI_TOUCH_FREQUENCY  2500000
