<?= $this->extend('layout/template') ?>

<?= $this->section('content') ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <p class="text-muted mb-0">Kelola Channel PlayStation</p>
    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addModal">
        <i class="fas fa-plus me-2"></i>Tambah Channel
    </button>
</div>

<div class="row">
    <?php foreach ($channels as $channel): ?>
    <div class="col-md-3 mb-4">
        <div class="card border-0 h-100 rounded-4 shadow-sm">
            <div class="card-body p-3 text-center">
                <!-- Icon dan Status -->
                <div class="position-relative mb-2">
                    <div class="bg-primary bg-opacity-10 rounded-circle p-3 mx-auto" style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-desktop fa-lg text-primary"></i>
                    </div>
    </div>

                <!-- Informasi Channel -->
                <h5 class="card-title mb-2"><?= $channel['number'] ?></h5>
                <div class="badge bg-primary mb-2"><?= $channel['ps_type_name'] ?></div>
                
                <!-- IP Address -->
                <div class="bg-light rounded p-2 mb-3">
                    <small class="text-muted d-block mb-1">IP Address</small>
                    <code class="text-dark"><?= $channel['ip_address'] ?></code>
                </div>

                <!-- Tombol Aksi -->
                <div class="d-flex justify-content-center gap-2">
                    <button class="btn btn-warning btn-sm rounded-pill px-3" data-bs-toggle="modal" data-bs-target="#editModal<?= $channel['id'] ?>">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-danger btn-sm rounded-pill px-3" onclick="deleteChannel(<?= $channel['id'] ?>)">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Modal Edit -->
        <div class="modal fade" id="editModal<?= $channel['id'] ?>">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Edit Channel</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <form action="/channel/edit/<?= $channel['id'] ?>" method="post">
                        <div class="modal-body">
                            <div class="mb-3">
                                <label class="form-label">Nama Channel</label>
                                <input type="text" name="number" class="form-control" value="<?= $channel['number'] ?>" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Jenis PS</label>
                                <select name="ps_type_id" class="form-control" required>
                                    <?php foreach ($ps_types as $type): ?>
                                    <option value="<?= $type['id'] ?>" <?= $channel['ps_type_id'] == $type['id'] ? 'selected' : '' ?>>
                                        <?= $type['name'] ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">IP Address</label>
                                <?php $ips = explode('.', $channel['ip_address']); ?>
                                <div class="d-flex align-items-center mt-2">
                                    <input type="text" name="ip1" class="form-control text-center" maxlength="3" pattern="[0-9]{1,3}" value="<?= $ips[0] ?>" placeholder="_" required style="width: 70px">
                                    <span class="mx-2">.</span>
                                    <input type="text" name="ip2" class="form-control text-center" maxlength="3" pattern="[0-9]{1,3}" value="<?= $ips[1] ?>" placeholder="_" required style="width: 70px">
                                    <span class="mx-2">.</span>
                                    <input type="text" name="ip3" class="form-control text-center" maxlength="3" pattern="[0-9]{1,3}" value="<?= $ips[2] ?>" placeholder="_" required style="width: 70px">
                                    <span class="mx-2">.</span>
                                    <input type="text" name="ip4" class="form-control text-center" maxlength="3" pattern="[0-9]{1,3}" value="<?= $ips[3] ?>" placeholder="_" required style="width: 70px">
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                            <button type="submit" class="btn btn-primary">Simpan</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <?php endforeach; ?>
</div>

<!-- Modal Tambah -->
<div class="modal fade" id="addModal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">Tambah Channel</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form action="/channel/add" method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Nama Channel</label>
                        <input type="text" name="number" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Jenis PS</label>
                        <select name="ps_type_id" class="form-control" required>
                            <option value="">Pilih Jenis PS</option>
                            <?php foreach ($ps_types as $type): ?>
                            <option value="<?= $type['id'] ?>"><?= $type['name'] ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">IP Address</label>
                        <div class="d-flex align-items-center mt-2">
                            <input type="text" name="ip1" class="form-control text-center" maxlength="3" pattern="[0-9]{1,3}" placeholder="_" required style="width: 70px">
                            <span class="mx-2">.</span>
                            <input type="text" name="ip2" class="form-control text-center" maxlength="3" pattern="[0-9]{1,3}" placeholder="_" required style="width: 70px">
                            <span class="mx-2">.</span>
                            <input type="text" name="ip3" class="form-control text-center" maxlength="3" pattern="[0-9]{1,3}" placeholder="_" required style="width: 70px">
                            <span class="mx-2">.</span>
                            <input type="text" name="ip4" class="form-control text-center" maxlength="3" pattern="[0-9]{1,3}" placeholder="_" required style="width: 70px">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    function deleteChannel(id) {
        if(confirm('Yakin hapus channel ini?')) {
            window.location.href = `/channel/delete/${id}`;
        }
    }

    // Auto-tab untuk input IP address
    document.querySelectorAll('input[name^="ip"]').forEach(input => {
        input.addEventListener('input', function() {
            if (this.value.length === 3) {
                let next = this.nextElementSibling;
                while (next && next.tagName !== 'INPUT') {
                    next = next.nextElementSibling;
                }
                if (next) next.focus();
            }
        });
    });
</script>
<?= $this->endSection() ?>