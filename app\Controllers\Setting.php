<?php

namespace App\Controllers;

use App\Models\OperatorModel;

class Setting extends BaseController
{
    protected $operatorModel;

    public function __construct()
    {
        $this->operatorModel = new OperatorModel();
    }

    public function index()
    {
        $data = [
            'title' => 'Setting'
        ];

        return view('setting/index', $data);
    }

    /**
     * Save rental settings
     *
     * @return \CodeIgniter\HTTP\ResponseInterface JSON response
     */
    public function saveRental()
    {
        log_message('info', 'saveRental method called');

        if (!$this->request->isAJAX()) {
            log_message('warning', 'saveRental called without AJAX');
            return $this->response->setStatusCode(405)->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ]);
        }

        try {
            log_message('info', 'Processing rental settings save request');
            $validation = \Config\Services::validation();

            // Validation rules
            $validation->setRules([
                'nama_rental' => 'required|min_length[3]|max_length[100]',
                'slogan_rental' => 'required|min_length[3]|max_length[100]',
                'whatsapp_rental' => 'required|min_length[10]|max_length[15]',
                'alamat_rental' => 'required|min_length[10]|max_length[255]',
                'logo' => 'if_exist|uploaded[logo]|is_image[logo]|max_size[logo,2048]'
            ]);

            if (!$validation->withRequest($this->request)->run()) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Validasi gagal: ' . implode(', ', $validation->getErrors())
                ]);
            }

            $namaRental = $this->request->getPost('nama_rental');
            $sloganRental = $this->request->getPost('slogan_rental');
            $whatsappRental = $this->request->getPost('whatsapp_rental');
            $alamatRental = $this->request->getPost('alamat_rental');
            $logoFile = $this->request->getFile('logo');

            log_message('info', 'Received data: nama=' . $namaRental . ', slogan=' . $sloganRental . ', wa=' . $whatsappRental . ', alamat=' . $alamatRental);
            log_message('info', 'Logo file: ' . ($logoFile ? $logoFile->getName() : 'none'));

            // Handle logo upload
            $logoPath = null;
            if ($logoFile && $logoFile->isValid() && !$logoFile->hasMoved()) {
                try {
                    $logoName = 'logo_rental_' . time() . '.' . $logoFile->getExtension();
                    $uploadPath = FCPATH . 'assets/images/';

                    log_message('info', 'Upload path: ' . $uploadPath);
                    log_message('info', 'Logo name: ' . $logoName);

                    // Create directory if not exists
                    if (!is_dir($uploadPath)) {
                        if (!mkdir($uploadPath, 0755, true)) {
                            throw new \Exception('Failed to create upload directory: ' . $uploadPath);
                        }
                        log_message('info', 'Created upload directory: ' . $uploadPath);
                    }

                    // Move uploaded file
                    if (!$logoFile->move($uploadPath, $logoName)) {
                        throw new \Exception('Failed to move uploaded file');
                    }

                    $logoPath = 'assets/images/' . $logoName;

                    log_message('info', 'Logo uploaded successfully: ' . $logoPath);

                    // Verify file exists
                    if (!file_exists($uploadPath . $logoName)) {
                        throw new \Exception('Uploaded file not found after move');
                    }

                } catch (\Exception $e) {
                    log_message('error', 'Logo upload failed: ' . $e->getMessage());
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => 'Gagal upload logo: ' . $e->getMessage()
                    ]);
                }
            }

            // Save to JSON config file
            $configPath = WRITEPATH . 'rental_config.json';

            try {
                log_message('info', 'Config path: ' . $configPath);

                $config = [];
                if (file_exists($configPath)) {
                    $existingConfig = file_get_contents($configPath);
                    $config = json_decode($existingConfig, true) ?: [];
                    log_message('info', 'Loaded existing config: ' . json_encode($config));
                }

                $config['nama_rental'] = $namaRental;
                $config['slogan_rental'] = $sloganRental;
                $config['whatsapp_rental'] = $whatsappRental;
                $config['alamat_rental'] = $alamatRental;

                if ($logoPath) {
                    $config['logo_rental'] = $logoPath;
                    log_message('info', 'Logo path saved: ' . $logoPath);
                }

                $config['updated_at'] = date('Y-m-d H:i:s');

                log_message('info', 'Config to save: ' . json_encode($config));

                $jsonData = json_encode($config, JSON_PRETTY_PRINT);
                if ($jsonData === false) {
                    throw new \Exception('Failed to encode JSON data');
                }

                // Ensure writable directory exists
                $writableDir = dirname($configPath);
                if (!is_dir($writableDir)) {
                    if (!mkdir($writableDir, 0755, true)) {
                        throw new \Exception('Failed to create writable directory: ' . $writableDir);
                    }
                }

                $bytesWritten = file_put_contents($configPath, $jsonData);
                if ($bytesWritten === false) {
                    throw new \Exception('Failed to write config file');
                }

                log_message('info', 'Config file written successfully. Bytes: ' . $bytesWritten);

                // Verify file was written
                if (!file_exists($configPath)) {
                    throw new \Exception('Config file not found after write');
                }

                // Verify content
                $verifyContent = file_get_contents($configPath);
                $verifyConfig = json_decode($verifyContent, true);
                if (!$verifyConfig) {
                    throw new \Exception('Config file verification failed');
                }

                log_message('info', 'Rental settings saved and verified successfully');

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Pengaturan rental berhasil disimpan',
                    'data' => $config
                ]);

            } catch (\Exception $e) {
                log_message('error', 'Save config failed: ' . $e->getMessage());
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Gagal menyimpan konfigurasi: ' . $e->getMessage()
                ]);
            }

        } catch (\Exception $e) {
            log_message('error', 'Save rental settings error: ' . $e->getMessage());
            return $this->response->setStatusCode(500)->setJSON([
                'success' => false,
                'message' => 'Terjadi kesalahan sistem: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get current rental settings
     *
     * @return \CodeIgniter\HTTP\ResponseInterface JSON response
     */
    public function getRentalSettings()
    {
        try {
            $configPath = WRITEPATH . 'rental_config.json';

            log_message('info', 'Getting rental settings from: ' . $configPath);

            if (file_exists($configPath)) {
                $configContent = file_get_contents($configPath);
                $config = json_decode($configContent, true);

                log_message('info', 'Config file exists. Content: ' . $configContent);

                if ($config) {
                    return $this->response->setJSON([
                        'success' => true,
                        'data' => $config
                    ]);
                } else {
                    log_message('error', 'Failed to decode JSON config');
                }
            } else {
                log_message('info', 'Config file does not exist, returning defaults');
            }

            // Return default settings
            $defaultSettings = [
                'nama_rental' => 'PlaySphere Gaming Center',
                'slogan_rental' => 'Gaming Center & Internet Cafe',
                'whatsapp_rental' => '08123456789',
                'alamat_rental' => 'Jl. Gaming Center No. 123, Kota Gaming, 12345',
                'logo_rental' => 'assets/images/default-logo.svg'
            ];

            return $this->response->setJSON([
                'success' => true,
                'data' => $defaultSettings
            ]);

        } catch (\Exception $e) {
            log_message('error', 'getRentalSettings error: ' . $e->getMessage());
            return $this->response->setStatusCode(500)->setJSON([
                'success' => false,
                'message' => 'Terjadi kesalahan sistem: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Halaman manajemen operator
     */
    public function operator()
    {
        // Check if user is admin
        $authCheck = \App\Controllers\Auth::checkAdmin();
        if ($authCheck) return $authCheck;

        $data = [
            'title' => 'Manajemen Operator'
        ];

        return view('setting/operator', $data);
    }

    /**
     * Get all operators (AJAX)
     */
    public function getOperators()
    {
        try {
            $operators = $this->operatorModel->getOperatorsWithStats();

            return $this->response->setJSON([
                'success' => true,
                'data' => $operators
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Gagal mengambil data operator: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Create new operator (AJAX)
     */
    public function createOperator()
    {
        if (!$this->request->isAJAX() || $this->request->getMethod() !== 'POST') {
            return $this->response->setStatusCode(405)->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ]);
        }

        $data = [
            'username' => $this->request->getPost('username'),
            'nama' => $this->request->getPost('nama'),
            'no_wa' => $this->request->getPost('no_wa'),
            'password' => $this->request->getPost('password'),
            'role' => $this->request->getPost('role') ?: 'operator',
            'status' => $this->request->getPost('status') ?: 'aktif'
        ];

        if ($this->operatorModel->createOperator($data)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Operator berhasil ditambahkan'
            ]);
        } else {
            $errors = $this->operatorModel->errors();
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Gagal menambahkan operator',
                'errors' => $errors
            ]);
        }
    }

    /**
     * Get operator by ID (AJAX)
     */
    public function getOperator($id)
    {
        $operator = $this->operatorModel->find($id);

        if (!$operator) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Operator tidak ditemukan'
            ]);
        }

        // Remove password from response
        unset($operator['password']);

        return $this->response->setJSON([
            'success' => true,
            'data' => $operator
        ]);
    }

    /**
     * Update operator (AJAX)
     */
    public function updateOperator($id)
    {
        if (!$this->request->isAJAX() || $this->request->getMethod() !== 'POST') {
            return $this->response->setStatusCode(405)->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ]);
        }

        $operator = $this->operatorModel->find($id);
        if (!$operator) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Operator tidak ditemukan'
            ]);
        }

        $data = [
            'username' => $this->request->getPost('username'),
            'nama' => $this->request->getPost('nama'),
            'no_wa' => $this->request->getPost('no_wa'),
            'role' => $this->request->getPost('role'),
            'status' => $this->request->getPost('status')
        ];

        // Only update password if provided
        $password = $this->request->getPost('password');
        if (!empty($password)) {
            $data['password'] = $password;
        }

        if ($this->operatorModel->updateOperator($id, $data)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Operator berhasil diperbarui'
            ]);
        } else {
            $errors = $this->operatorModel->errors();
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Gagal memperbarui operator',
                'errors' => $errors
            ]);
        }
    }

    /**
     * Delete operator (AJAX)
     */
    public function deleteOperator($id)
    {
        if (!$this->request->isAJAX() || $this->request->getMethod() !== 'POST') {
            return $this->response->setStatusCode(405)->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ]);
        }

        $operator = $this->operatorModel->find($id);
        if (!$operator) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Operator tidak ditemukan'
            ]);
        }

        // Prevent deleting current user
        if ($operator['id'] == session()->get('operator_id')) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Tidak dapat menghapus akun yang sedang digunakan'
            ]);
        }

        if ($this->operatorModel->delete($id)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Operator berhasil dihapus'
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Gagal menghapus operator'
            ]);
        }
    }

    /**
     * Toggle operator status (AJAX)
     */
    public function toggleStatus($id)
    {
        if (!$this->request->isAJAX() || $this->request->getMethod() !== 'POST') {
            return $this->response->setStatusCode(405)->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ]);
        }

        $operator = $this->operatorModel->find($id);
        if (!$operator) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Operator tidak ditemukan'
            ]);
        }

        // Prevent disabling current user
        if ($operator['id'] == session()->get('operator_id')) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Tidak dapat menonaktifkan akun yang sedang digunakan'
            ]);
        }

        if ($this->operatorModel->toggleStatus($id)) {
            $newStatus = $operator['status'] === 'aktif' ? 'nonaktif' : 'aktif';
            return $this->response->setJSON([
                'success' => true,
                'message' => "Status operator berhasil diubah menjadi {$newStatus}",
                'new_status' => $newStatus
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Gagal mengubah status operator'
            ]);
        }
    }
}
