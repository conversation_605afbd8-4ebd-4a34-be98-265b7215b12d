<!DOCTYPE html>
<html>
<head>
    <title>Test Produk API</title>
    <link href="<?= base_url('assets/css/bootstrap.min.css') ?>" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>Test Produk API</h2>
        
        <button onclick="testAPI()" class="btn btn-primary">Test API</button>
        <button onclick="testRender()" class="btn btn-success">Test Render</button>
        
        <div id="results" class="mt-3"></div>
        
        <div id="productGrid" class="mt-3" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 1rem;">
            <!-- Products will appear here -->
        </div>
    </div>

    <script>
        function testAPI() {
            console.log('Testing API...');
            document.getElementById('results').innerHTML = 'Loading...';
            
            fetch('<?= base_url('produk/getAllProduk') ?>', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                console.log('Response:', response);
                return response.json();
            })
            .then(data => {
                console.log('Data:', data);
                document.getElementById('results').innerHTML = `
                    <div class="alert alert-success">
                        <h5>API Response:</h5>
                        <p>Success: ${data.success}</p>
                        <p>Count: ${data.data ? data.data.length : 0}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
                
                if (data.success && data.data) {
                    renderTestProducts(data.data);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('results').innerHTML = `
                    <div class="alert alert-danger">
                        <h5>Error:</h5>
                        <p>${error.message}</p>
                    </div>
                `;
            });
        }
        
        function testRender() {
            const testData = [
                {
                    id: 1,
                    nama_produk: 'Test Product 1',
                    harga_jual: 5000,
                    stok: 10,
                    kategori: 'Test'
                },
                {
                    id: 2,
                    nama_produk: 'Test Product 2',
                    harga_jual: 8000,
                    stok: 5,
                    kategori: 'Test'
                }
            ];
            
            renderTestProducts(testData);
        }
        
        function renderTestProducts(products) {
            const grid = document.getElementById('productGrid');
            let html = '';
            
            products.forEach(product => {
                const stok = parseInt(product.stok);
                let stockClass = stok > 10 ? 'bg-success' : stok > 0 ? 'bg-warning' : 'bg-danger';
                
                html += `
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">${product.nama_produk}</h6>
                            <p class="card-text">
                                Harga: Rp ${parseInt(product.harga_jual).toLocaleString('id-ID').replace(/,/g, '.')}<br>
                                Stok: <span class="badge ${stockClass}">${stok}</span><br>
                                Kategori: ${product.kategori}
                            </p>
                            <button class="btn btn-sm btn-primary" onclick="alert('Product ID: ${product.id}')">
                                Add to Cart
                            </button>
                        </div>
                    </div>
                `;
            });
            
            grid.innerHTML = html;
            console.log('Rendered', products.length, 'products');
        }
        
        // Auto test on load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded');
            testAPI();
        });
    </script>
</body>
</html>
