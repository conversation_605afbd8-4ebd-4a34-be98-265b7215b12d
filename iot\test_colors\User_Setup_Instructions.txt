INSTRUKSI SETUP TFT_eSPI untuk Test Colors

SEBELUM UPLOAD test_colors.ino:

1. Buka folder TFT_eSPI library:
   Windows: Documents\Arduino\libraries\TFT_eSPI\
   
2. Edit file User_Setup.h, pastikan konfigurasi berikut:

// ===== DRIVER SELECTION =====
#define ST7789_DRIVER

// ===== PIN CONFIGURATION =====
#define TFT_MOSI D7  // SDA (GPIO 13)
#define TFT_SCLK D5  // SCL (GPIO 14)
#define TFT_CS   -1  // Not connected
#define TFT_DC   D3  // DC (GPIO 0)
#define TFT_RST  D4  // RES (GPIO 2)
#define TFT_BL   D8  // BLK (GPIO 15)

// ===== DISPLAY SIZE =====
#define TFT_WIDTH  240
#define TFT_HEIGHT 240

// ===== COLOR ORDER =====
#define TFT_RGB_ORDER TFT_RGB

// ===== FONTS =====
#define LOAD_GLCD
#define LOAD_FONT2
#define LOAD_FONT4
#define LOAD_FONT6
#define LOAD_FONT7
#define LOAD_FONT8
#define LOAD_GFXFF

// ===== SPI FREQUENCY =====
#define SPI_FREQUENCY  27000000

LANGKAH-LANGKAH:

1. Backup User_Setup.h yang lama
2. Edit User_Setup.h sesuai konfigurasi di atas
3. Restart Arduino IDE
4. Buka test_colors.ino
5. Upload ke Wemos D1 Mini
6. Buka Serial Monitor (115200 baud)
7. Lihat hasil test warna di layar

HASIL YANG DIHARAPKAN:

1. Basic Colors Test:
   - Merah, Hijau, Biru, Kuning, Cyan, Magenta terlihat jelas
   - Warna sesuai dengan label

2. Card Colors Test:
   - ACTIVE: Background hijau gelap
   - PAUSED: Background biru
   - STANDBY: Background abu-abu

3. Color Cycle:
   - Berganti setiap 2 detik
   - Simulasi tampilan PlaySphere

TROUBLESHOOTING:

Jika warna masih salah:
1. Coba ganti TFT_RGB_ORDER ke TFT_BGR
2. Periksa koneksi pin display
3. Pastikan display ST7789 240x240

Jika display tidak menyala:
1. Periksa pin BLK (backlight) ke D8
2. Periksa power supply 3.3V
3. Periksa koneksi SPI (MOSI, SCLK)

Jika compilation error:
1. Pastikan hanya membuka test_colors.ino
2. Tutup wemos.ino di Arduino IDE
3. Restart Arduino IDE
