/*
 * Test Relay untuk Wemos D1 Mini
 * Test manual relay control pada pin D1
 * 
 * CARA PENGGUNAAN:
 * 1. Upload ke Wemos D1 Mini
 * 2. Buka Serial Monitor (115200 baud)
 * 3. Kirim command: "ON" atau "OFF"
 * 4. Lihat status relay dan pin D1
 */

#define RELAY_PIN D1  // GPIO 5

void setup() {
    Serial.begin(115200);
    Serial.println("\n=== RELAY TEST START ===");
    
    // Setup relay pin
    pinMode(RELAY_PIN, OUTPUT);
    digitalWrite(RELAY_PIN, LOW);
    
    Serial.println("Relay pin configured on D1 (GPIO 5)");
    Serial.println("Commands:");
    Serial.println("- Type 'ON' to turn relay ON");
    Serial.println("- Type 'OFF' to turn relay OFF");
    Serial.println("- Type 'STATUS' to check current status");
    Serial.println("- Type 'TEST' to run auto test");
    Serial.println("\nRelay is initially OFF");
    Serial.println("========================\n");
}

void loop() {
    // Check for serial commands
    if (Serial.available()) {
        String command = Serial.readStringUntil('\n');
        command.trim();
        command.toUpperCase();
        
        if (command == "ON") {
            digitalWrite(RELAY_PIN, HIGH);
            Serial.println("✓ Relay ON - Pin D1 HIGH");
            printStatus();
        }
        else if (command == "OFF") {
            digitalWrite(RELAY_PIN, LOW);
            Serial.println("✓ Relay OFF - Pin D1 LOW");
            printStatus();
        }
        else if (command == "STATUS") {
            printStatus();
        }
        else if (command == "TEST") {
            runAutoTest();
        }
        else {
            Serial.println("❌ Unknown command: " + command);
            Serial.println("Valid commands: ON, OFF, STATUS, TEST");
        }
    }
    
    // Auto status every 10 seconds
    static unsigned long lastStatus = 0;
    if (millis() - lastStatus > 10000) {
        lastStatus = millis();
        Serial.println("[AUTO] Current status:");
        printStatus();
    }
}

void printStatus() {
    bool pinState = digitalRead(RELAY_PIN);
    Serial.println("Pin D1 (GPIO 5): " + String(pinState ? "HIGH" : "LOW"));
    Serial.println("Relay Status: " + String(pinState ? "ON" : "OFF"));
    Serial.println("Voltage: " + String(pinState ? "3.3V" : "0V"));
    
    // Test dengan multimeter
    Serial.println("\n📋 Multimeter Test:");
    Serial.println("- Positive probe: Pin D1");
    Serial.println("- Negative probe: GND");
    Serial.println("- Expected reading: " + String(pinState ? "3.3V" : "0V"));
    Serial.println("- If different, check connections\n");
}

void runAutoTest() {
    Serial.println("\n🔄 Running Auto Test...");
    
    for (int i = 0; i < 5; i++) {
        Serial.println("Test " + String(i+1) + "/5:");
        
        // Turn ON
        digitalWrite(RELAY_PIN, HIGH);
        Serial.println("  → Relay ON");
        delay(1000);
        
        // Turn OFF  
        digitalWrite(RELAY_PIN, LOW);
        Serial.println("  → Relay OFF");
        delay(1000);
    }
    
    Serial.println("✅ Auto test complete");
    Serial.println("If relay module has LED, it should have blinked 5 times");
    printStatus();
}

/*
 * TROUBLESHOOTING:
 * 
 * 1. Relay tidak merespon:
 *    - Cek koneksi pin D1 ke relay module
 *    - Cek power supply relay module (5V atau 3.3V)
 *    - Cek ground connection
 * 
 * 2. Pin D1 tidak berubah:
 *    - Cek dengan multimeter
 *    - Pastikan tidak ada short circuit
 *    - Coba pin lain (D2, D3, dll)
 * 
 * 3. Relay module bermasalah:
 *    - Cek LED indicator pada module
 *    - Cek dengan relay module lain
 *    - Pastikan trigger HIGH atau LOW sesuai module
 * 
 * 4. Wiring untuk relay module umum:
 *    VCC → 3.3V atau 5V (sesuai module)
 *    GND → GND
 *    IN  → D1 (GPIO 5)
 *    
 * 5. Jika relay trigger LOW (active low):
 *    Ganti digitalWrite(RELAY_PIN, HIGH) dengan LOW
 *    dan digitalWrite(RELAY_PIN, LOW) dengan HIGH
 */
