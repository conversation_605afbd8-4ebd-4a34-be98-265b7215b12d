<?php namespace App\Models;

use CodeIgniter\Model;

class ChannelModel extends Model
{
    protected $table = 'channels';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'number', 'ps_type_id', 'ip_address', 'status', 
        'connection_status', 'session_data', 'last_ping', 'last_seen'
    ];

    public function getChannels()
    {
        return $this->db->table('channels c')
             ->select('c.*, pt.name as ps_type_name, pt.price_per_hour')
             ->join('ps_types pt', 'pt.id = c.ps_type_id')
             ->get()
             ->getResultArray();
    }
}