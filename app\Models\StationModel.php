<?php
namespace App\Models;
use CodeIgniter\Model;

class StationModel extends Model
{
    protected $table = 'station';
    protected $primaryKey = 'id';
    protected $allowedFields = ['nama_station', 'id_konsol', 'ip_address'];
    protected $useTimestamps = true;

    public function getWithKonsol()
    {
        return $this->select('station.*, konsol.nama_konsol')
                    ->join('konsol', 'konsol.id = station.id_konsol')
                    ->findAll();
    }
}
