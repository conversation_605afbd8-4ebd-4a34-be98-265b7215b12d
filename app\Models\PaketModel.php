<?php
namespace App\Models;
use CodeIgniter\Model;

class PaketModel extends Model
{
    protected $table = 'paket';
    protected $primaryKey = 'id';
    protected $allowedFields = ['nama_paket', 'durasi', 'harga', 'keterangan', 'id_konsol'];
    protected $useTimestamps = true;

    public function getWithKonsol()
    {
        return $this->select('paket.*, konsol.nama_konsol')
                    ->join('konsol', 'konsol.id = paket.id_konsol')
                    ->orderBy('paket.nama_paket')
                    ->findAll();
    }

    public function getByKonsol($konsolId)
    {
        return $this->where('id_konsol', $konsolId)
                    ->orderBy('nama_paket')
                    ->findAll();
    }
}