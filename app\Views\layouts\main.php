<?php
// Check if user is logged in
if (!session()->get('logged_in')) {
    return redirect()->to('/login');
}

$userRole = session()->get('role');
$userName = session()->get('nama');
?>
<!DOCTYPE html>
<html lang="id">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>PlaySphere | Game Console Management</title>

  <!-- Favicon untuk berbagai device -->
  <link rel="icon" type="image/x-icon" href="<?= base_url('assets/img/favicon.ico') ?>">
  <link rel="icon" type="image/png" sizes="32x32" href="<?= base_url('assets/img/favicon-32x32.png') ?>">
  <link rel="icon" type="image/png" sizes="16x16" href="<?= base_url('assets/img/favicon-16x16.png') ?>">
  <link rel="icon" type="image/png" sizes="512x512" href="<?= base_url('assets/img/favicon-512x512.png') ?>">
  <link rel="apple-touch-icon" href="<?= base_url('assets/img/apple-touch-icon.png') ?>">

  <!-- CSS Dependencies -->
  <link rel="stylesheet" href="<?= base_url('assets/css/bootstrap.min.css') ?>">
  <link rel="stylesheet" href="<?= base_url('assets/css/bootstrap-icons.min.css') ?>">
  <link rel="stylesheet" href="<?= base_url('assets/css/main.css') ?>">

  <!-- Page-specific CSS is loaded in each view -->
</head>
<body>

  <div class="sidebar-overlay" id="sidebarOverlay"></div>
  <div class="sidebar-overlay" id="sidebarOverlay"></div>

  <!-- Sidebar -->
  <div class="sidebar" id="sidebar">
    <div>
      <div class="brand">
        <img src="<?= base_url('assets/img/playsphere3.png') ?>" alt="PlaySphere Logo">
      </div>
      <ul class="nav flex-column">
        <!-- Menu yang bisa diakses semua role -->
        <li><a class="nav-link <?= uri_string() == 'dashboard' ? 'active' : '' ?>" href="<?= base_url('dashboard') ?>">
          <i class="bi bi-speedometer2"></i> <span class="nav-text">Dashboard</span>
        </a></li>
        <li><a class="nav-link <?= uri_string() == 'member' ? 'active' : '' ?>" href="<?= base_url('member') ?>">
          <i class="bi bi-people-fill"></i> <span class="nav-text">Member</span>
        </a></li>
        <li><a class="nav-link <?= uri_string() == 'kasir' ? 'active' : '' ?>" href="<?= base_url('kasir') ?>">
          <i class="bi bi-cash-stack"></i> <span class="nav-text">Kasir</span>
        </a></li>

        <?php if ($userRole === 'admin'): ?>
        <!-- Menu khusus admin -->
        <li><a class="nav-link <?= uri_string() == 'konsol' ? 'active' : '' ?>" href="<?= base_url('konsol') ?>">
          <i class="bi bi-controller"></i> <span class="nav-text">Konsol</span>
        </a></li>
        <li><a class="nav-link <?= uri_string() == 'paket' ? 'active' : '' ?>" href="<?= base_url('paket') ?>">
          <i class="bi bi-box-seam"></i> <span class="nav-text">Paket</span>
        </a></li>
        <li><a class="nav-link <?= uri_string() == 'produk' ? 'active' : '' ?>" href="<?= base_url('produk') ?>">
          <i class="bi bi-box"></i> <span class="nav-text">Produk</span>
        </a></li>
        <li><a class="nav-link <?= uri_string() == 'laporan' ? 'active' : '' ?>" href="<?= base_url('laporan') ?>">
          <i class="bi bi-graph-up"></i> <span class="nav-text">Laporan</span>
        </a></li>
        <li><a class="nav-link <?= strpos(uri_string(), 'setting') === 0 ? 'active' : '' ?>" href="<?= base_url('setting') ?>">
          <i class="bi bi-gear-fill"></i> <span class="nav-text">Setting</span>
        </a></li>
        <?php endif; ?>
      </ul>
    </div>
    <div class="mb-2">
      <div class="px-3">
        <a href="<?= base_url('logout') ?>" class="btn btn-outline-light w-100 mb-2">
          <i class="bi bi-box-arrow-right"></i> <span class="nav-text">Keluar</span>
        </a>
      </div>
      <div class="sidebar-footer">
        &copy; <?= date('Y') ?> <a href="https://langitinovasi.id" target="_blank">Langit Inovasi</a><br>
        <small>v.3.17.3</small>
      </div>
    </div>
  </div>

  <!-- Content -->
  <div class="content" id="content">
    <!-- Topbar -->
    <div class="topbar d-flex justify-content-between align-items-center flex-wrap">
      <div class="fw-bold fs-5">
        <button class="mobile-menu-btn me-2" id="mobileMenuBtn">
          <i class="bi bi-list"></i>
        </button>
        <?php
        helper('rental');
        $rentalSettings = getRentalSettings();
        ?>
        <img src="<?= base_url($rentalSettings['logo_rental'] ?? 'assets/images/default-logo.svg') ?>"
             alt="Logo" class="topbar-logo me-2"><?= $rentalSettings['nama_rental'] ?? 'PlaySphere Gaming Center' ?>
      </div>

      <div class="text-center flex-fill">
        <div id="tanggal-jam" class="fw-semibold text-dark" style="font-size: 1.1rem;">
          <?= tanggalIndo(date('Y-m-d')) ?> | <span id="jam">00:00:00</span>
        </div>
      </div>

      <div class="text-end">
        <span class="badge <?= $userRole === 'admin' ? 'bg-warning' : 'bg-info' ?>">
          <i class="bi bi-<?= $userRole === 'admin' ? 'shield-check' : 'person-badge' ?> me-1"></i>
          <?= ucfirst($userRole) ?>
        </span>
        <span class="fw-semibold ms-2"><?= $userName ?></span>
      </div>
    </div>

    <!-- Dynamic Section -->
    <div class="container-fluid p-4">
      <?= $this->renderSection('content') ?>
      
      <!-- Mobile Footer - hanya tampil di mobile -->
      <div class="mobile-footer" style="background-color: #37474F; color: #cfd8dc; text-align: center; padding: 20px 15px; margin-top: 30px; font-size: 0.85rem;">
        <div class="mb-2">
          <strong><i class="bi bi-controller me-1"></i>PlaySphere</strong><br>
          <small>Game Console Management</small>
        </div>
        <div class="mb-2">
          &copy; <?= date('Y') ?> <a href="https://langitinovasi.id" target="_blank" style="color: #4FC3F7; text-decoration: none;">Langit Inovasi</a><br>
          <small>v.3.17.3</small>
        </div>
        <div>
          <small style="color: #b0bec5;">Made with <i class="bi bi-heart-fill text-danger"></i> for Gamers</small>
        </div>
      </div>
    </div>
  </div>

  <!-- JS -->
  <script src="<?= base_url('assets/js/jquery-3.6.0.min.js') ?>"></script>
  <script src="<?= base_url('assets/js/bootstrap.bundle.min.js') ?>"></script>
  <script src="<?= base_url('assets/js/helpers.js') ?>"></script>
  <script src="<?= base_url('assets/js/main.js') ?>"></script>

  <!-- Page-specific scripts -->
  <?= $this->renderSection('scripts') ?>
</body>
</html>