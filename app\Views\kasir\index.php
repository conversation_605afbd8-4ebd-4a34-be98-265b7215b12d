<?php
/**
 * Kasir Index View
 * Halaman utama sistem Point of Sale (POS) untuk transaksi kasir
 * Menampilkan berbagai jenis transaksi dan fitur kasir
 *
 * <AUTHOR> Team
 * @version 3.0
 */
?>
<?= $this->extend('layouts/main') ?>
<?= $this->section('content') ?>

<!-- CSS Dependencies untuk Kasir -->
<link href="<?= base_url('assets/sweetalert2/sweetalert2.min.css') ?>" rel="stylesheet">
<script src="<?= base_url('assets/sweetalert2/sweetalert2.all.min.js') ?>"></script>
<link rel="stylesheet" href="<?= base_url('assets/css/bootstrap.min.css') ?>">
<link rel="stylesheet" href="<?= base_url('assets/css/bootstrap-icons.min.css') ?>">
<link rel="stylesheet" href="<?= base_url('assets/css/kasir.css') ?>">

<div class="kasir-container">
  <!-- Panel Utama - Area Transaksi -->
  <div class="kasir-main">
    <div class="kasir-header">
      <div class="kasir-header-title">
        <i class="bi bi-cash-stack"></i>
        Sistem Kasir PlaySphere
      </div>
    </div>

    <!-- Tab Navigation -->
    <div class="kasir-tabs">
      <button class="kasir-tab" onclick="switchTab('pembayaran')">
        <i class="bi bi-credit-card me-1"></i>Pembayaran Sesi
      </button>
      <button class="kasir-tab" onclick="switchTab('topup')">
        <i class="bi bi-wallet2 me-1"></i>Top Up Member
      </button>
      <button class="kasir-tab active" onclick="switchTab('penjualan')">
        <i class="bi bi-bag-check me-1"></i>Penjualan
      </button>
      <button class="kasir-tab" onclick="switchTab('daftar-transaksi')">
        <i class="bi bi-list-ul me-1"></i>Daftar Transaksi
      </button>
    </div>

    <!-- Content Area -->
    <div class="kasir-content">
      <!-- Tab Pembayaran Sesi -->
      <div id="tab-pembayaran" class="tab-content">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <h5 class="mb-0">
            <i class="bi bi-clock-history me-2"></i>Sesi Belum Dibayar
          </h5>
          <button class="btn btn-outline-primary btn-sm" onclick="refreshUnpaidSessions()">
            <i class="bi bi-arrow-clockwise"></i> Refresh
          </button>
        </div>

        <div id="unpaidSessionsList">
          <?php if (empty($unpaidSessions)): ?>
            <div class="text-center py-4">
              <i class="bi bi-check-circle text-success" style="font-size: 3rem;"></i>
              <p class="text-muted mt-2">Semua sesi sudah dibayar</p>
            </div>
          <?php else: ?>
            <div class="unpaid-sessions-grid">
              <?php foreach ($unpaidSessions as $session): ?>
                <div class="unpaid-session" data-session-id="<?= $session['id'] ?>">
                  <div class="session-info">
                    <div class="session-details">
                      <div class="session-station">
                        <i class="bi bi-display me-1"></i>
                        <?= esc($session['nama_station']) ?> - <?= esc($session['nama_konsol']) ?>
                      </div>
                      <div class="session-price">
                        Rp <?= number_format($session['harga_total'], 0, ',', '.') ?>
                      </div>
                    </div>
                    <button class="session-cart-btn" onclick="addSessionToCart(<?= $session['id'] ?>, '<?= esc($session['nama_station']) ?> - <?= esc($session['nama_konsol']) ?>', <?= $session['harga_total'] ?>, '<?= $session['waktu_mulai'] ?>', '<?= $session['waktu_berhenti'] ?>')">
                      <i class="bi bi-cart-plus"></i>
                    </button>
                  </div>
                  <div class="session-time">
                    <i class="bi bi-clock me-1"></i>
                    <?= date('d/m/Y H:i', strtotime($session['waktu_mulai'])) ?> -
                    <?= date('H:i', strtotime($session['waktu_berhenti'])) ?>
                  </div>
                </div>
              <?php endforeach; ?>
            </div>
          <?php endif; ?>
        </div>
      </div>

      <!-- Tab Top Up Member -->
      <div id="tab-topup" class="tab-content">
        <h5 class="mb-3">
          <i class="bi bi-wallet2 me-2"></i>Top Up Saldo Member
        </h5>

        <div class="member-search-container">
          <i class="bi bi-search member-search-icon"></i>
          <input type="text" class="member-search-input" id="searchMember" placeholder="Cari member berdasarkan nama atau ID..." oninput="searchMember(this.value)">
        </div>

        <!-- Member Cards Grid -->
        <div class="member-cards-container" id="memberCardsContainer">
          <!-- Cards will be generated here -->
        </div>

        <!-- Pagination -->
        <nav aria-label="Member pagination" class="mt-3">
          <ul class="pagination justify-content-center" id="memberPagination">
            <!-- Pagination will be generated here -->
          </ul>
        </nav>
      </div>

      <!-- Tab Penjualan -->
      <div id="tab-penjualan" class="tab-content active">
        <h5 class="mb-3">
          <i class="bi bi-bag-check me-2"></i>Penjualan Produk
        </h5>

        <!-- Barcode Scanner Area -->
        <div class="barcode-container">
          <div class="barcode-icon">
            <i class="bi bi-upc-scan"></i>
          </div>
          <div class="barcode-text">Scan Barcode atau Pilih Produk</div>
          <input type="text" class="form-control mt-2" id="barcodeInput" placeholder="Scan atau ketik barcode..." onkeypress="handleBarcode(event)">
        </div>

        <!-- Product Categories -->
        <div class="mb-3">
          <div class="btn-group" role="group">
            <button class="btn btn-outline-primary active" onclick="filterProducts('all')">Semua</button>
            <button class="btn btn-outline-primary" onclick="filterProducts('makanan')">Makanan</button>
            <button class="btn btn-outline-primary" onclick="filterProducts('minuman')">Minuman</button>
            <button class="btn btn-outline-primary" onclick="filterProducts('snack')">Snack</button>
          </div>
        </div>

        <!-- Product Grid -->
        <div class="product-grid" id="productGrid">
          <?php if (!empty($produk)): ?>
            <?php foreach ($produk as $product): ?>
              <?php
                $stok = (int)$product['stok'];
                $stockBadge = '';
                $stockClass = '';

                if ($stok <= 0) {
                  $stockBadge = 'Habis';
                  $stockClass = 'badge-stock-empty';
                } elseif ($stok <= 10) {
                  $stockBadge = 'Rendah';
                  $stockClass = 'badge-stock-low';
                } else {
                  $stockBadge = 'Ready';
                  $stockClass = 'badge-stock';
                }

                $hargaFormatted = 'Rp ' . number_format($product['harga_jual'], 0, '', '.');
              ?>

              <div class="product-card <?= $stok <= 0 ? 'product-disabled' : '' ?>"
                   onclick="<?= $stok > 0 ? "addToCart({$product['id']}, '{$product['nama_produk']}', {$product['harga_jual']}, {$product['stok']})" : 'void(0)' ?>"
                   data-category="<?= strtolower($product['kategori']) ?>">
                <div class="product-name"><?= esc($product['nama_produk']) ?></div>
                <div class="product-price"><?= $hargaFormatted ?></div>
                <div class="product-stock">Stok: <?= $stok ?> <span class="<?= $stockClass ?>"><?= $stockBadge ?></span></div>
                <div class="product-category"><?= esc($product['kategori']) ?></div>
              </div>
            <?php endforeach; ?>
          <?php else: ?>
            <div class="text-center py-4">
              <i class="bi bi-box text-muted" style="font-size: 3rem;"></i>
              <p class="text-muted mt-2">Belum ada produk</p>
              <p class="text-muted">Tambahkan produk melalui menu Manajemen Stok</p>
            </div>
          <?php endif; ?>
        </div>
      </div>

      <!-- Tab Daftar Transaksi -->
      <div id="tab-daftar-transaksi" class="tab-content">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <h5 class="mb-0">
            <i class="bi bi-list-ul me-2"></i>Daftar Transaksi
          </h5>
          <div class="d-flex gap-2">
            <input type="date" class="form-control form-control-sm" id="filterTanggal" onchange="filterTransaksi()">
            <button class="btn btn-outline-primary btn-sm" onclick="refreshTransaksi()">
              <i class="bi bi-arrow-clockwise"></i>
            </button>
          </div>
        </div>

        <div class="table-responsive">
          <table class="table table-striped table-hover">
            <thead class="table-dark">
              <tr>
                <th>No. Transaksi</th>
                <th>Tanggal</th>
                <th>Total Item</th>
                <th>Total Harga</th>
                <th>Metode Bayar</th>
                <th>Aksi</th>
              </tr>
            </thead>
            <tbody id="transaksiTableBody">
              <tr>
                <td colspan="6" class="text-center">
                  <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                  Memuat data transaksi...
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <nav aria-label="Pagination">
          <ul class="pagination justify-content-center" id="transaksiPagination">
            <!-- Pagination will be generated here -->
          </ul>
        </nav>
      </div>
    </div>
  </div>
</div>

<!-- Floating Cart Button -->
<div class="floating-cart">
  <button class="cart-btn" onclick="showCartModal()">
    <i class="bi bi-cart3"></i>
    <span>Keranjang</span>
    <span class="cart-badge" id="cartItemCount">0</span>
  </button>
</div>





<!-- Modal Top Up Member -->
<div class="modal fade" id="modalTopupMember" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header bg-primary text-white">
        <h5 class="modal-title">
          <i class="bi bi-wallet2 me-2"></i>Top Up Saldo Member
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="mb-3">
          <label class="form-label">Member</label>
          <input type="text" class="form-control" id="memberName" readonly>
          <input type="hidden" id="memberId">
        </div>

        <div class="mb-3">
          <label class="form-label">Saldo Saat Ini</label>
          <div class="input-group">
            <span class="input-group-text">Rp</span>
            <input type="text" class="form-control" id="saldoSaatIni" readonly>
          </div>
        </div>

        <div class="mb-3">
          <label class="form-label">Jumlah Top Up</label>
          <div class="input-group">
            <span class="input-group-text">Rp</span>
            <input type="number" class="form-control" id="jumlahTopup" oninput="hitungSaldoBaru()">
          </div>
          <div class="mt-2">
            <button class="btn btn-outline-primary btn-sm me-1" onclick="setTopupAmount(10000)">10K</button>
            <button class="btn btn-outline-primary btn-sm me-1" onclick="setTopupAmount(25000)">25K</button>
            <button class="btn btn-outline-primary btn-sm me-1" onclick="setTopupAmount(50000)">50K</button>
            <button class="btn btn-outline-primary btn-sm" onclick="setTopupAmount(100000)">100K</button>
          </div>
        </div>



        <div class="mb-3">
          <label class="form-label">Saldo Setelah Top Up</label>
          <div class="input-group">
            <span class="input-group-text">Rp</span>
            <input type="text" class="form-control text-success fw-bold" id="saldoBaru" readonly>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
        <button type="button" class="btn btn-primary" onclick="addTopupToCart()">
          <i class="bi bi-cart-plus me-1"></i>Tambah ke Keranjang
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Modal Keranjang Belanja -->
<div class="modal fade" id="modalKeranjang" tabindex="-1">
  <div class="modal-dialog modal-lg cart-modal">
    <div class="modal-content">
      <div class="modal-header bg-success text-white">
        <h5 class="modal-title">
          <i class="bi bi-cart3 me-2"></i>Keranjang Belanja
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body p-0">
        <div class="cart-items" id="cartItems">
          <div class="text-center py-4">
            <i class="bi bi-cart text-muted" style="font-size: 3rem;"></i>
            <p class="text-muted mt-2">Keranjang kosong</p>
            <p class="text-muted">Pilih produk untuk ditambahkan ke keranjang</p>
          </div>
        </div>

        <div class="cart-total" id="cartTotalSection" style="display: none;">
          <span class="total-label">Total Belanja:</span>
          <span class="total-amount" id="cartTotal">Rp 0</span>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          <i class="bi bi-arrow-left me-1"></i>Lanjut Belanja
        </button>
        <button type="button" class="btn btn-danger" onclick="clearCart()" id="clearCartBtn" style="display: none;">
          <i class="bi bi-trash me-1"></i>Kosongkan
        </button>
        <button type="button" class="btn btn-success" onclick="processCheckout()" id="checkoutBtn" disabled>
          <i class="bi bi-credit-card me-1"></i>Proses Pembayaran
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Modal Konfirmasi Pembayaran -->
<div class="modal fade" id="modalKonfirmasiPembayaran" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header bg-success text-white">
        <h5 class="modal-title">
          <i class="bi bi-credit-card me-2"></i>Konfirmasi Pembayaran
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div class="mb-3">
          <h6>Detail Transaksi:</h6>
          <div id="checkoutItemsList" class="border rounded p-2 mb-3" style="max-height: 200px; overflow-y: auto;">
            <!-- Items will be loaded here -->
          </div>
        </div>

        <div class="row mb-3">
          <div class="col-6">
            <strong>Total Items:</strong>
          </div>
          <div class="col-6 text-end">
            <span id="checkoutTotalItems">0</span>
          </div>
        </div>

        <div class="row mb-3">
          <div class="col-6">
            <strong>Total Pembayaran:</strong>
          </div>
          <div class="col-6 text-end">
            <strong class="text-success" id="checkoutTotalAmount">Rp 0</strong>
          </div>
        </div>

        <hr>

        <div class="mb-3">
          <label class="form-label"><strong>Metode Pembayaran:</strong></label>
          <div class="row g-2">
            <div class="col-6">
              <div class="form-check">
                <input class="form-check-input" type="radio" name="metodePembayaran" id="metodeTunai" value="tunai" checked onchange="togglePaymentFields()">
                <label class="form-check-label" for="metodeTunai">
                  <i class="bi bi-cash text-success me-1"></i>Tunai
                </label>
              </div>
            </div>
            <div class="col-6">
              <div class="form-check">
                <input class="form-check-input" type="radio" name="metodePembayaran" id="metodeQris" value="qris" onchange="togglePaymentFields()">
                <label class="form-check-label" for="metodeQris">
                  <i class="bi bi-qr-code text-primary me-1"></i>QRIS
                </label>
              </div>
            </div>
            <div class="col-6">
              <div class="form-check">
                <input class="form-check-input" type="radio" name="metodePembayaran" id="metodeDebit" value="debit" onchange="togglePaymentFields()">
                <label class="form-check-label" for="metodeDebit">
                  <i class="bi bi-credit-card text-info me-1"></i>Kartu Debit
                </label>
              </div>
            </div>
            <div class="col-6">
              <div class="form-check">
                <input class="form-check-input" type="radio" name="metodePembayaran" id="metodeTransfer" value="transfer" onchange="togglePaymentFields()">
                <label class="form-check-label" for="metodeTransfer">
                  <i class="bi bi-bank text-warning me-1"></i>Transfer Bank
                </label>
              </div>
            </div>
          </div>
        </div>

        <!-- Cash Payment Fields -->
        <div id="cashPaymentFields" class="mb-3">
          <div class="row">
            <div class="col-6">
              <label class="form-label">Jumlah Bayar:</label>
              <div class="input-group">
                <span class="input-group-text">Rp</span>
                <input type="number" class="form-control" id="jumlahBayarInput" oninput="hitungKembalian()" min="0">
              </div>
            </div>
            <div class="col-6">
              <label class="form-label">Kembalian:</label>
              <div class="input-group">
                <span class="input-group-text">Rp</span>
                <input type="text" class="form-control" id="kembalianDisplay" readonly>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          <i class="bi bi-x-circle me-1"></i>Batal
        </button>
        <button type="button" class="btn btn-success" onclick="konfirmasiPembayaranFinal()">
          <i class="bi bi-check-circle me-1"></i>Proses Pembayaran
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Modal Struk Pembayaran -->
<div class="modal fade" id="modalStruk" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header bg-success text-white">
        <h5 class="modal-title">
          <i class="bi bi-receipt me-2"></i>Struk Pembayaran
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <div id="strukContent" class="struk-container">
          <!-- Struk content will be generated here -->
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          <i class="bi bi-x-circle me-1"></i>Tutup
        </button>
        <button type="button" class="btn btn-primary" onclick="cetakStruk()">
          <i class="bi bi-printer me-1"></i>Cetak
        </button>
      </div>
    </div>
  </div>
</div>

<script>
// Global variables
let currentSesiId = null;
let currentMemberId = null;
let currentSessionData = null;
let currentMemberData = null;
let cart = [];

// Helper function for consistent price formatting
function formatPrice(amount) {
  return 'Rp ' + parseInt(amount).toLocaleString('id-ID').replace(/,/g, '.');
}

// Cart persistence functions
function saveCartToStorage() {
  try {
    localStorage.setItem('playsphere_cart', JSON.stringify(cart));
    console.log('Cart saved to localStorage:', cart.length, 'items');
  } catch (e) {
    console.error('Failed to save cart to localStorage:', e);
  }
}

function loadCartFromStorage() {
  try {
    const savedCart = localStorage.getItem('playsphere_cart');
    if (savedCart) {
      cart = JSON.parse(savedCart);
      console.log('Cart loaded from localStorage:', cart.length, 'items');
      updateCartDisplay();
    }
  } catch (e) {
    console.error('Failed to load cart from localStorage:', e);
    cart = [];
  }
}

function clearCartStorage() {
  try {
    localStorage.removeItem('playsphere_cart');
    console.log('Cart storage cleared');
  } catch (e) {
    console.error('Failed to clear cart storage:', e);
  }
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
  console.log('Kasir page loaded');

  // Load cart from localStorage first
  loadCartFromStorage();

  renderMemberCards(); // Initialize member cards

  // Load products immediately since penjualan is now the default tab
  console.log('Loading products on page load...');
  loadProducts();
});

// Tab switching functionality
function switchTab(tabName) {
  // Remove active class from all tabs and contents
  document.querySelectorAll('.kasir-tab').forEach(tab => tab.classList.remove('active'));
  document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

  // Add active class to selected tab and content
  event.target.classList.add('active');
  document.getElementById('tab-' + tabName).classList.add('active');

  // Load data based on tab
  if (tabName === 'pembayaran') {
    refreshUnpaidSessions();
  } else if (tabName === 'topup') {
    loadMembers();
  } else if (tabName === 'penjualan') {
    loadProducts();
  } else if (tabName === 'daftar-transaksi') {
    loadTransaksiList();
  }
}

// Refresh unpaid sessions
function refreshUnpaidSessions() {
  fetch('<?= base_url('kasir/getUnpaidSessions') ?>', {
    method: 'GET',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      updateUnpaidSessionsList(data.sessions);
    }
  })
  .catch(error => {
    console.error('Error refreshing sessions:', error);
  });
}

// Update unpaid sessions list
function updateUnpaidSessionsList(sessions) {
  const container = document.getElementById('unpaidSessionsList');

  if (sessions.length === 0) {
    container.innerHTML = `
      <div class="text-center py-4">
        <i class="bi bi-check-circle text-success" style="font-size: 3rem;"></i>
        <p class="text-muted mt-2">Semua sesi sudah dibayar</p>
      </div>
    `;
    return;
  }

  let html = '<div class="unpaid-sessions-grid">';
  sessions.forEach(session => {
    html += `
      <div class="unpaid-session" data-session-id="${session.id}">
        <div class="session-info">
          <div class="session-details">
            <div class="session-station">
              <i class="bi bi-display me-1"></i>
              ${session.nama_station} - ${session.nama_konsol}
            </div>
            <div class="session-price">
              ${formatPrice(session.harga_total)}
            </div>
          </div>
          <button class="session-cart-btn" onclick="addSessionToCart(${session.id}, '${session.nama_station} - ${session.nama_konsol}', ${session.harga_total}, '${session.waktu_mulai}', '${session.waktu_berhenti}')">
            <i class="bi bi-cart-plus"></i>
          </button>
        </div>
        <div class="session-time">
          <i class="bi bi-clock me-1"></i>
          ${formatDateTime(session.waktu_mulai)} - ${formatTime(session.waktu_berhenti)}
        </div>
      </div>
    `;
  });
  html += '</div>';

  container.innerHTML = html;
}

// Format date time
function formatDateTime(dateString) {
  const date = new Date(dateString);
  return date.toLocaleDateString('id-ID') + ' ' + date.toLocaleTimeString('id-ID', {hour: '2-digit', minute: '2-digit'});
}

// Format time only
function formatTime(dateString) {
  const date = new Date(dateString);
  return date.toLocaleTimeString('id-ID', {hour: '2-digit', minute: '2-digit'});
}

// Add session payment to cart directly
function addSessionToCart(sesiId, stationName, totalHarga, waktuMulai, waktuBerhenti) {
  // Check if session payment already exists in cart
  const existingItem = cart.find(item => item.type === 'session_payment' && item.sessionId === sesiId);

  if (existingItem) {
    Swal.fire('Info', 'Pembayaran sesi ini sudah ada di keranjang', 'info');
    return;
  }

  // Format times to HH:MM
  const startTime = new Date(waktuMulai);
  const endTime = new Date(waktuBerhenti);
  const startTimeFormatted = startTime.toLocaleTimeString('id-ID', {hour: '2-digit', minute: '2-digit'});
  const endTimeFormatted = endTime.toLocaleTimeString('id-ID', {hour: '2-digit', minute: '2-digit'});

  // Add session payment to cart directly
  cart.push({
    id: `session_${sesiId}`,
    type: 'session_payment',
    sessionId: sesiId,
    name: `Personal - ${stationName} - ${startTimeFormatted} - ${endTimeFormatted}`,
    price: totalHarga,
    quantity: 1,
    subtotal: totalHarga,
    startTime: startTimeFormatted,
    endTime: endTimeFormatted
  });

  updateCartDisplay();
  saveCartToStorage(); // Save to localStorage

  Swal.fire({
    title: 'Ditambahkan!',
    text: 'Pembayaran sesi ditambahkan ke keranjang',
    icon: 'success',
    timer: 1500,
    showConfirmButton: false
  });
}





// Search member functionality
function searchMember(keyword) {
  if (!keyword.trim()) {
    // If search is empty, show all members
    allMembers = <?= json_encode($members) ?>;
  } else {
    // Filter members based on search keyword
    const originalMembers = <?= json_encode($members) ?>;
    allMembers = originalMembers.filter(member =>
      member.nama.toLowerCase().includes(keyword.toLowerCase()) ||
      member.id_member.toLowerCase().includes(keyword.toLowerCase())
    );
  }

  // Reset to first page and re-render
  currentMemberPage = 1;
  renderMemberCards(1);
}

// Show top-up modal
function showTopupModal(memberId, memberName, currentSaldo) {
  currentMemberId = memberId;
  currentMemberData = {
    id: memberId,
    name: memberName,
    saldo: currentSaldo
  };

  document.getElementById('memberName').value = memberName;
  document.getElementById('memberId').value = memberId;
  document.getElementById('saldoSaatIni').value = parseInt(currentSaldo).toLocaleString('id-ID');
  document.getElementById('jumlahTopup').value = '';
  document.getElementById('saldoBaru').value = parseInt(currentSaldo).toLocaleString('id-ID');

  const modal = new bootstrap.Modal(document.getElementById('modalTopupMember'));
  modal.show();
}

// Set top-up amount
function setTopupAmount(amount) {
  document.getElementById('jumlahTopup').value = amount;
  hitungSaldoBaru();
}

// Calculate new balance
function hitungSaldoBaru() {
  const saldoSaatIni = parseInt(document.getElementById('saldoSaatIni').value.replace(/\./g, ''));
  const jumlahTopup = parseInt(document.getElementById('jumlahTopup').value) || 0;
  const saldoBaru = saldoSaatIni + jumlahTopup;

  document.getElementById('saldoBaru').value = saldoBaru.toLocaleString('id-ID');
}

// Add top-up to cart
function addTopupToCart() {
  const jumlah = parseInt(document.getElementById('jumlahTopup').value) || 0;

  if (jumlah <= 0) {
    Swal.fire('Error', 'Jumlah top-up harus lebih dari 0', 'error');
    return;
  }

  // Check if member topup already in cart
  const existingItem = cart.find(item => item.type === 'member_topup' && item.memberId === currentMemberId);

  if (existingItem) {
    // Update existing topup amount
    existingItem.price = jumlah;
    existingItem.subtotal = jumlah;
  } else {
    // Add new topup to cart
    cart.push({
      id: `topup_${currentMemberId}`,
      type: 'member_topup',
      memberId: currentMemberId,
      name: `Top Up Saldo - ${currentMemberData.name}`,
      price: jumlah,
      quantity: 1,
      subtotal: jumlah,
      memberData: currentMemberData
    });
  }

  updateCartDisplay();
  saveCartToStorage(); // Save to localStorage

  // Close modal
  const modal = bootstrap.Modal.getInstance(document.getElementById('modalTopupMember'));
  modal.hide();

  Swal.fire({
    title: 'Ditambahkan!',
    text: 'Top-up member ditambahkan ke keranjang',
    icon: 'success',
    timer: 1500,
    showConfirmButton: false
  });
}

// Load products from database
function loadProducts() {
  console.log('=== LOADING PRODUCTS ===');

  // Use jQuery if available, otherwise use fetch
  if (typeof $ !== 'undefined') {
    console.log('Using jQuery AJAX');
    $.ajax({
      url: '<?= base_url('produk/getAllProduk') ?>',
      method: 'GET',
      dataType: 'json',
      headers: {
        'X-Requested-With': 'XMLHttpRequest'
      },
      success: function(data) {
        console.log('jQuery success:', data);
        if (data.success && data.data) {
          renderProducts(data.data);
        } else {
          showProductError('Gagal memuat produk: ' + (data.message || 'Unknown error'));
        }
      },
      error: function(xhr, status, error) {
        console.error('jQuery error:', status, error);
        showProductError('Error loading products: ' + error);
      }
    });
  } else {
    console.log('Using fetch API');
    fetch('<?= base_url('produk/getAllProduk') ?>', {
      method: 'GET',
      headers: {
        'X-Requested-With': 'XMLHttpRequest'
      }
    })
    .then(response => response.json())
    .then(data => {
      console.log('Fetch success:', data);
      if (data.success && data.data) {
        renderProducts(data.data);
      } else {
        showProductError('Gagal memuat produk: ' + (data.message || 'Unknown error'));
      }
    })
    .catch(error => {
      console.error('Fetch error:', error);
      showProductError('Error loading products: ' + error.message);
    });
  }
}

function showProductError(message) {
  const productGrid = document.getElementById('productGrid');
  if (productGrid) {
    productGrid.innerHTML = `
      <div class="text-center py-4">
        <i class="bi bi-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
        <p class="text-muted mt-2">Gagal memuat produk</p>
        <p class="text-muted">${message}</p>
        <button class="btn btn-primary mt-2" onclick="loadProducts()">Coba Lagi</button>
      </div>
    `;
  }
}

// Render products to grid
function renderProducts(products) {
  console.log('Rendering products:', products);
  const productGrid = document.getElementById('productGrid');

  if (!productGrid) {
    console.error('Product grid element not found!');
    return;
  }

  if (products.length === 0) {
    console.log('No products to display');
    productGrid.innerHTML = `
      <div class="text-center py-4">
        <i class="bi bi-box text-muted" style="font-size: 3rem;"></i>
        <p class="text-muted mt-2">Belum ada produk</p>
        <p class="text-muted">Tambahkan produk melalui menu Manajemen Stok</p>
      </div>
    `;
    return;
  }

  let html = '';
  products.forEach(product => {
    const stok = parseInt(product.stok);
    let stockBadge = '';
    let stockClass = '';

    if (stok <= 0) {
      stockBadge = 'Habis';
      stockClass = 'badge-stock-empty';
    } else if (stok <= 10) {
      stockBadge = 'Rendah';
      stockClass = 'badge-stock-low';
    } else {
      stockBadge = 'Ready';
      stockClass = 'badge-stock';
    }

    html += `
      <div class="product-card ${stok <= 0 ? 'product-disabled' : ''}"
           onclick="${stok > 0 ? `addToCart(${product.id}, '${product.nama_produk}', ${product.harga_jual}, ${product.stok})` : 'void(0)'}"
           data-category="${product.kategori.toLowerCase()}">
        <div class="product-name">${product.nama_produk}</div>
        <div class="product-price">Rp ${parseInt(product.harga_jual).toLocaleString('id-ID').replace(/,/g, '.')}</div>
        <div class="product-stock">Stok: ${stok} <span class="${stockClass}">${stockBadge}</span></div>
        ${product.kategori ? `<div class="product-category">${product.kategori}</div>` : ''}
      </div>
    `;
  });

  console.log('Generated HTML:', html);
  productGrid.innerHTML = html;
  console.log('Product grid updated, children count:', productGrid.children.length);
}

// Handle barcode input
function handleBarcode(event) {
  if (event.key === 'Enter') {
    const barcode = event.target.value;
    if (barcode) {
      searchProductByBarcode(barcode);
      event.target.value = '';
    }
  }
}

// Search product by barcode
function searchProductByBarcode(barcode) {
  if (!barcode.trim()) return;

  // Show loading
  Swal.fire({
    title: 'Mencari...',
    text: 'Mencari produk dengan barcode: ' + barcode,
    allowOutsideClick: false,
    didOpen: () => {
      Swal.showLoading();
    }
  });

  // Search in current products
  fetch('<?= base_url('produk/getAllProduk') ?>', {
    method: 'GET',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => response.json())
  .then(data => {
    Swal.close();

    if (data.success) {
      const product = data.data.find(p => p.barcode === barcode);

      if (product) {
        if (product.stok > 0) {
          addToCart(product.id, product.nama_produk, product.harga_jual, product.stok);
        } else {
          Swal.fire('Stok Habis', `Produk "${product.nama_produk}" stok habis`, 'warning');
        }
      } else {
        Swal.fire('Tidak Ditemukan', `Produk dengan barcode "${barcode}" tidak ditemukan`, 'info');
      }
    } else {
      Swal.fire('Error', 'Gagal mencari produk', 'error');
    }
  })
  .catch(error => {
    Swal.close();
    console.error('Error searching product:', error);
    Swal.fire('Error', 'Terjadi kesalahan saat mencari produk', 'error');
  });
}

// Filter products by category
function filterProducts(category) {
  // Update active button
  document.querySelectorAll('.btn-group .btn').forEach(btn => btn.classList.remove('active'));
  event.target.classList.add('active');

  // Show/hide products based on category
  const productCards = document.querySelectorAll('.product-card');

  productCards.forEach(card => {
    if (category === 'all') {
      card.style.display = 'block';
    } else {
      const productCategory = card.getAttribute('data-category');
      if (productCategory === category) {
        card.style.display = 'block';
      } else {
        card.style.display = 'none';
      }
    }
  });
}

// Show pulsa modal (placeholder)
function showPulsaModal() {
  Swal.fire('Info', 'Fitur top-up pulsa akan segera tersedia', 'info');
}

// Show e-wallet modal (placeholder)
function showEwalletModal() {
  Swal.fire('Info', 'Fitur top-up e-wallet akan segera tersedia', 'info');
}

// Show cart modal
function showCartModal() {
  const modal = new bootstrap.Modal(document.getElementById('modalKeranjang'));
  modal.show();
}

// Add product to cart
function addToCart(productId, productName, price, stock) {
  // Check if product already in cart
  const existingItem = cart.find(item => item.type === 'product' && item.id === productId);

  if (existingItem) {
    // Increase quantity if stock allows
    if (existingItem.quantity < stock) {
      existingItem.quantity++;
      existingItem.subtotal = existingItem.quantity * existingItem.price;
    } else {
      Swal.fire('Warning', 'Stok tidak mencukupi', 'warning');
      return;
    }
  } else {
    // Add new item to cart
    cart.push({
      id: productId,
      type: 'product',
      name: productName,
      price: price,
      quantity: 1,
      subtotal: price,
      stock: stock
    });
  }

  updateCartDisplay();
  saveCartToStorage(); // Save to localStorage

  // Show success notification
  Swal.fire({
    title: 'Ditambahkan!',
    text: `${productName} ditambahkan ke keranjang`,
    icon: 'success',
    timer: 1500,
    showConfirmButton: false
  });
}

// Update cart display
function updateCartDisplay() {
  const cartItems = document.getElementById('cartItems');
  const cartItemCount = document.getElementById('cartItemCount');
  const cartTotal = document.getElementById('cartTotal');
  const cartTotalSection = document.getElementById('cartTotalSection');
  const checkoutBtn = document.getElementById('checkoutBtn');
  const clearCartBtn = document.getElementById('clearCartBtn');

  // Update item count
  const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
  cartItemCount.textContent = totalItems;

  // Update total price
  const totalPrice = cart.reduce((sum, item) => sum + item.subtotal, 0);
  cartTotal.textContent = formatPrice(totalPrice);

  if (cart.length === 0) {
    // Empty cart
    cartItems.innerHTML = `
      <div class="text-center py-4">
        <i class="bi bi-cart text-muted" style="font-size: 3rem;"></i>
        <p class="text-muted mt-2">Keranjang kosong</p>
        <p class="text-muted">Pilih produk untuk ditambahkan ke keranjang</p>
      </div>
    `;
    cartTotalSection.style.display = 'none';
    checkoutBtn.disabled = true;
    clearCartBtn.style.display = 'none';
  } else {
    // Show cart items
    let html = '';
    cart.forEach(item => {
      if (item.type === 'product') {
        // Product item with quantity controls
        html += `
          <div class="cart-item">
            <div class="item-info">
              <div class="item-name">${item.name}</div>
              <div class="item-price">${formatPrice(item.price)} x ${item.quantity}</div>
            </div>
            <div class="item-controls">
              <button class="qty-btn" onclick="decreaseQuantity('${item.id}')">
                <i class="bi bi-dash"></i>
              </button>
              <input type="number" class="qty-input" value="${item.quantity}" min="1" max="${item.stock}" onchange="updateQuantity('${item.id}', this.value)">
              <button class="qty-btn" onclick="increaseQuantity('${item.id}')">
                <i class="bi bi-plus"></i>
              </button>
              <button class="qty-btn text-danger" onclick="removeFromCart('${item.id}')">
                <i class="bi bi-trash"></i>
              </button>
            </div>
          </div>
        `;
      } else {
        // Service item (session payment, topup, etc.) - no quantity controls
        html += `
          <div class="cart-item">
            <div class="item-info">
              <div class="item-name">${item.name}</div>
              <div class="item-price">${formatPrice(item.price)}</div>
              ${item.type === 'session_payment' && item.metode_bayar ? `<small class="text-muted">Metode: ${item.metode_bayar}</small>` : ''}
              ${item.kembalian ? `<small class="text-success">Kembalian: Rp ${item.kembalian.toLocaleString('id-ID')}</small>` : ''}
            </div>
            <div class="item-controls">
              <button class="qty-btn text-danger" onclick="removeFromCart('${item.id}')">
                <i class="bi bi-trash"></i>
              </button>
            </div>
          </div>
        `;
      }
    });

    cartItems.innerHTML = html;
    cartTotalSection.style.display = 'flex';
    checkoutBtn.disabled = false;
    clearCartBtn.style.display = 'inline-block';
  }
}

// Increase quantity (only for products)
function increaseQuantity(itemId) {
  const item = cart.find(item => item.id === itemId && item.type === 'product');
  if (item && item.quantity < item.stock) {
    item.quantity++;
    item.subtotal = item.quantity * item.price;
    updateCartDisplay();
    saveCartToStorage(); // Save to localStorage
  } else {
    Swal.fire('Warning', 'Stok tidak mencukupi', 'warning');
  }
}

// Decrease quantity (only for products)
function decreaseQuantity(itemId) {
  const item = cart.find(item => item.id === itemId && item.type === 'product');
  if (item && item.quantity > 1) {
    item.quantity--;
    item.subtotal = item.quantity * item.price;
    updateCartDisplay();
    saveCartToStorage(); // Save to localStorage
  } else if (item && item.quantity === 1) {
    removeFromCart(itemId);
  }
}

// Update quantity manually (only for products)
function updateQuantity(itemId, newQuantity) {
  const item = cart.find(item => item.id === itemId && item.type === 'product');
  const quantity = parseInt(newQuantity);

  if (item && quantity > 0 && quantity <= item.stock) {
    item.quantity = quantity;
    item.subtotal = item.quantity * item.price;
    updateCartDisplay();
    saveCartToStorage(); // Save to localStorage
  } else if (quantity <= 0) {
    removeFromCart(itemId);
  } else {
    Swal.fire('Warning', 'Stok tidak mencukupi', 'warning');
    updateCartDisplay(); // Reset display
  }
}

// Remove item from cart
function removeFromCart(itemId) {
  const itemIndex = cart.findIndex(item => item.id === itemId);
  if (itemIndex > -1) {
    cart.splice(itemIndex, 1);
    updateCartDisplay();
    saveCartToStorage(); // Save to localStorage
  }
}

// Clear entire cart
function clearCart() {
  Swal.fire({
    title: 'Kosongkan Keranjang?',
    text: 'Semua item akan dihapus dari keranjang',
    icon: 'warning',
    showCancelButton: true,
    confirmButtonText: 'Ya, Kosongkan',
    cancelButtonText: 'Batal'
  }).then((result) => {
    if (result.isConfirmed) {
      cart = [];
      updateCartDisplay();
      clearCartStorage(); // Clear localStorage
      Swal.fire('Dikosongkan!', 'Keranjang telah dikosongkan', 'success');
    }
  });
}

// Process checkout
function processCheckout() {
  if (cart.length === 0) {
    Swal.fire('Error', 'Keranjang masih kosong', 'error');
    return;
  }

  // Close cart modal first
  const cartModal = bootstrap.Modal.getInstance(document.getElementById('modalKeranjang'));
  cartModal.hide();

  // Prepare checkout data
  const totalPrice = cart.reduce((sum, item) => sum + item.subtotal, 0);
  const totalItems = cart.reduce((sum, item) => {
    if (item.type === 'product') {
      return sum + item.quantity;
    } else {
      return sum + 1;
    }
  }, 0);

  // Update checkout modal content
  document.getElementById('checkoutTotalItems').textContent = totalItems;
  document.getElementById('checkoutTotalAmount').textContent = formatPrice(totalPrice);

  // Build items list
  let itemsHtml = '';
  cart.forEach(item => {
    if (item.type === 'product') {
      itemsHtml += `
        <div class="d-flex justify-content-between align-items-center py-1">
          <span>${item.name} (${item.quantity}x)</span>
          <span class="text-success">${formatPrice(item.subtotal)}</span>
        </div>
      `;
    } else {
      itemsHtml += `
        <div class="d-flex justify-content-between align-items-center py-1">
          <span>${item.name}</span>
          <span class="text-success">${formatPrice(item.subtotal)}</span>
        </div>
      `;
    }
  });

  document.getElementById('checkoutItemsList').innerHTML = itemsHtml;

  // Reset metode pembayaran ke tunai
  document.getElementById('metodeTunai').checked = true;

  // Show checkout confirmation modal
  const checkoutModal = new bootstrap.Modal(document.getElementById('modalKonfirmasiPembayaran'));
  checkoutModal.show();

  // Initialize payment fields
  togglePaymentFields();
}

// Process all transactions in cart
function processAllTransactions(metodePembayaran = 'tunai') {
  const sessionPayments = cart.filter(item => item.type === 'session_payment');
  const memberTopups = cart.filter(item => item.type === 'member_topup');
  const products = cart.filter(item => item.type === 'product');

  let processedCount = 0;
  const totalTransactions = sessionPayments.length + memberTopups.length + (products.length > 0 ? 1 : 0);

  // Process session payments
  sessionPayments.forEach(item => {
    // Update item with selected payment method if not already set
    if (!item.metode_bayar) {
      item.metode_bayar = metodePembayaran;
    }
    processSessionPayment(item, metodePembayaran).then(() => {
      processedCount++;
      if (processedCount === totalTransactions) {
        // Only clear cart and refresh after ALL transactions are complete
        cart = [];
        updateCartDisplay();
        clearCartStorage(); // Clear localStorage
        refreshUnpaidSessions();
      }
    });
  });

  // Process member topups
  memberTopups.forEach(item => {
    processMemberTopup(item, metodePembayaran).then(() => {
      processedCount++;
      if (processedCount === totalTransactions) {
        // Only clear cart and refresh after ALL transactions are complete
        cart = [];
        updateCartDisplay();
        clearCartStorage(); // Clear localStorage
        refreshUnpaidSessions();
      }
    });
  });

  // Process product sales (if any)
  if (products.length > 0) {
    processProductSales(products, metodePembayaran).then(() => {
      processedCount++;
      if (processedCount === totalTransactions) {
        // Only clear cart and refresh after ALL transactions are complete
        cart = [];
        updateCartDisplay();
        clearCartStorage(); // Clear localStorage
        refreshUnpaidSessions();
      }
    });
  }

  // If no transactions to process
  if (totalTransactions === 0) {
    cart = [];
    updateCartDisplay();
    clearCartStorage(); // Clear localStorage
    refreshUnpaidSessions();
  }
}

// Process session payment
function processSessionPayment(item, metodePembayaran = 'tunai') {
  return new Promise((resolve, reject) => {
    const formData = new FormData();
    formData.append('sesi_id', item.sessionId);
    formData.append('metode_bayar', item.metode_bayar || metodePembayaran);
    formData.append('jumlah_bayar', item.price);

    fetch('<?= base_url('kasir/bayarSesi') ?>', {
      method: 'POST',
      headers: {
        'X-Requested-With': 'XMLHttpRequest'
      },
      body: formData
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // Remove session from unpaid list
        const sessionElement = document.querySelector(`[data-session-id="${item.sessionId}"]`);
        if (sessionElement) {
          sessionElement.remove();
        }
        resolve();
      } else {
        reject(data.message);
      }
    })
    .catch(error => {
      reject(error);
    });
  });
}

// Process member topup
function processMemberTopup(item, metodePembayaran = 'tunai') {
  return new Promise((resolve, reject) => {
    const formData = new FormData();
    formData.append('member_id', item.memberId);
    formData.append('jumlah', item.price);
    formData.append('metode_bayar', metodePembayaran);

    fetch('<?= base_url('kasir/topupMember') ?>', {
      method: 'POST',
      headers: {
        'X-Requested-With': 'XMLHttpRequest'
      },
      body: formData
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // Update member saldo display in table
        const saldoElement = document.querySelector(`.member-saldo[data-member-id="${item.memberId}"]`);
        if (saldoElement) {
          saldoElement.textContent = `Rp ${parseInt(data.saldo_baru).toLocaleString('id-ID')}`;
        }
        resolve();
      } else {
        reject(data.message);
      }
    })
    .catch(error => {
      reject(error);
    });
  });
}

// Process product sales
function processProductSales(products, metodePembayaran = 'tunai') {
  return new Promise((resolve, reject) => {
    // For now, just simulate success
    // In real implementation, you would save to transaksi and transaksi_detail tables
    console.log('Processing product sales:', products, 'Payment method:', metodePembayaran);
    setTimeout(() => {
      resolve();
    }, 500);
  });
}

// Toggle payment fields based on selected method
function togglePaymentFields() {
  const selectedMethod = document.querySelector('input[name="metodePembayaran"]:checked');
  const cashFields = document.getElementById('cashPaymentFields');

  if (selectedMethod && selectedMethod.value === 'tunai') {
    cashFields.classList.remove('d-none');
    // Set default amount to total
    const totalAmount = cart.reduce((sum, item) => sum + item.subtotal, 0);
    document.getElementById('jumlahBayarInput').value = totalAmount;
    hitungKembalian();
  } else {
    cashFields.classList.add('d-none');
  }
}

// Hitung kembalian untuk pembayaran tunai
function hitungKembalian() {
  const totalAmount = cart.reduce((sum, item) => sum + item.subtotal, 0);
  const jumlahBayar = parseInt(document.getElementById('jumlahBayarInput').value) || 0;
  const kembalian = Math.max(0, jumlahBayar - totalAmount);

  document.getElementById('kembalianDisplay').value = kembalian.toLocaleString('id-ID');
}

// Konfirmasi pembayaran final dengan metode pembayaran
function konfirmasiPembayaranFinal() {
  // Get selected payment method
  const selectedMethod = document.querySelector('input[name="metodePembayaran"]:checked');
  if (!selectedMethod) {
    Swal.fire('Error', 'Pilih metode pembayaran terlebih dahulu', 'error');
    return;
  }

  const metodePembayaran = selectedMethod.value;
  let jumlahBayar = 0;

  // For cash payment, validate amount
  if (metodePembayaran === 'tunai') {
    const totalAmount = cart.reduce((sum, item) => sum + item.subtotal, 0);
    jumlahBayar = parseInt(document.getElementById('jumlahBayarInput').value) || 0;

    if (jumlahBayar < totalAmount) {
      Swal.fire('Error', 'Jumlah bayar tidak boleh kurang dari total', 'error');
      return;
    }
  }

  // Close checkout modal
  const checkoutModal = bootstrap.Modal.getInstance(document.getElementById('modalKonfirmasiPembayaran'));
  checkoutModal.hide();

  // Process checkout with new integrated system
  processCheckoutIntegrated(metodePembayaran, jumlahBayar);
}

// Process checkout with integrated database system
function processCheckoutIntegrated(metodePembayaran, jumlahBayar = 0) {
  if (cart.length === 0) {
    Swal.fire('Error', 'Keranjang kosong', 'error');
    return;
  }

  // Show loading
  Swal.fire({
    title: 'Memproses...',
    text: 'Sedang memproses transaksi',
    allowOutsideClick: false,
    didOpen: () => {
      Swal.showLoading();
    }
  });

  // Calculate total
  const totalHarga = cart.reduce((sum, item) => sum + item.subtotal, 0);

  // Set jumlah bayar based on payment method
  if (metodePembayaran !== 'tunai') {
    jumlahBayar = totalHarga; // Exact payment for non-cash
  }

  // Prepare data for backend
  const checkoutData = {
    cart_items: cart,
    metode_pembayaran: metodePembayaran,
    jumlah_bayar: jumlahBayar
  };

  // Send to backend
  fetch('<?= base_url('kasir/processCheckout') ?>', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Requested-With': 'XMLHttpRequest'
    },
    body: JSON.stringify(checkoutData)
  })
  .then(response => response.json())
  .then(data => {
    Swal.close();

    if (data.success) {
      // Show receipt modal
      showReceiptModal(data, metodePembayaran, cart);

      // Clear cart and refresh
      cart = [];
      updateCartDisplay();
      clearCartStorage(); // Clear localStorage
      refreshUnpaidSessions();
    } else {
      Swal.fire('Error', data.message || 'Gagal memproses transaksi', 'error');
    }
  })
  .catch(error => {
    Swal.close();
    console.error('Checkout error:', error);
    Swal.fire('Error', 'Terjadi kesalahan saat memproses transaksi', 'error');
  });
}

// Show receipt modal
function showReceiptModal(transactionData, metodePembayaran, cartItems) {
  const now = new Date();
  const tanggal = now.toLocaleDateString('id-ID', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
  const waktu = now.toLocaleTimeString('id-ID', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });

  // Calculate total items
  const totalItems = cartItems.reduce((sum, item) => sum + (item.quantity || 1), 0);

  // Get rental settings for struk header
  fetch('<?= base_url('setting/getRentalSettings') ?>')
    .then(response => response.json())
    .then(data => {
      const rentalSettings = data.success ? data.data : {
        nama_rental: 'PlaySphere Gaming Center',
        slogan_rental: 'Gaming Center & Internet Cafe',
        alamat_rental: 'Jl. Gaming Center No. 123',
        whatsapp_rental: '08123456789',
        logo_rental: 'assets/images/default-logo.svg'
      };

      let strukHtml = `
        <div class="struk-header">
          <div class="struk-logo">
            <img src="<?= base_url() ?>${rentalSettings.logo_rental}" alt="Logo" style="width: 40px; height: 40px; object-fit: contain;">
          </div>
          <h3>${rentalSettings.nama_rental.toUpperCase()}</h3>
          <div class="subtitle">${rentalSettings.slogan_rental}</div>
          <div class="subtitle">${rentalSettings.alamat_rental}</div>
          <div class="subtitle">WA: ${rentalSettings.whatsapp_rental}</div>
        </div>

    <div class="struk-info">
      <div><span>No. Transaksi</span><span>${transactionData.nomor_transaksi}</span></div>
      <div><span>Tanggal</span><span>${tanggal}</span></div>
      <div><span>Waktu</span><span>${waktu}</span></div>
      <div><span>Kasir</span><span>Kasir</span></div>
    </div>

    <div class="struk-separator">
      ================================
    </div>

    <div class="struk-items">
  `;

  // Add items with quantity and details
  cartItems.forEach(item => {
    const quantity = item.quantity || 1;
    const unitPrice = item.price;

    strukHtml += `
      <div class="struk-item">
        <div class="struk-item-name">${item.name}</div>
        <div class="struk-item-detail">
          <span>@${unitPrice.toLocaleString('id-ID')} (${quantity}x)</span>
          <span>Rp ${item.subtotal.toLocaleString('id-ID')}</span>
        </div>
      </div>
    `;
  });

  strukHtml += `
    </div>

    <div class="struk-separator">
      ================================
    </div>

    <div class="struk-summary">
      <div><span>Total Item</span><span>${totalItems}</span></div>
    </div>

    <div class="struk-total">
      <div><span>TOTAL</span><span>Rp ${transactionData.total_harga.toLocaleString('id-ID')}</span></div>
    </div>

    <div class="struk-payment">
      <div><span>Bayar (${metodePembayaran.toUpperCase()})</span><span>Rp ${transactionData.jumlah_bayar.toLocaleString('id-ID')}</span></div>
  `;

  if (transactionData.kembalian > 0) {
    strukHtml += `
    </div>

    <div class="struk-change">
      <div><span>KEMBALIAN</span><span>Rp ${transactionData.kembalian.toLocaleString('id-ID')}</span></div>
    </div>
    `;
  } else {
    strukHtml += `</div>`;
  }

      strukHtml += `
        </div>

        <div class="struk-footer">
          <div class="thank-you">TERIMA KASIH</div>
          <div>Selamat bermain dan sampai jumpa lagi!</div>
          <div>WA: ${rentalSettings.whatsapp_rental}</div>
          <div>=====================================</div>
        </div>
      `;

      // Set content and show modal
      document.getElementById('strukContent').innerHTML = strukHtml;
      const strukModal = new bootstrap.Modal(document.getElementById('modalStruk'));
      strukModal.show();

      // Auto print if printer available (simulate)
      setTimeout(() => {
        if (navigator.userAgent.includes('Chrome')) {
          // Auto print for Chrome (simulate printer detection)
          // In real implementation, check for actual printer
          console.log('Auto printing...');
          // cetakStruk(); // Uncomment for auto print
        }
      }, 500);
    })
    .catch(error => {
      console.error('Error loading rental settings:', error);
      // Fallback to default struk if rental settings fail
      showDefaultReceipt(transactionData, metodePembayaran, cartItems, tanggal, waktu, totalItems);
    });
}

// Fallback function for default receipt
function showDefaultReceipt(transactionData, metodePembayaran, cartItems, tanggal, waktu, totalItems) {
  let strukHtml = `
    <div class="struk-header">
      <h3>PLAYSPHERE GAMING</h3>
      <div class="subtitle">Gaming Center & Internet Cafe</div>
      <div class="subtitle">Jl. Gaming Center No. 123</div>
      <div class="subtitle">WA: 08123456789</div>
    </div>

    <div class="struk-info">
      <div><span>No. Transaksi</span><span>${transactionData.nomor_transaksi}</span></div>
      <div><span>Tanggal</span><span>${tanggal}</span></div>
      <div><span>Waktu</span><span>${waktu}</span></div>
      <div><span>Kasir</span><span>Kasir</span></div>
    </div>

    <div class="struk-separator">
      ================================
    </div>

    <div class="struk-items">
  `;

  // Add items with quantity and details
  cartItems.forEach(item => {
    const quantity = item.quantity || 1;
    const unitPrice = item.price;

    strukHtml += `
      <div class="struk-item">
        <div class="struk-item-name">${item.name}</div>
        <div class="struk-item-detail">
          <span>@${unitPrice.toLocaleString('id-ID')} (${quantity}x)</span>
          <span>Rp ${item.subtotal.toLocaleString('id-ID')}</span>
        </div>
      </div>
    `;
  });

  strukHtml += `
    </div>

    <div class="struk-separator">
      ================================
    </div>

    <div class="struk-summary">
      <div><span>Total Item</span><span>${totalItems}</span></div>
    </div>

    <div class="struk-total">
      <div><span>TOTAL</span><span>Rp ${transactionData.total_harga.toLocaleString('id-ID')}</span></div>
    </div>

    <div class="struk-payment">
      <div><span>Bayar (${metodePembayaran.toUpperCase()})</span><span>Rp ${transactionData.jumlah_bayar.toLocaleString('id-ID')}</span></div>
  `;

  if (transactionData.kembalian > 0) {
    strukHtml += `
    </div>

    <div class="struk-change">
      <div><span>KEMBALIAN</span><span>Rp ${transactionData.kembalian.toLocaleString('id-ID')}</span></div>
    </div>
    `;
  } else {
    strukHtml += `</div>`;
  }

  strukHtml += `
    </div>

    <div class="struk-footer">
      <div class="thank-you">TERIMA KASIH</div>
      <div>Selamat bermain dan sampai jumpa lagi!</div>
      <div>WA: 08123456789</div>
      <div>=====================================</div>
    </div>
  `;

  // Set content and show modal
  document.getElementById('strukContent').innerHTML = strukHtml;
  const strukModal = new bootstrap.Modal(document.getElementById('modalStruk'));
  strukModal.show();
}

// Print receipt function
function cetakStruk() {
  const strukContent = document.getElementById('strukContent').innerHTML;
  const printWindow = window.open('', '_blank');

  printWindow.document.write(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>Struk Pembayaran - ${new Date().toLocaleDateString('id-ID')}</title>
      <style>
        body {
          font-family: 'Courier New', monospace;
          margin: 0;
          padding: 10px;
          font-size: 11px;
          line-height: 1.2;
        }
        .struk-container {
          max-width: 280px;
          margin: 0 auto;
          background: white;
        }
        .struk-header {
          text-align: center;
          border-bottom: 2px solid #000;
          padding-bottom: 8px;
          margin-bottom: 12px;
        }
        .struk-header h3 {
          margin: 0 0 5px 0;
          font-size: 14px;
          font-weight: bold;
          letter-spacing: 1px;
        }
        .struk-header .subtitle {
          font-size: 9px;
          margin: 2px 0;
        }
        .struk-info {
          margin-bottom: 12px;
          font-size: 10px;
        }
        .struk-info div {
          display: flex;
          justify-content: space-between;
          margin-bottom: 1px;
        }
        .struk-separator {
          text-align: center;
          margin: 8px 0;
          font-size: 10px;
        }
        .struk-items {
          border-top: 1px dashed #000;
          border-bottom: 1px dashed #000;
          padding: 8px 0;
          margin: 12px 0;
        }
        .struk-item {
          margin-bottom: 6px;
          font-size: 10px;
        }
        .struk-item-name {
          font-weight: bold;
          margin-bottom: 1px;
        }
        .struk-item-detail {
          display: flex;
          justify-content: space-between;
          font-size: 9px;
          margin-left: 5px;
        }
        .struk-summary {
          border-top: 1px solid #000;
          padding-top: 8px;
          margin-top: 8px;
          font-size: 10px;
        }
        .struk-summary div {
          display: flex;
          justify-content: space-between;
          margin-bottom: 2px;
        }
        .struk-total {
          margin-top: 8px;
          font-size: 11px;
          font-weight: bold;
          border-top: 2px solid #000;
          border-bottom: 2px solid #000;
          padding: 5px 0;
        }
        .struk-total div {
          display: flex;
          justify-content: space-between;
          margin-bottom: 2px;
        }
        .struk-payment {
          margin-top: 8px;
          font-size: 10px;
        }
        .struk-payment div {
          display: flex;
          justify-content: space-between;
          margin-bottom: 2px;
        }
        .struk-change {
          font-weight: bold;
          font-size: 11px;
          border-top: 1px dashed #000;
          padding-top: 5px;
          margin-top: 5px;
        }
        .struk-change div {
          display: flex;
          justify-content: space-between;
          margin-bottom: 2px;
        }
        .struk-footer {
          text-align: center;
          margin-top: 15px;
          font-size: 9px;
          border-top: 1px dashed #000;
          padding-top: 8px;
        }
        .struk-footer .thank-you {
          font-weight: bold;
          margin-bottom: 3px;
        }
        @media print {
          @page {
            size: 58mm auto;
            margin: 5mm 5mm 6mm 5mm; /* top right bottom left */
          }
          body {
            margin: 0;
            padding: 0;
            font-size: 10px; /* Increased from 8px */
            width: 48mm; /* 58mm - 10mm (left+right margin) */
          }
          .struk-container {
            max-width: 48mm;
            width: 48mm;
            padding: 0;
            border: none;
            box-shadow: none;
          }
          .struk-container::before {
            display: none;
          }
          .struk-header h3 {
            font-size: 12px; /* Increased from 10px */
            margin-bottom: 3px;
          }
          .struk-header .subtitle {
            font-size: 9px; /* Increased from 7px */
            margin: 1px 0;
          }
          .struk-info {
            font-size: 9px; /* Increased from 7px */
            margin-bottom: 8px;
          }
          .struk-separator {
            font-size: 9px;
            margin: 6px 0;
          }
          .struk-items {
            padding: 6px 0;
            margin: 8px 0;
          }
          .struk-item {
            margin-bottom: 4px;
          }
          .struk-item-name {
            font-size: 10px; /* Increased from 8px */
            margin-bottom: 1px;
          }
          .struk-item-detail {
            font-size: 9px; /* Increased from 7px */
          }
          .struk-summary {
            font-size: 9px;
            padding-top: 6px;
            margin-top: 6px;
          }
          .struk-total {
            font-size: 11px; /* Increased from 9px */
            padding: 4px 0;
            margin-top: 6px;
          }
          .struk-payment {
            font-size: 10px; /* Increased from 8px */
            margin-top: 6px;
          }
          .struk-change {
            font-size: 11px; /* Increased from 9px */
            padding-top: 4px;
            margin-top: 4px;
          }
          .struk-footer {
            font-size: 8px; /* Increased from 7px */
            margin-top: 10px;
            padding-top: 6px;
          }
          .struk-footer .thank-you {
            font-size: 9px;
            margin-bottom: 2px;
          }
        }
      </style>
    </head>
    <body>
      <div class="struk-container">
        ${strukContent}
      </div>
    </body>
    </html>
  `);

  printWindow.document.close();
  printWindow.focus();
  printWindow.print();
  printWindow.close();
}

// Member data and pagination
let allMembers = <?= json_encode($members) ?>;
let currentMemberPage = 1;
const membersPerPage = 24; // Increased to accommodate more cards per page

// Load members for top-up tab
function loadMembers() {
  console.log('Loading members for top-up...');
  renderMemberCards();
}

// Render member cards with pagination
function renderMemberCards(page = 1) {
  currentMemberPage = page;
  const container = document.getElementById('memberCardsContainer');
  const startIndex = (page - 1) * membersPerPage;
  const endIndex = startIndex + membersPerPage;
  const membersToShow = allMembers.slice(startIndex, endIndex);

  let cardsHtml = '';

  membersToShow.forEach(member => {
    const statusClass = getStatusClass(member.saldo);
    const statusText = getStatusText(member.saldo);

    cardsHtml += `
      <div class="member-card ${statusClass}" onclick="showTopupModal(${member.id}, '${escapeHtml(member.nama)}', ${member.saldo})">
        <div class="member-card-id">
          <i class="bi bi-person-badge"></i>
          ID: ${escapeHtml(member.id_member)}
        </div>
        <div class="member-card-name">
          <i class="bi bi-person-circle"></i>
          ${escapeHtml(member.nama)}
        </div>
        <div class="member-card-saldo member-saldo" data-member-id="${member.id}">
          Rp ${parseInt(member.saldo).toLocaleString('id-ID')}
        </div>
      </div>
    `;
  });

  container.innerHTML = cardsHtml;
  renderMemberPagination();
}

// Get status class based on saldo
function getStatusClass(saldo) {
  if (saldo > 15000) return 'status-active';
  if (saldo > 5000) return 'status-low';
  return 'status-critical';
}

// Get status text based on saldo
function getStatusText(saldo) {
  if (saldo > 15000) return 'Aktif';
  if (saldo > 5000) return 'Saldo Rendah';
  return 'Perlu Top Up';
}

// Render pagination for members
function renderMemberPagination() {
  const totalPages = Math.ceil(allMembers.length / membersPerPage);
  const pagination = document.getElementById('memberPagination');

  if (totalPages <= 1) {
    pagination.innerHTML = '';
    return;
  }

  let paginationHtml = '';

  // Previous button
  if (currentMemberPage > 1) {
    paginationHtml += `
      <li class="page-item">
        <a class="page-link" href="#" onclick="renderMemberCards(${currentMemberPage - 1}); return false;">
          <i class="bi bi-chevron-left"></i>
        </a>
      </li>
    `;
  }

  // Page numbers
  for (let i = 1; i <= totalPages; i++) {
    if (i === currentMemberPage) {
      paginationHtml += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
    } else {
      paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="renderMemberCards(${i}); return false;">${i}</a></li>`;
    }
  }

  // Next button
  if (currentMemberPage < totalPages) {
    paginationHtml += `
      <li class="page-item">
        <a class="page-link" href="#" onclick="renderMemberCards(${currentMemberPage + 1}); return false;">
          <i class="bi bi-chevron-right"></i>
        </a>
      </li>
    `;
  }

  pagination.innerHTML = paginationHtml;
}

// Escape HTML to prevent XSS
function escapeHtml(text) {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

// Load products for penjualan tab
function loadProducts() {
  console.log('Loading products...');
  // Products are already loaded in the HTML, no need to fetch again
  // This function exists to prevent the error in switchTab
}

// Load transaction list
function loadTransaksiList(page = 1) {
  const filterTanggal = document.getElementById('filterTanggal').value;
  const params = new URLSearchParams({
    page: page,
    limit: 10
  });

  if (filterTanggal) {
    params.append('tanggal', filterTanggal);
  }

  fetch(`<?= base_url('kasir/getTransaksiList') ?>?${params}`, {
    method: 'GET',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      updateTransaksiTable(data.transactions);
      updatePagination(data.pagination);
    } else {
      document.getElementById('transaksiTableBody').innerHTML = `
        <tr>
          <td colspan="6" class="text-center text-muted">
            <i class="bi bi-exclamation-circle me-1"></i>
            ${data.message || 'Gagal memuat data transaksi'}
          </td>
        </tr>
      `;
    }
  })
  .catch(error => {
    console.error('Error loading transactions:', error);
    document.getElementById('transaksiTableBody').innerHTML = `
      <tr>
        <td colspan="6" class="text-center text-danger">
          <i class="bi bi-x-circle me-1"></i>
          Terjadi kesalahan saat memuat data
        </td>
      </tr>
    `;
  });
}

// Update transaction table
function updateTransaksiTable(transactions) {
  const tbody = document.getElementById('transaksiTableBody');

  if (transactions.length === 0) {
    tbody.innerHTML = `
      <tr>
        <td colspan="6" class="text-center text-muted">
          <i class="bi bi-inbox me-1"></i>
          Tidak ada transaksi ditemukan
        </td>
      </tr>
    `;
    return;
  }

  let html = '';
  transactions.forEach(transaction => {
    const tanggal = new Date(transaction.tanggal_transaksi).toLocaleDateString('id-ID', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    html += `
      <tr>
        <td>
          <small class="text-muted">${transaction.nomor_transaksi}</small>
        </td>
        <td>${tanggal}</td>
        <td class="text-center">${transaction.total_item}</td>
        <td class="text-end">Rp ${parseInt(transaction.total_harga).toLocaleString('id-ID')}</td>
        <td>
          <span class="badge bg-${getPaymentMethodColor(transaction.metode_bayar)}">
            ${transaction.metode_bayar.toUpperCase()}
          </span>
        </td>
        <td>
          <div class="btn-group btn-group-sm" role="group">
            <button class="btn btn-outline-primary" onclick="viewTransactionDetail(${transaction.id})" title="Detail">
              <i class="bi bi-eye"></i>
            </button>
            <button class="btn btn-outline-success" onclick="reprintReceipt(${transaction.id})" title="Cetak Ulang">
              <i class="bi bi-printer"></i>
            </button>
            <button class="btn btn-outline-danger" onclick="deleteTransaction(${transaction.id})" title="Hapus">
              <i class="bi bi-trash"></i>
            </button>
          </div>
        </td>
      </tr>
    `;
  });

  tbody.innerHTML = html;
}

// Get payment method color for badge
function getPaymentMethodColor(method) {
  switch(method) {
    case 'tunai': return 'success';
    case 'qris': return 'primary';
    case 'debit': return 'info';
    case 'transfer': return 'warning';
    default: return 'secondary';
  }
}

// Update pagination
function updatePagination(pagination) {
  const paginationEl = document.getElementById('transaksiPagination');

  if (pagination.total_pages <= 1) {
    paginationEl.innerHTML = '';
    return;
  }

  let html = '';

  // Previous button
  if (pagination.current_page > 1) {
    html += `
      <li class="page-item">
        <a class="page-link" href="#" onclick="loadTransaksiList(${pagination.current_page - 1})">
          <i class="bi bi-chevron-left"></i>
        </a>
      </li>
    `;
  }

  // Page numbers
  for (let i = 1; i <= pagination.total_pages; i++) {
    if (i === pagination.current_page) {
      html += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
    } else {
      html += `<li class="page-item"><a class="page-link" href="#" onclick="loadTransaksiList(${i})">${i}</a></li>`;
    }
  }

  // Next button
  if (pagination.current_page < pagination.total_pages) {
    html += `
      <li class="page-item">
        <a class="page-link" href="#" onclick="loadTransaksiList(${pagination.current_page + 1})">
          <i class="bi bi-chevron-right"></i>
        </a>
      </li>
    `;
  }

  paginationEl.innerHTML = html;
}

// Filter transactions by date
function filterTransaksi() {
  loadTransaksiList(1);
}

// Refresh transaction list
function refreshTransaksi() {
  document.getElementById('filterTanggal').value = '';
  loadTransaksiList(1);
}

// View transaction detail
function viewTransactionDetail(transactionId) {
  // TODO: Implement transaction detail modal
  Swal.fire('Info', 'Fitur detail transaksi akan segera tersedia', 'info');
}

// Reprint receipt
function reprintReceipt(transactionId) {
  // TODO: Implement reprint functionality
  Swal.fire('Info', 'Fitur cetak ulang akan segera tersedia', 'info');
}

// Delete transaction
function deleteTransaction(transactionId) {
  Swal.fire({
    title: 'Hapus Transaksi?',
    text: 'Transaksi yang dihapus tidak dapat dikembalikan!',
    icon: 'warning',
    showCancelButton: true,
    confirmButtonColor: '#d33',
    cancelButtonColor: '#3085d6',
    confirmButtonText: 'Ya, Hapus!',
    cancelButtonText: 'Batal'
  }).then((result) => {
    if (result.isConfirmed) {
      // TODO: Implement delete functionality
      Swal.fire('Info', 'Fitur hapus transaksi akan segera tersedia', 'info');
    }
  });
}

// Finish checkout process
function finishCheckout() {
  cart = [];
  updateCartDisplay();
  clearCartStorage(); // Clear localStorage
  refreshUnpaidSessions();
}

// Clear cart only (without refresh)
function clearCartOnly() {
  cart = [];
  updateCartDisplay();
  clearCartStorage(); // Clear localStorage
}
</script>

<?= $this->endSection() ?>