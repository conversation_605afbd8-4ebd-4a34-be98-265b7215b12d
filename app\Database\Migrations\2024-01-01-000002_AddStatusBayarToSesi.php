<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddStatusBayarToSesi extends Migration
{
    public function up()
    {
        // Tambahkan kolom status_bayar untuk memudahkan deteksi sesi belum dibayar
        $fields = [
            'status_bayar' => [
                'type' => 'ENUM',
                'constraint' => ['belum', 'bayar'],
                'default' => 'belum',
                'null' => false,
                'comment' => 'Status pembayaran: belum, bayar'
            ]
        ];
        
        $this->forge->addColumn('sesi', $fields);
        
        // Update existing data - set status_bayar berdasarkan metode_bayar
        $this->db->query("UPDATE sesi SET status_bayar = 'bayar' WHERE metode_bayar IS NOT NULL AND metode_bayar != ''");
        $this->db->query("UPDATE sesi SET status_bayar = 'belum' WHERE metode_bayar IS NULL OR metode_bayar = ''");
    }

    public function down()
    {
        // Hapus kolom status_bayar dari tabel sesi
        $this->forge->dropColumn('sesi', 'status_bayar');
    }
}
