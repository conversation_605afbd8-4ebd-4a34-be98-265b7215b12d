<?php
namespace App\Controllers;
use App\Models\KonsolModel;
use App\Models\StationModel;

class Konsol extends BaseController
{
    public function index()
    {
        $konsolModel = new KonsolModel();
        $stationModel = new StationModel();

        $data['konsol'] = $konsolModel->findAll();
        $data['station'] = $stationModel->getWithKonsol();

        return view('konsol/index', $data);
    }

    public function simpanKonsol()
    {
        $model = new KonsolModel();
        $model->save([
            'nama_konsol' => $this->request->getPost('nama_konsol'),
            'harga_personal' => $this->request->getPost('harga_personal'),
            'harga_member' => $this->request->getPost('harga_member'),
        ]);
        return redirect()->to('/konsol')->with('pesan', 'Konsol ditambahkan!');
    }

    public function simpanStation()
    {
        $model = new StationModel();
        $model->save([
            'nama_station' => $this->request->getPost('nama_station'),
            'id_konsol' => $this->request->getPost('id_konsol'),
            'ip_address' => $this->request->getPost('ip_address'),
        ]);
        return redirect()->to('/konsol')->with('pesan', 'Station ditambahkan!');
    }

    public function editKonsol($id)
{
    $model = new KonsolModel();
    $data['konsol'] = $model->find($id);
    return view('konsol/edit_konsol', $data);
}

public function updateKonsol($id)
{
    $model = new KonsolModel();
    $model->update($id, [
        'nama_konsol' => $this->request->getPost('nama_konsol'),
        'harga_personal' => $this->request->getPost('harga_personal'),
        'harga_member' => $this->request->getPost('harga_member'),
    ]);
    return redirect()->to('/konsol')->with('pesan', 'Data Konsol berhasil diupdate!');
}

public function editStation($id)
{
    $model = new StationModel();
    $konsol = new KonsolModel();
    $data['station'] = $model->find($id);
    $data['konsol'] = $konsol->findAll();
    return view('konsol/edit_station', $data);
}

public function updateStation($id)
{
    $model = new StationModel();
    $model->update($id, [
        'nama_station' => $this->request->getPost('nama_station'),
        'id_konsol' => $this->request->getPost('id_konsol'),
        'ip_address' => $this->request->getPost('ip_address'),
    ]);
    return redirect()->to('/konsol')->with('pesan', 'Data Station berhasil diupdate!');
}

public function hapusKonsol($id)
{
    $model = new \App\Models\KonsolModel();
    $model->delete($id);
    return redirect()->to('/konsol')->with('pesan', 'Konsol berhasil dihapus!');
}

public function hapusStation($id)
{
    $model = new \App\Models\StationModel();
    $model->delete($id);
    return redirect()->to('/konsol')->with('pesan', 'Station berhasil dihapus!');
}

}
