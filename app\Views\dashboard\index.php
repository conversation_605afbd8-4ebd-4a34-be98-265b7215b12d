<?php
/**
 * Dashboard Index View
 * Halaman utama untuk monitoring station gaming
 * Menampilkan status real-time semua station dan kontrol sesi
 *
 * <AUTHOR>
 * @version 3.0
 */
?>
<?= $this->extend('layouts/main') ?>
<?= $this->section('content') ?>

<!-- CSS dan JS Dependencies untuk SweetAlert -->
<link href="<?= base_url('assets/sweetalert2/sweetalert2.min.css') ?>" rel="stylesheet">
<script src="<?= base_url('assets/sweetalert2/sweetalert2.all.min.js') ?>"></script>
<link rel="stylesheet" href="<?= base_url('assets/css/bootstrap.min.css') ?>">
<link rel="stylesheet" href="<?= base_url('assets/css/bootstrap-icons.min.css') ?>">
<link rel="stylesheet" href="<?= base_url('assets/css/dashboard.css') ?>">

<style>
  /* Add Package Modal Styling */
  .package-card {
    transition: all 0.3s ease;
    border-width: 2px !important;
  }

  .package-card:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    transform: translateY(-2px);
  }

  .bg-gradient-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
  }

  .modal-header.bg-gradient-primary {
    border-bottom: none;
  }

  .card-header {
    font-size: 0.95rem;
    padding: 0.75rem 1rem;
  }

  .card-body {
    padding: 1rem;
  }

  .card-footer {
    padding: 0.5rem 1rem;
    border-top: 1px solid rgba(0,0,0,0.125);
  }

  .package-card .card-body .row .col-6 {
    padding: 0.5rem;
  }

  .package-card .card-body h6 {
    margin-bottom: 0;
    font-weight: bold;
  }

  /* Minimalis package card styling */
  .package-card-mini {
    transition: all 0.2s ease;
    border-width: 1px !important;
    border-radius: 6px;
    min-height: auto;
    font-size: 0.7rem;
  }

  .package-card-mini:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  }

  .package-card-mini .card-body {
    padding: 0.5rem !important;
  }

  .package-card-mini .btn {
    font-size: 0.65rem;
    padding: 0.1rem 0.25rem;
    margin-top: 0.25rem;
  }

  /* Compact modal styling */
  #addPackageModal .modal-header {
    padding: 0.5rem 1rem;
  }

  #addPackageModal .modal-body {
    padding: 0.75rem;
  }

  #addPackageModal .modal-footer {
    padding: 0.5rem;
  }

  #addPackageModal .modal-title {
    font-size: 0.9rem;
  }

  #addPackageModal .btn-close {
    font-size: 0.7rem;
    padding: 0.5rem;
  }

  /* Move Station Modal Styling */
  #moveStationModal .modal-header {
    padding: 0.5rem 1rem;
  }

  #moveStationModal .modal-body {
    padding: 0.75rem;
  }

  #moveStationModal .modal-footer {
    padding: 0.5rem;
  }

  #moveStationModal .modal-title {
    font-size: 0.9rem;
  }

  #moveStationModal .btn-close {
    font-size: 0.7rem;
    padding: 0.5rem;
  }

  /* Station card styling untuk move modal */
  .station-card-mini {
    transition: all 0.2s ease;
    border-width: 1px !important;
    border-radius: 6px;
    min-height: auto;
    font-size: 0.8rem;
    cursor: pointer;
  }

  .station-card-mini:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-color: #007bff !important;
  }

  .station-card-mini .card-body {
    padding: 0.75rem !important;
  }

  .station-card-mini .btn {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
    margin-top: 0.25rem;
  }

  .station-card-mini.selected {
    border-color: #007bff !important;
    background-color: rgba(0, 123, 255, 0.1);
  }
</style>

<div class="container-fluid">
  <h4 class="mb-4">Monitor Station</h4>
  <div class="row gx-3 gy-4">
    <?php foreach ($station as $s): ?>
      <div class="col-xxl-1-5 col-xl-2 col-lg-3 col-md-4 col-sm-6 col-6">
        <div class="card-konsol <?= $s['status'] === 'berjalan' ? 'active' : '' ?>" data-status="<?= $s['status'] ?>" data-harga-personal="<?= $s['harga_personal'] ?>" data-harga-member="<?= $s['harga_member'] ?>" data-station-id="<?= $s['id'] ?>" data-konsol-id="<?= $s['id_konsol'] ?>">
          <i class="bi bi-wifi signal-icon"></i>
          <div class="fw-bold mb-2 text-white text-start w-100"> <?= esc($s['nama_station']) ?> </div>
          <div class="card-title mt-2"> <?= esc($s['nama_konsol']) ?> </div>
          <div class="timer" id="timer-<?= $s['id'] ?>" data-seconds="<?= $s['status'] === 'berjalan' && isset($s['durasi_sisa']) ? $s['durasi_sisa'] : 0 ?>">
            <?= $s['status'] === 'berjalan' && $s['jenis_user'] === 'member' && isset($s['durasi_sisa'])
              ? gmdate("H:i:s", $s['durasi_sisa'])
              : '00:00:00' ?>
          </div>
          <div class="paket-info" id="harga-<?= $s['id'] ?>">
            <?php if ($s['status'] === 'berjalan'): ?>
              <?php if ($s['jenis_user'] === 'member'): ?>
                <?php if (isset($s['nama_paket'])): ?>
                  <?= esc($s['nama_paket']) ?>
                <?php else: ?>
                  Rp <?= number_format($s['member_saldo'] ?? 0, 0, ',', '.') ?>
                <?php endif; ?>
              <?php else: ?>
                Rp <?= number_format($s['harga_total'] ?? 0, 0, ',', '.') ?>
              <?php endif; ?>
            <?php else: ?>
              PlaySphere
            <?php endif; ?>
          </div>
          <div class="user-info">
            <?php if (isset($s['jenis_user']) && $s['jenis_user'] === 'member'): ?>
              <span class="user-member"><i class="bi bi-person-fill"></i> <?= esc($s['nama_member'] ?? 'Member') ?></span>
            <?php else: ?>
              <?= $s['status'] === 'berjalan' ? 'Personal' : 'Ready' ?>
            <?php endif; ?>
          </div>
          <div class="btn-icon-group">
            <?php if ($s['status'] !== 'berjalan'): ?>
              <button class="btn-icon btn-play" data-id="<?= $s['id'] ?>" title="Mulai">
                <i class="bi bi-play-fill"></i>
              </button>
            <?php else: ?>
              <button class="btn-icon btn-stop" data-id="<?= $s['id'] ?>" title="Berhenti">
                <i class="bi bi-stop-fill"></i>
              </button>
              <?php if ($s['jenis_user'] === 'member' && !empty($s['id_paket'])): ?>
                <button class="btn-icon btn-plus" data-id="<?= $s['id'] ?>" title="Tambah Paket">
                  <i class="bi bi-plus-circle"></i>
                </button>
              <?php endif; ?>
              <button class="btn-icon btn-move" data-id="<?= $s['id'] ?>" title="Pindah Station">
                <i class="bi bi-arrow-left-right"></i>
              </button>
            <?php endif; ?>
          </div>
        </div>
      </div>
    <?php endforeach; ?>
  </div>
</div>

<!-- Modal Tambah Paket -->
<div class="modal fade" id="addPackageModal" tabindex="-1" aria-labelledby="addPackageModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-sm" style="max-width: 500px;">
    <div class="modal-content">
      <div class="modal-header bg-gradient-primary text-white py-2">
        <h5 class="modal-title" id="addPackageModalLabel" style="font-size: 0.9rem;">
          <i class="bi bi-plus-circle-fill me-1"></i>Tambah Paket
        </h5>
        <button type="button" class="btn-close btn-close-white btn-sm" data-bs-dismiss="modal" aria-label="Close" style="font-size: 0.6rem;"></button>
      </div>
      <div class="modal-body">
        <div class="row mb-2">
          <div class="col-6">
            <div class="card border-info">
              <div class="card-body text-center p-1">
                <i class="bi bi-person-circle text-info" style="font-size: 1rem;"></i>
                <p class="mb-0 mt-1" style="font-size: 0.7rem;">Member</p>
                <p class="card-text fw-bold text-info mb-0" style="font-size: 0.8rem;" id="addPackageMemberName">-</p>
              </div>
            </div>
          </div>
          <div class="col-6">
            <div class="card border-success">
              <div class="card-body text-center p-1">
                <i class="bi bi-wallet2 text-success" style="font-size: 1rem;"></i>
                <p class="mb-0 mt-1" style="font-size: 0.7rem;">Saldo</p>
                <p class="card-text fw-bold text-success mb-0" style="font-size: 0.8rem;" id="addPackageMemberSaldo">-</p>
              </div>
            </div>
          </div>
        </div>

        <div class="alert alert-info py-1 px-2 mb-2">
          <small style="font-size: 0.7rem;">
            <i class="bi bi-info-circle me-1"></i>
            Pilih paket untuk tambah durasi. Saldo akan dipotong sesuai harga.
          </small>
        </div>

        <div class="row" id="addPackageList">
          <!-- Package cards will be loaded here -->
        </div>
      </div>
      <div class="modal-footer p-1">
        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal" style="font-size: 0.7rem; padding: 0.25rem 0.5rem;">
          <i class="bi bi-x-circle me-1"></i>Tutup
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Modal Pindah Station -->
<div class="modal fade" id="moveStationModal" tabindex="-1" aria-labelledby="moveStationModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-sm" style="max-width: 500px;">
    <div class="modal-content">
      <div class="modal-header bg-gradient-primary text-white py-2">
        <h5 class="modal-title" id="moveStationModalLabel" style="font-size: 0.9rem;">
          <i class="bi bi-arrow-left-right me-1"></i>Pindah Station
        </h5>
        <button type="button" class="btn-close btn-close-white btn-sm" data-bs-dismiss="modal" aria-label="Close" style="font-size: 0.6rem;"></button>
      </div>
      <div class="modal-body">
        <div class="row mb-2">
          <div class="col-6">
            <div class="card border-info">
              <div class="card-body text-center p-1">
                <i class="bi bi-display text-info" style="font-size: 1rem;"></i>
                <p class="mb-0 mt-1" style="font-size: 0.7rem;">Station Asal</p>
                <p class="card-text fw-bold text-info mb-0" style="font-size: 0.8rem;" id="moveFromStationName">-</p>
              </div>
            </div>
          </div>
          <div class="col-6">
            <div class="card border-success">
              <div class="card-body text-center p-1">
                <i class="bi bi-controller text-success" style="font-size: 1rem;"></i>
                <p class="mb-0 mt-1" style="font-size: 0.7rem;">Konsol</p>
                <p class="card-text fw-bold text-success mb-0" style="font-size: 0.8rem;" id="moveKonsolName">-</p>
              </div>
            </div>
          </div>
        </div>

        <div class="alert alert-info py-1 px-2 mb-2">
          <small style="font-size: 0.7rem;">
            <i class="bi bi-info-circle me-1"></i>
            Pilih station tujuan dengan konsol yang sama. Sesi akan dipindahkan tanpa menghentikan timer.
          </small>
        </div>

        <div class="row" id="availableStationsList">
          <!-- Available stations will be loaded here -->
        </div>

        <div id="noStationsMessage" style="display: none;">
          <div class="alert alert-warning py-2 px-2 mb-0">
            <small style="font-size: 0.7rem;">
              <i class="bi bi-exclamation-triangle me-1"></i>
              Tidak ada station lain yang tersedia dengan konsol yang sama.
            </small>
          </div>
        </div>
      </div>
      <div class="modal-footer p-1">
        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal" style="font-size: 0.7rem; padding: 0.25rem 0.5rem;">
          <i class="bi bi-x-circle me-1"></i>Batal
        </button>
        <button type="button" class="btn btn-primary btn-sm" id="confirmMoveBtn" onclick="confirmMoveStation()" disabled style="font-size: 0.7rem; padding: 0.25rem 0.5rem;">
          <i class="bi bi-arrow-right me-1"></i>Pindah
        </button>
      </div>
    </div>
  </div>
</div>

<script>
let currentStationId = null;
let selectedMember = null;
let currentTimers = {};

// Event listener untuk semua tombol menggunakan event delegation
document.addEventListener('DOMContentLoaded', function() {
  console.log('Dashboard loaded, setting up event listeners');

  // Event delegation untuk semua tombol
  document.addEventListener('click', function(e) {
    console.log('Click detected on:', e.target);

    // Tombol Play
    if (e.target.closest('.btn-play')) {
      console.log('Play button clicked');
      const stationId = e.target.closest('.btn-play').getAttribute('data-id');
      console.log('Station ID:', stationId);
      currentStationId = stationId;
      showStartSessionModal();
      return;
    }

    // Tombol Stop
    if (e.target.closest('.btn-stop')) {
      const stationId = e.target.closest('.btn-stop').getAttribute('data-id');
      stopSession(stationId);
      return;
    }

    // Tombol Move
    if (e.target.closest('.btn-move')) {
      const stationId = e.target.closest('.btn-move').getAttribute('data-id');
      showMoveStationModal(stationId);
      return;
    }

    // Tombol Add Package
    if (e.target.closest('.btn-plus')) {
      const stationId = e.target.closest('.btn-plus').getAttribute('data-id');
      showAddPackageModal(stationId);
      return;
    }
  });

  // Load semua sesi aktif saat halaman dimuat
  loadAllActiveSessions();

  // Initialize existing timers
  initializeTimers();

  // Clean up invalid localStorage entries
  cleanupInvalidSessionStorage();

  // For testing: uncomment to clear all session storage
  // clearAllSessionStorage();

  // Debug: Show all session start times in localStorage
  console.log('Current localStorage session times:');
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && key.startsWith('session_start_')) {
      const value = localStorage.getItem(key);
      const stationId = key.replace('session_start_', '');
      const timestamp = parseInt(value);
      if (!isNaN(timestamp)) {
        console.log(`Station ${stationId}: ${new Date(timestamp)}`);
      }
    }
  }

  console.log('Event listeners setup complete');
});

// Fungsi untuk menampilkan modal mulai sesi
function showStartSessionModal() {
  console.log('showStartSessionModal called for station:', currentStationId);

  // Ambil data harga personal dari station card
  const stationCard = document.querySelector(`[data-station-id="${currentStationId}"]`);
  if (!stationCard) {
    console.error('Station card not found for ID:', currentStationId);
    return;
  }

  // Cek apakah station sedang aktif
  if (stationCard.classList.contains('active')) {
    Swal.fire('Error', 'Station ini sedang digunakan. Tidak dapat memulai sesi baru.', 'error');
    return;
  }

  const hargaPersonal = stationCard.getAttribute('data-harga-personal') || '8000';
  console.log('Station data:', { hargaPersonal });

  const modalHtml = `
    <div class="modal fade" id="startSessionModal" tabindex="-1" aria-labelledby="startSessionModalLabel" aria-hidden="true">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="startSessionModalLabel">Mulai Sesi</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div class="row mb-3">
              <div class="col-6">
                <button type="button" class="btn btn-primary w-100" id="btnPersonal" onclick="toggleUserType('personal')">Personal</button>
              </div>
              <div class="col-6">
                <button type="button" class="btn btn-outline-primary w-100" id="btnMember" onclick="toggleUserType('member')">Member</button>
              </div>
            </div>

            <div id="personalSection">
              <div class="alert alert-info">
                <strong>Harga Personal:</strong> <span id="hargaPersonal">Rp ${parseInt(hargaPersonal).toLocaleString('id-ID')}</span>
              </div>
            </div>

            <div id="memberSection" style="display: none;">
              <div class="mb-3">
                <label for="searchMember" class="form-label">Cari Member</label>
                <input type="text" class="form-control" id="searchMember" placeholder="Ketik nama member..." oninput="searchMemberRealtime(this.value)">
                <div id="memberResults" class="list-group mt-2" style="max-height: 200px; overflow-y: auto;"></div>
              </div>

              <div id="selectedMemberInfo" style="display: none;">
                <div class="alert alert-success">
                  <strong>Member Dipilih:</strong> <span id="selectedMemberName"></span><br>
                  <strong>Saldo:</strong> <span id="selectedMemberSaldo"></span>
                </div>
              </div>

              <div id="paketSelection" style="display: none;">
                <div class="mb-3">
                  <label for="selectPaket" class="form-label">Pilih Paket</label>
                  <select class="form-select" id="selectPaket">
                    <option value="">Pilih Paket...</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
            <button type="button" class="btn btn-primary" onclick="startSession()">Mulai Sesi</button>
          </div>
        </div>
      </div>
    </div>
  `;

  // Remove existing modal if any
  const existingModal = document.getElementById('startSessionModal');
  if (existingModal) {
    existingModal.remove();
  }

  // Add modal to body
  document.body.insertAdjacentHTML('beforeend', modalHtml);

  // Load paket untuk konsol ini saat modal dibuka
  const konsolId = stationCard.getAttribute('data-konsol-id');
  if (konsolId) {
    loadPaketByKonsol(konsolId);
  }

  // Show modal
  const modal = new bootstrap.Modal(document.getElementById('startSessionModal'));
  modal.show();
}

// Fungsi untuk toggle antara Personal dan Member
function toggleUserType(type) {
  const btnPersonal = document.getElementById('btnPersonal');
  const btnMember = document.getElementById('btnMember');
  const personalSection = document.getElementById('personalSection');
  const memberSection = document.getElementById('memberSection');

  if (type === 'personal') {
    btnPersonal.classList.add('btn-primary');
    btnPersonal.classList.remove('btn-outline-primary');
    btnMember.classList.add('btn-outline-primary');
    btnMember.classList.remove('btn-primary');
    personalSection.style.display = 'block';
    memberSection.style.display = 'none';
  } else {
    btnMember.classList.add('btn-primary');
    btnMember.classList.remove('btn-outline-primary');
    btnPersonal.classList.add('btn-outline-primary');
    btnPersonal.classList.remove('btn-primary');
    personalSection.style.display = 'none';
    memberSection.style.display = 'block';
  }
}

function searchMemberRealtime(query) {
  console.log('Searching for member:', query);

  if (!query || query.trim().length < 2) {
    document.getElementById('memberResults').innerHTML = '';
    return;
  }

  fetch(`<?= base_url('dashboard/searchMember') ?>?q=${encodeURIComponent(query)}`, {
    method: 'GET',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => {
    console.log('Search response status:', response.status);
    return response.json();
  })
  .then(data => {
    console.log('Search response data:', data);
    const resultsContainer = document.getElementById('memberResults');
    resultsContainer.innerHTML = '';

    if (data.success && data.members && data.members.length > 0) {
      data.members.forEach((member, index) => {
        console.log(`Processing member ${index + 1}:`, member);
        console.log(`Member properties:`, Object.keys(member));

        const memberDiv = document.createElement('div');
        memberDiv.className = 'list-group-item list-group-item-action';
        memberDiv.style.cursor = 'pointer';

        // Try different property names for member name
        const memberName = member.nama_member || member.name || member.nama || member.member_name || member.username || member.full_name || 'Member';
        const memberSaldo = member.saldo || member.balance || member.amount || 0;

        console.log(`Member ${index + 1} - Name: "${memberName}", Saldo: ${memberSaldo}`);

        memberDiv.innerHTML = `
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <strong>${memberName}</strong><br>
              <small class="text-muted">Saldo: Rp ${parseInt(memberSaldo).toLocaleString('id-ID')}</small>
            </div>
            <i class="bi bi-chevron-right"></i>
          </div>
        `;

        memberDiv.addEventListener('click', function() {
          selectMember(member);
        });

        resultsContainer.appendChild(memberDiv);
      });
    } else {
      resultsContainer.innerHTML = '<div class="list-group-item text-muted">Tidak ada member ditemukan</div>';
    }
  })
  .catch(error => {
    console.error('Error searching members:', error);
    document.getElementById('memberResults').innerHTML = '<div class="list-group-item text-danger">Error mencari member</div>';
  });
}

function selectMember(member) {
  console.log('Selecting member:', member);
  selectedMember = member;

  // Try different property names for member name
  const memberName = member.nama_member || member.name || member.nama || member.member_name || member.username || 'Member';
  const memberSaldo = member.saldo || member.balance || 0;

  console.log('Selected member object:', member);
  console.log('Selected member name:', memberName);
  console.log('Selected member saldo:', memberSaldo);
  console.log('Selected member ID:', member.id || member.member_id || member.id_member || member.user_id);

  // Update tampilan member yang dipilih
  document.getElementById('selectedMemberName').textContent = memberName;
  document.getElementById('selectedMemberSaldo').textContent = 'Rp ' + parseInt(memberSaldo).toLocaleString('id-ID');

  // Show selected member info dan paket selection
  document.getElementById('selectedMemberInfo').style.display = 'block';
  document.getElementById('paketSelection').style.display = 'block';

  // Hide search results
  document.getElementById('memberResults').innerHTML = '';
  document.getElementById('searchMember').value = memberName;

  // Load paket untuk konsol ini
  const stationCard = document.querySelector(`[data-station-id="${currentStationId}"]`);
  const konsolId = stationCard.getAttribute('data-konsol-id');
  loadPaketByKonsol(konsolId);
}

function loadPaketByKonsol(konsolId) {
  console.log('Loading paket for konsol:', konsolId);
  const selectPaket = document.getElementById('selectPaket');
  selectPaket.innerHTML = '<option value="">Memuat paket...</option>';

  fetch(`<?= base_url('dashboard/getPaketByKonsol') ?>/${konsolId}`, {
    method: 'GET',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => {
    console.log('Paket response status:', response.status);
    return response.json();
  })
  .then(data => {
    console.log('Paket response data:', data);
    selectPaket.innerHTML = '<option value="">Pilih Paket...</option>';

    if (data.success && data.pakets && data.pakets.length > 0) {
      data.pakets.forEach(paket => {
        const option = document.createElement('option');
        option.value = paket.id;

        // Format duration for display
        let durasiDisplay = '';
        if (paket.durasi && paket.durasi.includes(':')) {
          const [hours, minutes] = paket.durasi.split(':');
          durasiDisplay = `${hours} Jam ${minutes} menit`;
        } else {
          const minutes = parseInt(paket.durasi || 0);
          const hours = Math.floor(minutes / 60);
          const remainingMinutes = minutes % 60;
          durasiDisplay = hours > 0 ?
            `${hours} Jam ${remainingMinutes > 0 ? remainingMinutes + ' menit' : ''}` :
            `${minutes} menit`;
        }

        const paketName = paket.nama_paket || 'Paket';
        const paketHarga = paket.harga || 0;

        option.textContent = `${paketName} | ${durasiDisplay} | Rp ${parseInt(paketHarga).toLocaleString('id-ID')}`;
        selectPaket.appendChild(option);
      });
    }

    // Tambahkan opsi Non Paket
    const nonPaketOption = document.createElement('option');
    nonPaketOption.value = 'non_paket';
    nonPaketOption.textContent = 'Non Paket';
    selectPaket.appendChild(nonPaketOption);
  })
  .catch(error => {
    console.error('Error loading pakets:', error);
    selectPaket.innerHTML = '<option value="">Error memuat paket</option>';
  });
}

// Fungsi untuk memulai sesi
function startSession() {
  const btnPersonal = document.getElementById('btnPersonal');
  const isPersonal = btnPersonal.classList.contains('btn-primary');

  if (isPersonal) {
    startPersonalSession();
  } else {
    startMemberSession();
  }
}

// Fungsi untuk memulai sesi personal
function startPersonalSession() {
  const stationCard = document.querySelector(`[data-station-id="${currentStationId}"]`);
  const hargaPersonal = stationCard.getAttribute('data-harga-personal');

  const formData = new FormData();
  formData.append('station_id', currentStationId);
  formData.append('jenis_user', 'personal');
  formData.append('harga_personal', hargaPersonal);

  fetch('<?= base_url('dashboard/mulai') ?>', {
    method: 'POST',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    },
    body: formData
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      // Close modal
      const modal = bootstrap.Modal.getInstance(document.getElementById('startSessionModal'));
      modal.hide();

      // Simpan waktu mulai sesi di localStorage (waktu saat ini)
      const sessionStartTime = Date.now();
      localStorage.setItem(`session_start_${currentStationId}`, sessionStartTime.toString());
      console.log(`Saved session start time for station ${currentStationId}: ${new Date(sessionStartTime)}`);

      Swal.fire('Berhasil', 'Sesi Personal dimulai', 'success');
      // Reload active sessions to update display
      setTimeout(() => {
        loadAllActiveSessions();
      }, 1000);
    } else {
      Swal.fire('Error', data.message || 'Gagal memulai sesi', 'error');
    }
  })
  .catch(error => {
    console.error('Error starting personal session:', error);
    Swal.fire('Error', 'Terjadi kesalahan saat memulai sesi', 'error');
  });
}

// Fungsi untuk memulai sesi member
function startMemberSession() {
  if (!selectedMember) {
    Swal.fire('Error', 'Pilih member terlebih dahulu', 'error');
    return;
  }

  const selectPaket = document.getElementById('selectPaket');
  const selectedPaketId = selectPaket.value;

  console.log('Selected paket ID:', selectedPaketId);
  console.log('Selected paket text:', selectPaket.options[selectPaket.selectedIndex].text);

  if (!selectedPaketId) {
    Swal.fire('Error', 'Pilih paket terlebih dahulu', 'error');
    return;
  }

  // Validasi station yang dipilih tidak sedang digunakan
  const stationCard = document.querySelector(`[data-station-id="${currentStationId}"]`);
  if (stationCard && stationCard.classList.contains('active')) {
    Swal.fire('Error', 'Station ini sedang digunakan. Pilih station lain.', 'error');
    return;
  }

  // Validasi client-side: cek apakah member benar-benar sedang digunakan
  const memberName = selectedMember.nama_member || selectedMember.name || selectedMember.nama || 'Member';
  const memberId = selectedMember.id || selectedMember.member_id || selectedMember.id_member || selectedMember.user_id;

  console.log('=== CLIENT-SIDE MEMBER VALIDATION ===');
  console.log('Checking member:', memberName, 'ID:', memberId);

  // Cek apakah ada station yang sedang menggunakan member ini
  let memberInUse = false;
  let usedInStation = null;

  document.querySelectorAll('.card-konsol.active').forEach(activeCard => {
    const userInfo = activeCard.querySelector('.user-info');
    if (userInfo) {
      const userText = userInfo.textContent || '';
      console.log('Active station user:', userText);

      // Cek apakah ini member session dan nama member sama
      if (userText.includes(memberName) && userText.includes('person-fill')) {
        memberInUse = true;
        usedInStation = activeCard.getAttribute('data-station-id');
        console.log(`Member ${memberName} found in use at station ${usedInStation}`);
      }
    }
  });

  if (memberInUse) {
    Swal.fire({
      title: 'Member Sedang Digunakan',
      text: `Member "${memberName}" sedang bermain di Station ${usedInStation}. Pilih member lain.`,
      icon: 'warning',
      confirmButtonText: 'OK'
    });
    return;
  }

  console.log('Client-side validation passed, proceeding with member session');

  // Lanjutkan dengan memulai sesi
  proceedWithMemberSession();
}

// Fungsi terpisah untuk melanjutkan proses mulai sesi member
function proceedWithMemberSession() {
  try {
    const selectPaket = document.getElementById('selectPaket');
    if (!selectPaket) {
      Swal.fire('Error', 'Element paket tidak ditemukan', 'error');
      return;
    }

    const selectedPaketId = selectPaket.value;

    if (!selectedPaketId) {
      Swal.fire('Error', 'Pilih paket terlebih dahulu', 'error');
      return;
    }

    if (!selectedMember) {
      Swal.fire('Error', 'Data member tidak tersedia', 'error');
      return;
    }

  console.log('Starting member session with data:', {
    station_id: currentStationId,
    member: selectedMember,
    paket_id: selectedPaketId,
    id_paket: selectedPaketId === 'non_paket' ? '' : selectedPaketId
  });

  const formData = new FormData();
  formData.append('station_id', currentStationId);
  formData.append('jenis_user', 'member');

  // Try different property names for member ID
  const memberId = selectedMember.id || selectedMember.member_id || selectedMember.id_member || selectedMember.user_id;
  if (!memberId) {
    console.error('Member ID not found in object:', selectedMember);
    console.error('Available properties:', Object.keys(selectedMember));
    Swal.fire('Error', 'ID Member tidak ditemukan', 'error');
    return;
  }

  console.log('Using member ID:', memberId);

  // Tambahkan data member lengkap untuk menghindari error saldo
  const memberName = selectedMember.nama_member || selectedMember.name || selectedMember.nama || selectedMember.member_name || selectedMember.username || 'Member';
  const memberSaldo = selectedMember.saldo || selectedMember.balance || 0;

  // Validasi data sebelum kirim
  if (!memberId || !memberName || !currentStationId) {
    Swal.fire('Error', 'Data tidak lengkap. Silakan coba lagi.', 'error');
    return;
  }

  formData.append('member_id', memberId);

  // Data member yang diperlukan server
  formData.append('member_name', memberName);
  formData.append('member_saldo', memberSaldo);
  formData.append('nama_member', memberName); // Fallback
  formData.append('saldo', memberSaldo); // Fallback

  // Tambahkan semua data member yang tersedia dari response
  Object.keys(selectedMember).forEach(key => {
    if (selectedMember[key] !== null && selectedMember[key] !== undefined) {
      formData.append(key, selectedMember[key]);
    }
  });

  // Tambahkan data konsol dan station
  const stationCard = document.querySelector(`[data-station-id="${currentStationId}"]`);
  const konsolId = stationCard.getAttribute('data-konsol-id');
  const hargaPersonal = stationCard.getAttribute('data-harga-personal');
  const hargaMember = stationCard.getAttribute('data-harga-member');

  if (konsolId) formData.append('konsol_id', konsolId);
  if (hargaPersonal) formData.append('harga_personal', hargaPersonal);
  if (hargaMember) formData.append('harga_member', hargaMember);

  // Tambahkan parameter tambahan yang mungkin diperlukan server
  formData.append('id_konsol', konsolId); // Alternative konsol ID field
  formData.append('id_station', currentStationId); // Alternative station ID field
  formData.append('nama', memberName); // Alternative member name field
  formData.append('member_nama', memberName); // Another alternative

  // Debug: pastikan member ID dalam format yang benar
  console.log('Member ID type:', typeof memberId, 'Value:', memberId);
  if (selectedMember.id) {
    formData.append('id', selectedMember.id); // Raw ID field
  }

  // Tambahkan parameter untuk membantu server validasi
  formData.append('check_member_conflict', '1'); // Flag untuk cek konflik member
  formData.append('ignore_personal_sessions', '1'); // Ignore personal sessions saat cek konflik
  formData.append('allow_member_with_personal', '1'); // Allow member session meskipun ada personal session
  formData.append('session_type_validation', 'member_only'); // Hanya cek konflik dengan member session lain

  if (selectedPaketId === 'non_paket') {
    formData.append('id_paket', '');
    formData.append('paket_id', '');
    formData.append('is_non_paket', '1');
  } else {
    formData.append('id_paket', selectedPaketId);
    formData.append('paket_id', selectedPaketId);
  }

  // Debug: tampilkan semua data yang akan dikirim
  console.log('=== MEMBER SESSION START DEBUG ===');
  console.log('Station ID:', currentStationId);
  console.log('Member ID:', memberId);
  console.log('Member Object:', selectedMember);
  console.log('Member Object Keys:', Object.keys(selectedMember));
  console.log('Member Name:', memberName);
  console.log('Member Saldo:', memberSaldo);
  console.log('Paket ID:', selectedPaketId);
  const requestData = {
    station_id: currentStationId,
    jenis_user: 'member',
    member_id: memberId,
    member_name: memberName,
    member_saldo: memberSaldo,
    nama_member: selectedMember.nama_member || memberName,
    saldo: memberSaldo,
    id_paket: selectedPaketId === 'non_paket' ? '' : selectedPaketId,
    paket_id: selectedPaketId === 'non_paket' ? '' : selectedPaketId,
    is_non_paket: selectedPaketId === 'non_paket' ? '1' : undefined,
    check_member_conflict: '1',
    ignore_personal_sessions: '1',
    allow_member_with_personal: '1',
    session_type_validation: 'member_only'
  };

  console.log('Request data being sent:', requestData);

  // Debug: tampilkan semua FormData entries
  console.log('=== FORM DATA ENTRIES ===');
  for (let [key, value] of formData.entries()) {
    console.log(`${key}: ${value}`);
  }

  // Debug: cek status semua station saat ini
  console.log('=== CURRENT STATION STATUS ===');
  document.querySelectorAll('.card-konsol').forEach(card => {
    const stationId = card.getAttribute('data-station-id');
    const status = card.getAttribute('data-status');
    const isActive = card.classList.contains('active');
    const userInfo = card.querySelector('.user-info')?.textContent || 'Unknown';
    console.log(`Station ${stationId}: status=${status}, active=${isActive}, user=${userInfo}`);
  });

  // Try main endpoint first, then fallback if needed
  const endpoint = '<?= base_url('dashboard/mulai') ?>';
  console.log('Sending request to:', endpoint);

  // Convert FormData to JSON with proper field mapping
  const jsonData = {};
  for (let [key, value] of formData.entries()) {
    jsonData[key] = value;
  }

  // Ensure critical fields are properly mapped using existing variables
  // Override with correct field names that server expects
  jsonData.member_id = selectedMember.id || selectedMember.member_id || selectedMember.id_member;
  jsonData.id_member = selectedMember.id || selectedMember.member_id || selectedMember.id_member;
  jsonData.nama_member = selectedMember.nama_member || selectedMember.nama || selectedMember.name;
  jsonData.nama = selectedMember.nama_member || selectedMember.nama || selectedMember.name;
  jsonData.saldo = selectedMember.saldo || 0; // This is the key field server expects
  jsonData.member_saldo = selectedMember.saldo || 0;

  console.log('JSON data being sent:', jsonData);
  console.log('Critical fields check:');
  console.log('- saldo:', jsonData.saldo);
  console.log('- member_id:', jsonData.member_id);
  console.log('- nama_member:', jsonData.nama_member);

  fetch(endpoint, {
    method: 'POST',
    headers: {
      'X-Requested-With': 'XMLHttpRequest',
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(jsonData)
  })
  .then(response => {
    console.log('=== SERVER RESPONSE (JSON METHOD) ===');
    console.log('Response status:', response.status);
    console.log('Response headers:', response.headers);
    console.log('Content-Type:', response.headers.get('content-type'));

    if (!response.ok) {
      console.error('HTTP Error:', response.status, response.statusText);
    }

    // Clone response untuk debug
    return response.clone().text().then(text => {
      console.log('Raw response text:', text);
      try {
        const parsed = JSON.parse(text);
        console.log('Parsed JSON response:', parsed);
        return parsed;
      } catch (e) {
        console.error('Failed to parse JSON:', e);
        console.error('Response text that failed to parse:', text);
        throw new Error('Invalid JSON response: ' + text.substring(0, 100));
      }
    });
  })
  .then(data => {
    console.log('Member session response data:', data);
    console.log('Response success:', data.success);
    console.log('Response message:', data.message);

    if (data.success) {
      // Close modal
      const modal = bootstrap.Modal.getInstance(document.getElementById('startSessionModal'));
      modal.hide();

      Swal.fire('Berhasil', 'Sesi Member dimulai', 'success');
      // Reload active sessions to update display
      setTimeout(() => {
        loadAllActiveSessions();
      }, 1000);
    } else {
      // Show detailed error message
      let errorMessage = data.message || 'Gagal memulai sesi member';

      // Try fallback with minimal FormData if JSON failed
      if (errorMessage.includes('Data tidak lengkap') || errorMessage.includes('Member tidak ditemukan')) {
        console.log('JSON request failed, trying FormData fallback...');
        tryFormDataFallback();
        return;
      }

      // Handle specific error cases
      if (errorMessage.includes('sedang digunakan')) {
        // Berikan opsi untuk retry dengan parameter berbeda
        Swal.fire({
          title: 'Server Validation Issue',
          html: `${errorMessage}<br><br>
                 <strong>Debug Info:</strong><br>
                 Member ID: ${selectedMember.id || selectedMember.member_id || 'Unknown'}<br>
                 Station: ${currentStationId}<br><br>
                 <em>This appears to be a server validation bug where personal sessions incorrectly block member sessions.</em>`,
          icon: 'warning',
          showCancelButton: true,
          confirmButtonText: 'Force Start Session',
          cancelButtonText: 'Cancel',
          confirmButtonColor: '#e74c3c'
        }).then((result) => {
          if (result.isConfirmed) {
            retryMemberSessionWithForce();
          }
        });
      } else if (errorMessage.includes('saldo') || errorMessage.includes('Undefined array key') || errorMessage.includes('Data tidak lengkap') || errorMessage.includes('Member tidak ditemukan')) {
        // Handle saldo/data issues
        Swal.fire({
          title: 'Data Issue Detected',
          html: `${errorMessage}<br><br>
                 <strong>Member Data Sent:</strong><br>
                 ID: ${selectedMember.id || 'Missing'}<br>
                 Name: ${selectedMember.nama_member || selectedMember.nama || selectedMember.name || 'Missing'}<br>
                 Saldo: ${selectedMember.saldo || selectedMember.balance || 'Missing'}<br>
                 Station: ${currentStationId}<br><br>
                 <em>Trying to fix data and retry...</em>`,
          icon: 'info',
          showCancelButton: true,
          confirmButtonText: 'Retry with Complete Data',
          cancelButtonText: 'Cancel',
          confirmButtonColor: '#3498db'
        }).then((result) => {
          if (result.isConfirmed) {
            retryMemberSessionWithForce();
          }
        });
      } else {
        Swal.fire({
          title: 'Error',
          text: errorMessage,
          icon: 'error',
          confirmButtonText: 'OK'
        });
      }
    }
  })
  .catch(error => {
    console.error('Error starting member session:', error);
    Swal.fire('Error', 'Terjadi kesalahan saat memulai sesi member', 'error');
  });

  } catch (error) {
    console.error('Error in proceedWithMemberSession:', error);
    Swal.fire('Error', 'Terjadi kesalahan saat memproses data member', 'error');
  }
}

// Fungsi fallback dengan FormData minimal
function tryFormDataFallback() {
  console.log('=== TRYING FORMDATA FALLBACK ===');

  const minimalFormData = new FormData();

  // Hanya kirim data minimal yang pasti diperlukan
  minimalFormData.append('station_id', currentStationId);
  minimalFormData.append('jenis_user', 'member');

  const memberId = selectedMember.id || selectedMember.member_id || selectedMember.id_member;
  const memberName = selectedMember.nama_member || selectedMember.nama || selectedMember.name;
  const memberSaldo = selectedMember.saldo || 0;

  // Coba berbagai kombinasi field name yang mungkin diharapkan server
  minimalFormData.append('member_id', memberId);
  minimalFormData.append('id_member', memberId); // Alternative
  minimalFormData.append('nama_member', memberName);
  minimalFormData.append('nama', memberName); // Alternative
  minimalFormData.append('saldo', memberSaldo); // Server expects this exact field name
  minimalFormData.append('member_saldo', memberSaldo); // Alternative

  const selectPaket = document.getElementById('selectPaket');
  const selectedPaketId = selectPaket.value;

  if (selectedPaketId === 'non_paket') {
    minimalFormData.append('id_paket', '');
    minimalFormData.append('paket_id', '');
    minimalFormData.append('is_non_paket', '1');
    console.log('Member non-paket session - no id_paket sent');
  } else {
    minimalFormData.append('id_paket', selectedPaketId);
    minimalFormData.append('paket_id', selectedPaketId);
    console.log(`Member paket session - id_paket: ${selectedPaketId}`);
  }

  console.log('=== MINIMAL FORM DATA ===');
  for (let [key, value] of minimalFormData.entries()) {
    console.log(`${key}: ${value}`);
  }

  fetch('<?= base_url('dashboard/mulai') ?>', {
    method: 'POST',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    },
    body: minimalFormData
  })
  .then(response => response.json())
  .then(data => {
    console.log('Fallback response:', data);

    if (data.success) {
      const modal = bootstrap.Modal.getInstance(document.getElementById('startSessionModal'));
      modal.hide();

      Swal.fire('Berhasil', 'Sesi Member dimulai', 'success');
      setTimeout(() => {
        loadAllActiveSessions();
      }, 1000);
    } else {
      Swal.fire({
        title: 'Sesi gagal dimulai',
        html: `<strong>${data.message}</strong>`,
        icon: 'error',
        confirmButtonText: 'OK'
      });
    }
  })
  .catch(error => {
    console.error('Fallback error:', error);
    Swal.fire('Error', 'Fallback method juga gagal', 'error');
  });
}

// Fungsi untuk retry member session dengan force parameter
function retryMemberSessionWithForce() {
  const selectPaket = document.getElementById('selectPaket');
  const selectedPaketId = selectPaket.value;

  console.log('=== RETRY MEMBER SESSION WITH FORCE ===');

  const formData = new FormData();
  formData.append('station_id', currentStationId);
  formData.append('jenis_user', 'member');

  const memberId = selectedMember.id || selectedMember.member_id || selectedMember.id_member || selectedMember.user_id;
  formData.append('member_id', memberId);

  // Tambahkan data member lengkap untuk retry
  const memberName = selectedMember.nama_member || selectedMember.name || selectedMember.nama || selectedMember.member_name || selectedMember.username || 'Member';
  const memberSaldo = selectedMember.saldo || selectedMember.balance || 0;

  formData.append('member_name', memberName);
  formData.append('member_saldo', memberSaldo);

  // Tambahkan semua data member yang tersedia dari response
  Object.keys(selectedMember).forEach(key => {
    if (selectedMember[key] !== null && selectedMember[key] !== undefined) {
      formData.append(key, selectedMember[key]);
    }
  });

  // Tambahkan data konsol dan station untuk retry
  const stationCard = document.querySelector(`[data-station-id="${currentStationId}"]`);
  const konsolId = stationCard.getAttribute('data-konsol-id');
  const hargaPersonal = stationCard.getAttribute('data-harga-personal');
  const hargaMember = stationCard.getAttribute('data-harga-member');

  if (konsolId) formData.append('konsol_id', konsolId);
  if (hargaPersonal) formData.append('harga_personal', hargaPersonal);
  if (hargaMember) formData.append('harga_member', hargaMember);

  // Tambahkan parameter tambahan untuk retry
  formData.append('id_konsol', konsolId);
  formData.append('id_station', currentStationId);
  formData.append('nama', memberName);
  formData.append('member_nama', memberName);

  if (selectedMember.id) {
    formData.append('id', selectedMember.id);
  }

  if (selectedPaketId === 'non_paket') {
    formData.append('id_paket', '');
    formData.append('paket_id', '');
    formData.append('is_non_paket', '1');
  } else {
    formData.append('id_paket', selectedPaketId);
    formData.append('paket_id', selectedPaketId);
  }

  // Parameter force untuk bypass validasi yang bermasalah
  formData.append('force_member_start', '1');
  formData.append('ignore_member_conflict', '1');
  formData.append('bypass_validation', '1');
  formData.append('ignore_personal_sessions', '1');
  formData.append('allow_member_with_personal', '1');
  formData.append('force_bypass_all_validation', '1');

  console.log('Retry form data:', {
    station_id: currentStationId,
    member_id: memberId,
    member_name: memberName,
    konsol_id: konsolId,
    force_member_start: '1',
    ignore_member_conflict: '1',
    bypass_validation: '1'
  });

  // Debug: tampilkan semua FormData entries untuk retry
  console.log('=== RETRY FORM DATA ENTRIES ===');
  for (let [key, value] of formData.entries()) {
    console.log(`${key}: ${value}`);
  }

  fetch('<?= base_url('dashboard/mulai') ?>', {
    method: 'POST',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    },
    body: formData
  })
  .then(response => {
    console.log('Retry response status:', response.status);
    return response.json();
  })
  .then(data => {
    console.log('Retry response data:', data);

    if (data.success) {
      const modal = bootstrap.Modal.getInstance(document.getElementById('startSessionModal'));
      modal.hide();

      Swal.fire('Berhasil', 'Sesi Member berhasil dimulai (dengan force)', 'success');
      setTimeout(() => {
        loadAllActiveSessions();
      }, 1000);
    } else {
      Swal.fire({
        title: 'Retry Failed',
        html: `Masih gagal setelah retry:<br><br><strong>${data.message}</strong><br><br>Silakan hubungi admin untuk memeriksa validasi server.`,
        icon: 'error',
        confirmButtonText: 'OK'
      });
    }
  })
  .catch(error => {
    console.error('Error in retry member session:', error);
    Swal.fire('Error', 'Terjadi kesalahan saat retry member session', 'error');
  });
}



// Fungsi untuk auto-stop session tanpa konfirmasi (untuk timer habis)
function autoStopSession(stationId) {
  console.log(`Auto-stopping session for station ${stationId}`);

  const formData = new FormData();
  formData.append('station_id', stationId);

  // Get current price from the card display
  const stationCard = document.querySelector(`[data-station-id="${stationId}"]`);
  if (stationCard) {
    const priceElement = stationCard.querySelector('.station-price');
    if (priceElement) {
      const currentPrice = priceElement.textContent.replace(/[^\d]/g, ''); // Extract numbers only
      if (currentPrice && currentPrice > 0) {
        formData.append('harga', currentPrice);
      }
    }
  }

  fetch('<?= base_url('dashboard/stop') ?>', {
    method: 'POST',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    },
    body: formData
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      console.log(`Session ${stationId} auto-stopped successfully`);

      // Clear timer if exists
      if (currentTimers[stationId]) {
        clearInterval(currentTimers[stationId]);
        delete currentTimers[stationId];
      }

      // Clear session storage
      clearSessionStorage(stationId);

      // Reload active sessions
      setTimeout(() => {
        loadAllActiveSessions();
      }, 1000);

    } else {
      console.error(`Failed to auto-stop session ${stationId}:`, data.message);
    }
  })
  .catch(error => {
    console.error('Error auto-stopping session:', error);
  });
}

// Fungsi untuk stop session dengan konfirmasi (untuk manual stop)
function stopSession(stationId) {
  Swal.fire({
    title: 'Konfirmasi',
    text: 'Yakin ingin menghentikan sesi ini?',
    icon: 'warning',
    showCancelButton: true,
    confirmButtonText: 'Ya, Stop',
    cancelButtonText: 'Batal'
  }).then((result) => {
    if (result.isConfirmed) {
      const formData = new FormData();
      formData.append('station_id', stationId);

      // Get current price from the card display
      const stationCard = document.querySelector(`[data-station-id="${stationId}"]`);
      if (stationCard) {
        const priceElement = stationCard.querySelector('.station-price');
        if (priceElement) {
          const currentPrice = priceElement.textContent.replace(/[^\d]/g, ''); // Extract numbers only
          if (currentPrice && currentPrice > 0) {
            formData.append('harga', currentPrice);
          }
        }
      }

      fetch('<?= base_url('dashboard/stop') ?>', {
        method: 'POST',
        headers: {
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          Swal.fire('Berhasil', data.message || 'Sesi berhasil dihentikan', 'success');

          // Clear timer if exists
          if (currentTimers[stationId]) {
            clearInterval(currentTimers[stationId]);
            delete currentTimers[stationId];
          }

          // Clear session storage
          clearSessionStorage(stationId);

          // Reload active sessions
          setTimeout(() => {
            loadAllActiveSessions();
          }, 1000);

        } else {
          Swal.fire('Error', data.message || 'Gagal menghentikan sesi', 'error');
        }
      })
      .catch(error => {
        console.error('Error stopping session:', error);
        Swal.fire('Error', 'Terjadi kesalahan saat menghentikan sesi', 'error');
      });
    }
  });
}

// FIXED: Load all active sessions with proper member session handling
function loadAllActiveSessions() {
  console.log('Loading active sessions...');

  fetch('<?= base_url('dashboard/getSessionStatus') ?>', {
    method: 'GET',
    headers: {
      'X-Requested-With': 'XMLHttpRequest',
      'Content-Type': 'application/json'
    }
  })
  .then(response => response.json())
  .then(data => {
    console.log('Active sessions response:', data);

    if (data.success) {
      // Clear all existing timers first
      Object.values(currentTimers).forEach(timer => clearInterval(timer));
      currentTimers = {};

      // Reset all station cards to default state
      document.querySelectorAll('.card-konsol').forEach(card => {
        card.classList.remove('active');
        card.setAttribute('data-status', 'kosong');

        const stationId = card.getAttribute('data-station-id');
        const userInfo = card.querySelector('.user-info');
        const hargaElement = card.querySelector(`#harga-${stationId}`);
        const timerElement = card.querySelector('.timer');
        const buttonGroup = card.querySelector('.btn-icon-group');

        if (userInfo) userInfo.textContent = 'Ready';
        if (hargaElement) hargaElement.textContent = 'PlaySphere';
        if (timerElement) timerElement.textContent = '00:00:00';
        if (buttonGroup) {
          buttonGroup.innerHTML = `
            <button class="btn-icon btn-play" data-id="${stationId}" title="Mulai">
              <i class="bi bi-play-fill"></i>
            </button>
          `;
        }
      });

      // Update each active session
      data.sessions.forEach(session => {
        console.log('Processing session:', session);
        console.log('Session waktu_mulai:', session.waktu_mulai);
        console.log('Session jenis_user:', session.jenis_user);
        console.log('Session nama_paket:', session.nama_paket);
        console.log('Session id_paket:', session.id_paket);
        console.log('Session durasi_sisa:', session.durasi_sisa);
        console.log('Session member_saldo:', session.member_saldo);
        console.log('Session sisa_saldo:', session.sisa_saldo);
        const stationId = session.station_id;
        const stationCard = document.querySelector(`[data-station-id="${stationId}"]`);
        if (!stationCard) return;

        // Update station card to show active state
        stationCard.classList.add('active');
        stationCard.setAttribute('data-status', 'berjalan');

        // Update user info
        const userInfo = stationCard.querySelector('.user-info');
        if (session.jenis_user === 'member') {
          userInfo.innerHTML = `<span class="user-member"><i class="bi bi-person-fill"></i> ${session.nama_member || 'Member'}</span>`;
        } else {
          userInfo.innerHTML = 'Personal';
        }

        // Update price/package info
        const hargaElement = stationCard.querySelector(`#harga-${stationId}`);
        if (session.jenis_user === 'member') {
          if (session.id_paket && session.id_paket !== '' && session.id_paket !== null && session.nama_paket) {
            // Member dengan paket - tampilkan nama paket
            hargaElement.textContent = session.nama_paket;
            console.log(`Station ${stationId} - Member paket: ${session.nama_paket} (ID: ${session.id_paket})`);
          } else {
            // Member non-paket - tampilkan sisa saldo yang berkurang setiap menit
            const memberSaldo = session.member_saldo || session.sisa_saldo || 0;
            hargaElement.textContent = `Rp ${parseInt(memberSaldo).toLocaleString('id-ID')}`;
            console.log(`Station ${stationId} - Member non-paket saldo: ${memberSaldo} (ID paket: ${session.id_paket})`);
          }
        } else {
          // For personal sessions, calculate current price based on elapsed time
          if (session.waktu_mulai) {
            const sessionStart = new Date(session.waktu_mulai).getTime();
            const elapsed = Math.floor((Date.now() - sessionStart) / 1000);

            // Calculate price using the same logic as updatePersonalPrice
            const hargaPersonalPerJam = parseInt(stationCard.getAttribute('data-harga-personal')) || 8000;
            const hargaPer15Menit = hargaPersonalPerJam / 4;
            const elapsedMinutes = Math.floor(elapsed / 60);
            const blok15Menit = Math.floor(elapsedMinutes / 15);
            const totalBlok = Math.max(1, blok15Menit + 1);
            const totalHarga = totalBlok * hargaPer15Menit;

            hargaElement.textContent = 'Rp ' + totalHarga.toLocaleString('id-ID');

            // Update last_blok untuk sinkronisasi
            localStorage.setItem(`last_blok_${stationId}`, totalBlok.toString());
          } else {
            // Fallback to server data
            hargaElement.textContent = 'Rp ' + parseInt(session.harga_total || 0).toLocaleString('id-ID');
          }
        }

        // Update buttons
        const buttonGroup = stationCard.querySelector('.btn-icon-group');
        let buttonsHtml = `
          <button class="btn-icon btn-stop" data-id="${stationId}" title="Berhenti">
            <i class="bi bi-stop-fill"></i>
          </button>
        `;

        // Only show "Tambah Paket" button for member sessions with existing packages
        if (session.jenis_user === 'member' && session.nama_paket) {
          buttonsHtml += `
            <button class="btn-icon btn-plus" data-id="${stationId}" title="Tambah Paket">
              <i class="bi bi-plus-circle"></i>
            </button>
          `;
        }

        buttonsHtml += `
          <button class="btn-icon btn-move" data-id="${stationId}" title="Pindah Station">
            <i class="bi bi-arrow-left-right"></i>
          </button>
        `;

        buttonGroup.innerHTML = buttonsHtml;

        // Update timer data-seconds attribute
        const timerElement = stationCard.querySelector(`#timer-${stationId}`);
        if (timerElement) {
          if (session.jenis_user === 'member') {
            if (session.id_paket && session.id_paket !== '' && session.id_paket !== null) {
              // Member dengan paket - countdown dari durasi paket
              const durasiSisa = parseInt(session.durasi_sisa) || 0;
              timerElement.setAttribute('data-seconds', durasiSisa);
              console.log(`Station ${stationId} - Member paket timer: ${durasiSisa} seconds (paket ID: ${session.id_paket})`);
            } else {
              // Member non-paket - countdown dari saldo yang dikonversi ke waktu
              const durasiSisa = parseInt(session.durasi_sisa) || 0;
              timerElement.setAttribute('data-seconds', durasiSisa);
              console.log(`Station ${stationId} - Member non-paket timer: ${durasiSisa} seconds (no paket)`);

              // Store session start time and initial saldo for real-time calculation
              const sessionStartKey = `session_start_${stationId}`;
              if (!localStorage.getItem(sessionStartKey)) {
                const sessionStartTime = new Date(session.waktu_mulai).getTime();
                localStorage.setItem(sessionStartKey, sessionStartTime.toString());
                console.log(`Station ${stationId} - Stored session start time: ${new Date(sessionStartTime)}`);
              }

              // Store initial saldo
              const initialSaldo = session.member_saldo || session.sisa_saldo || 0;
              stationCard.setAttribute('data-initial-saldo', initialSaldo);
              console.log(`Station ${stationId} - Stored initial saldo: ${initialSaldo}`);
            }
          } else {
            // Personal session - set to 0 for count-up timer
            timerElement.setAttribute('data-seconds', 0);
          }
        }
      });

      // Reinitialize timers after updating sessions
      setTimeout(() => {
        initializeTimers();
      }, 100);
    }
  })
  .catch(error => {
    console.error('Error loading active sessions:', error);
  });
}

// Fungsi untuk show modal move station
function showMoveStationModal(stationId) {
  console.log('Opening move station modal for station:', stationId);
  currentStationId = stationId;
  selectedTargetStationId = null;

  // Reset modal state
  document.getElementById('confirmMoveBtn').disabled = true;
  document.getElementById('availableStationsList').innerHTML = '';
  document.getElementById('noStationsMessage').style.display = 'none';

  // Get current station info
  const stationCard = document.querySelector(`[data-station-id="${stationId}"]`);
  if (!stationCard) {
    Swal.fire('Error', 'Station tidak ditemukan', 'error');
    return;
  }

  // Check if station is active
  if (!stationCard.classList.contains('active')) {
    Swal.fire('Error', 'Hanya station yang sedang aktif yang bisa dipindahkan', 'error');
    return;
  }

  // Get station info
  const stationName = stationCard.querySelector('.fw-bold').textContent.trim();
  const konsolName = stationCard.querySelector('.card-title').textContent.trim();

  // Update modal info
  document.getElementById('moveFromStationName').textContent = stationName;
  document.getElementById('moveKonsolName').textContent = konsolName;

  // Load available stations
  loadAvailableStationsForMove(stationId);

  // Show modal
  const modal = new bootstrap.Modal(document.getElementById('moveStationModal'));
  modal.show();
}

// Variabel untuk menyimpan station tujuan yang dipilih
let selectedTargetStationId = null;

// Fungsi untuk memuat station yang tersedia untuk pindah
function loadAvailableStationsForMove(stationId) {
  console.log('Loading available stations for move from station:', stationId);

  fetch(`<?= base_url('dashboard/getAvailableStationsForMove') ?>/${stationId}`, {
    method: 'GET',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => response.json())
  .then(data => {
    console.log('Available stations response:', data);

    const stationsContainer = document.getElementById('availableStationsList');
    stationsContainer.innerHTML = '';

    if (data.success && data.stations && data.stations.length > 0) {
      // Tampilkan station yang tersedia
      data.stations.forEach(station => {
        const stationCard = document.createElement('div');
        stationCard.className = 'col-6 mb-2';
        stationCard.innerHTML = `
          <div class="card station-card-mini border-secondary" data-station-id="${station.id}">
            <div class="card-body text-center p-2">
              <i class="bi bi-display text-primary" style="font-size: 1rem;"></i>
              <h6 class="card-title mb-1" style="font-size: 0.8rem;">${station.nama_station}</h6>
              <p class="card-text mb-1" style="font-size: 0.7rem;">${station.nama_konsol}</p>
              <div class="d-grid">
                <button class="btn btn-sm btn-outline-primary select-station-btn" type="button">
                  <i class="bi bi-check-circle"></i> Pilih
                </button>
              </div>
            </div>
          </div>
        `;

        stationsContainer.appendChild(stationCard);

        // Add click event to select station
        stationCard.querySelector('.station-card-mini').addEventListener('click', function() {
          selectTargetStation(station.id);
        });
      });

      document.getElementById('noStationsMessage').style.display = 'none';
    } else {
      // Tampilkan pesan tidak ada station yang tersedia
      document.getElementById('noStationsMessage').style.display = 'block';
      document.getElementById('confirmMoveBtn').disabled = true;
    }
  })
  .catch(error => {
    console.error('Error loading available stations:', error);
    document.getElementById('availableStationsList').innerHTML = `
      <div class="col-12">
        <div class="alert alert-danger py-2">
          <small>Error: Gagal memuat daftar station</small>
        </div>
      </div>
    `;
  });
}

// Fungsi untuk memilih station tujuan
function selectTargetStation(stationId) {
  console.log('Selected target station:', stationId);
  selectedTargetStationId = stationId;

  // Reset semua card selection
  document.querySelectorAll('.station-card-mini').forEach(card => {
    card.classList.remove('selected');
  });

  // Highlight selected card
  const selectedCard = document.querySelector(`.station-card-mini[data-station-id="${stationId}"]`);
  if (selectedCard) {
    selectedCard.classList.add('selected');
  }

  // Enable confirm button
  document.getElementById('confirmMoveBtn').disabled = false;
}

// Fungsi untuk konfirmasi dan eksekusi pindah station
function confirmMoveStation() {
  if (!selectedTargetStationId || !currentStationId) {
    Swal.fire('Error', 'Pilih station tujuan terlebih dahulu', 'error');
    return;
  }

  console.log(`Confirming move from station ${currentStationId} to station ${selectedTargetStationId}`);

  // Backup localStorage data untuk personal session sebelum pindah
  const sessionStartTime = localStorage.getItem(`session_start_${currentStationId}`);
  const lastBlok = localStorage.getItem(`last_blok_${currentStationId}`);
  const lastSaldoUpdate = localStorage.getItem(`last_saldo_update_${currentStationId}`);

  console.log('Backing up localStorage data for move:', {
    sessionStartTime,
    lastBlok,
    lastSaldoUpdate,
    fromStation: currentStationId,
    toStation: selectedTargetStationId
  });

  const formData = new FormData();
  formData.append('from_station_id', currentStationId);
  formData.append('to_station_id', selectedTargetStationId);

  fetch('<?= base_url('dashboard/moveSession') ?>', {
    method: 'POST',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    },
    body: formData
  })
  .then(response => response.json())
  .then(data => {
    console.log('Move session response:', data);

    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('moveStationModal'));
    modal.hide();

    if (data.success) {
      // Transfer localStorage data dari station lama ke station baru untuk personal session
      if (sessionStartTime) {
        console.log(`Transferring session start time from station ${currentStationId} to ${selectedTargetStationId}: ${sessionStartTime}`);
        localStorage.setItem(`session_start_${selectedTargetStationId}`, sessionStartTime);
        localStorage.removeItem(`session_start_${currentStationId}`);
      }

      if (lastBlok) {
        console.log(`Transferring last blok from station ${currentStationId} to ${selectedTargetStationId}: ${lastBlok}`);
        localStorage.setItem(`last_blok_${selectedTargetStationId}`, lastBlok);
        localStorage.removeItem(`last_blok_${currentStationId}`);
      }

      if (lastSaldoUpdate) {
        console.log(`Transferring last saldo update from station ${currentStationId} to ${selectedTargetStationId}: ${lastSaldoUpdate}`);
        localStorage.setItem(`last_saldo_update_${selectedTargetStationId}`, lastSaldoUpdate);
        localStorage.removeItem(`last_saldo_update_${currentStationId}`);
      }

      // Clear any existing timer for old station
      if (currentTimers[currentStationId]) {
        clearInterval(currentTimers[currentStationId]);
        delete currentTimers[currentStationId];
        console.log(`Cleared timer for old station ${currentStationId}`);
      }

      Swal.fire({
        title: 'Berhasil',
        text: 'Sesi berhasil dipindahkan ke station baru',
        icon: 'success',
        timer: 2000,
        showConfirmButton: false
      });

      // Reload active sessions to update display
      setTimeout(() => {
        loadAllActiveSessions();
      }, 1000);
    } else {
      Swal.fire('Error', data.message || 'Gagal memindahkan sesi', 'error');
    }
  })
  .catch(error => {
    console.error('Error moving session:', error);
    Swal.fire('Error', 'Terjadi kesalahan saat memindahkan sesi', 'error');
  });
}

// Fungsi untuk show modal add package
function showAddPackageModal(stationId) {
  currentStationId = stationId;
  console.log('Opening add package modal for station:', stationId);

  // Get station info
  const stationCard = document.querySelector(`[data-station-id="${stationId}"]`);
  if (!stationCard) {
    Swal.fire('Error', 'Station tidak ditemukan', 'error');
    return;
  }

  const konsolId = stationCard.getAttribute('data-konsol-id');
  const memberName = stationCard.querySelector('.user-member')?.textContent || 'Unknown';

  // Get actual member saldo from session info
  fetch(`<?= base_url('dashboard/getSessionInfo') ?>/${stationId}`, {
    method: 'GET',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => response.json())
  .then(data => {
    let memberSaldo = 'Rp 0';
    if (data.success && data.session && data.session.jenis_user === 'member') {
      const saldo = data.session.member_saldo || 0;
      memberSaldo = `Rp ${parseInt(saldo).toLocaleString('id-ID')}`;
    }

    console.log('Station card attributes:', {
      stationId: stationId,
      konsolId: konsolId,
      memberName: memberName,
      memberSaldo: memberSaldo
    });

    // Update modal info
    document.getElementById('addPackageMemberName').textContent = memberName;
    document.getElementById('addPackageMemberSaldo').textContent = memberSaldo;
  })
  .catch(error => {
    console.error('Error getting member saldo:', error);
    document.getElementById('addPackageMemberName').textContent = memberName;
    document.getElementById('addPackageMemberSaldo').textContent = 'Rp 0';
  });

  // Load available packages
  loadAddPackages(konsolId);

  // Show modal
  const modal = new bootstrap.Modal(document.getElementById('addPackageModal'));
  modal.show();
}

// Fungsi untuk load paket yang tersedia untuk tambah paket
function loadAddPackages(konsolId) {
  console.log('Loading packages for konsol:', konsolId);

  const url = `<?= base_url('dashboard/getPaketByKonsol') ?>/${konsolId}`;
  console.log('Fetching URL:', url);

  fetch(url, {
    method: 'GET',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => {
    console.log('Add package response status:', response.status);
    return response.json();
  })
  .then(data => {
    console.log('Add package response:', data);

    if (data.success && data.pakets && data.pakets.length > 0) {
      displayAddPackages(data.pakets);
    } else {
      console.log('No packages available. Response:', data);
      document.getElementById('addPackageList').innerHTML = `
        <div class="col-12">
          <div class="alert alert-warning">
            <i class="bi bi-exclamation-triangle me-2"></i>
            Tidak ada paket tersedia untuk konsol ini (ID: ${konsolId}).
            <br><small class="mt-2 d-block">Silakan tambahkan paket untuk konsol ini di menu Paket.</small>
          </div>
        </div>
      `;
    }
  })
  .catch(error => {
    console.error('Error loading add packages:', error);
    document.getElementById('addPackageList').innerHTML = `
      <div class="col-12">
        <div class="alert alert-danger">
          <i class="bi bi-x-circle me-2"></i>
          Terjadi kesalahan saat memuat paket.
        </div>
      </div>
    `;
  });
}

// Fungsi untuk display paket dalam card yang menarik
function displayAddPackages(pakets) {
  const packageList = document.getElementById('addPackageList');
  let packagesHtml = '';

  const colors = ['primary', 'success', 'warning', 'info', 'danger', 'secondary'];

  pakets.forEach((paket, index) => {
    const color = colors[index % colors.length];
    const durasi = paket.durasi;
    const harga = parseInt(paket.harga);

    // Parse durasi untuk display yang lebih baik
    let durasiDisplay = durasi;
    if (durasi.includes(':')) {
      const [hours, minutes] = durasi.split(':');
      if (parseInt(hours) > 0) {
        durasiDisplay = `${parseInt(hours)} jam ${parseInt(minutes)} menit`;
      } else {
        durasiDisplay = `${parseInt(minutes)} menit`;
      }
    } else {
      durasiDisplay = `${durasi} menit`;
    }

    packagesHtml += `
      <div class="col-6 mb-2">
        <div class="card border-${color} package-card-mini" style="cursor: pointer;"
             onclick="selectAddPackage('${paket.id}', '${paket.nama_paket}', '${durasi}', ${harga})">
          <div class="card-body p-1 text-center">
            <div class="d-flex align-items-center justify-content-between">
              <small class="text-${color} fw-bold" style="font-size: 0.7rem;">${paket.nama_paket}</small>
              <i class="bi bi-gift-fill text-${color}" style="font-size: 0.7rem;"></i>
            </div>
            <div class="row g-1 mt-1">
              <div class="col-6">
                <div class="d-flex align-items-center">
                  <i class="bi bi-clock text-${color} me-1" style="font-size: 0.7rem;"></i>
                  <small class="text-${color} fw-bold" style="font-size: 0.7rem;">${durasiDisplay}</small>
                </div>
              </div>
              <div class="col-6">
                <div class="d-flex align-items-center justify-content-end">
                  <i class="bi bi-currency-dollar text-${color} me-1" style="font-size: 0.7rem;"></i>
                  <small class="text-${color} fw-bold" style="font-size: 0.7rem;">Rp ${harga.toLocaleString('id-ID')}</small>
                </div>
              </div>
            </div>
            <button class="btn btn-${color} btn-sm w-100 mt-1" style="font-size: 0.7rem; padding: 0.1rem 0.25rem;">
              <i class="bi bi-plus-circle me-1"></i>Pilih
            </button>
          </div>
        </div>
      </div>
    `;
  });

  packageList.innerHTML = packagesHtml;
}

// Fungsi untuk select dan add paket
function selectAddPackage(paketId, namaPaket, durasi, harga) {
  console.log('Selected package:', { paketId, namaPaket, durasi, harga });

  // Konfirmasi sebelum menambah paket
  Swal.fire({
    title: 'Konfirmasi Tambah Paket',
    html: `
      <div class="text-start">
        <p><strong>Paket:</strong> ${namaPaket}</p>
        <p><strong>Durasi:</strong> ${durasi}</p>
        <p><strong>Harga:</strong> Rp ${harga.toLocaleString('id-ID')}</p>
        <hr>
        <p class="text-info"><i class="bi bi-info-circle me-2"></i>Durasi akan ditambahkan ke waktu yang tersisa dan saldo akan dipotong sesuai harga paket.</p>
      </div>
    `,
    icon: 'question',
    showCancelButton: true,
    confirmButtonText: '<i class="bi bi-plus-circle me-1"></i>Ya, Tambah Paket',
    cancelButtonText: '<i class="bi bi-x-circle me-1"></i>Batal',
    confirmButtonColor: '#28a745',
    cancelButtonColor: '#6c757d'
  }).then((result) => {
    if (result.isConfirmed) {
      processAddPackage(paketId, namaPaket, durasi, harga);
    }
  });
}

// Fungsi untuk memproses penambahan paket ke session
function processAddPackage(paketId, namaPaket, durasi, harga) {
  console.log('Processing add package:', { paketId, namaPaket, durasi, harga });

  const formData = new FormData();
  formData.append('station_id', currentStationId);
  formData.append('paket_id', paketId);

  // Show loading
  Swal.fire({
    title: 'Menambah Paket...',
    text: 'Mohon tunggu sebentar',
    allowOutsideClick: false,
    allowEscapeKey: false,
    showConfirmButton: false,
    didOpen: () => {
      Swal.showLoading();
    }
  });

  fetch('<?= base_url('dashboard/addPackageToSession') ?>', {
    method: 'POST',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    },
    body: formData
  })
  .then(response => response.json())
  .then(data => {
    console.log('Add package response:', data);

    if (data.success) {
      // Close add package modal
      const modal = bootstrap.Modal.getInstance(document.getElementById('addPackageModal'));
      modal.hide();

      // Show success message
      Swal.fire({
        title: 'Berhasil!',
        html: `
          <div class="text-start">
            <p><i class="bi bi-check-circle text-success me-2"></i>Paket <strong>${namaPaket}</strong> berhasil ditambahkan!</p>
            <p><strong>Durasi ditambahkan:</strong> ${durasi}</p>
            <p><strong>Saldo dipotong:</strong> Rp ${harga.toLocaleString('id-ID')}</p>
          </div>
        `,
        icon: 'success',
        timer: 3000,
        showConfirmButton: false
      });

      // Reload sessions to update display
      setTimeout(() => {
        loadAllActiveSessions();
      }, 1000);

    } else {
      Swal.fire({
        title: 'Gagal!',
        text: data.message || 'Gagal menambah paket',
        icon: 'error',
        confirmButtonText: 'OK'
      });
    }
  })
  .catch(error => {
    console.error('Error adding package:', error);
    Swal.fire({
      title: 'Error!',
      text: 'Terjadi kesalahan saat menambah paket',
      icon: 'error',
      confirmButtonText: 'OK'
    });
  });
}

// Fungsi untuk inisialisasi timer
function initializeTimers() {
  console.log('Initializing timers...');

  // Clear existing timers first
  Object.values(currentTimers).forEach(timer => clearInterval(timer));
  currentTimers = {};

  // Initialize timers for active sessions (exclude debug bar timers)
  console.log('Initializing timers...');
  const timers = document.querySelectorAll('.card-konsol .timer');
  console.log(`Found ${timers.length} dashboard timer elements`);

  timers.forEach((timerEl, index) => {
    console.log(`Processing timer ${index + 1}: ${timerEl.id}`);
    const stationId = timerEl.id.replace('timer-', '');
    const stationCard = timerEl.closest('.card-konsol');

    // Check if stationCard exists to prevent null error
    if (!stationCard) {
      console.warn(`Station card not found for timer ${timerEl.id}`);
      console.warn(`Timer element HTML:`, timerEl.outerHTML);
      console.warn(`Timer parent:`, timerEl.parentElement);

      // Try alternative method to find station card
      const alternativeCard = document.querySelector(`[data-station-id="${stationId}"]`);
      if (alternativeCard) {
        console.log(`Found alternative station card for ${stationId}`);
        // Use alternative card but skip timer initialization for safety
      }
      return;
    }

    const isActive = stationCard.classList.contains('active');

    if (isActive) {
      let seconds = parseInt(timerEl.dataset.seconds);
      console.log(`Station ${stationId} timer: ${seconds} seconds`);

      if (!isNaN(seconds) && seconds > 0) {
        // For member sessions with package or non-package with saldo, countdown
        console.log(`Station ${stationId} - Member countdown session with ${seconds} seconds`);
        updateTimerDisplay(timerEl, seconds);

        // Check if this is a non-package member session
        const userInfo = stationCard.querySelector('.user-member');
        const hargaText = stationCard.querySelector(`#harga-${stationId}`).textContent;
        const isNonPackageMember = userInfo && hargaText.includes('Rp') && !hargaText.includes('Paket');

        console.log(`Station ${stationId} - User info: ${userInfo ? 'member' : 'personal'}, Harga text: "${hargaText}", Is non-package: ${isNonPackageMember}`);

        currentTimers[stationId] = setInterval(() => {
          if (--seconds <= 0) {
            clearInterval(currentTimers[stationId]);
            delete currentTimers[stationId];
            timerEl.textContent = "00:00:00";

            // Check if this is a member package session that should auto-stop
            const stationCard = timerEl.closest('.card-konsol');
            const userInfo = stationCard.querySelector('.user-member');
            const hargaText = stationCard.querySelector(`#harga-${stationId}`).textContent;
            const isMemberPackage = userInfo && !hargaText.includes('Rp');

            console.log(`Station ${stationId} - Timer expired. User: ${userInfo ? 'member' : 'personal'}, Harga text: "${hargaText}", Is package: ${isMemberPackage}`);

            if (isMemberPackage) {
              // Member package session - auto stop and show notification
              console.log(`Station ${stationId} - Member package time expired, auto stopping session`);

              Swal.fire({
                title: 'Waktu Habis',
                text: 'Waktu paket telah habis. Sesi akan dihentikan.',
                icon: 'info',
                timer: 2000,
                showConfirmButton: false,
                allowOutsideClick: false,
                allowEscapeKey: false
              });

              // Auto stop the session after 2 seconds
              setTimeout(() => {
                autoStopSession(stationId);
              }, 2000);
            } else if (userInfo && hargaText.includes('Rp')) {
              // Member non-paket session - auto stop when time expires
              console.log(`Station ${stationId} - Member non-paket time expired, auto stopping session`);

              Swal.fire({
                title: 'Waktu Habis',
                text: 'Waktu sesi telah habis. Sesi akan dihentikan.',
                icon: 'info',
                timer: 2000,
                showConfirmButton: false,
                allowOutsideClick: false,
                allowEscapeKey: false
              });

              // Auto stop the session after 2 seconds
              setTimeout(() => {
                autoStopSession(stationId);
              }, 2000);
            } else {
              // Personal session - just reload
              setTimeout(() => {
                loadAllActiveSessions();
              }, 1000);
            }
          } else {
            updateTimerDisplay(timerEl, seconds);

            // For non-package member sessions, update display every 10 seconds for real-time feel
            if (isNonPackageMember && seconds % 10 === 0) {
              updateMemberNonPackageDisplay(stationId);
            }
          }
        }, 1000);
      } else {
        // For personal sessions, count up from session start time
        console.log(`Station ${stationId} - Personal session`);

        // Try to get session start time from localStorage first
        let sessionStartTime = getSessionStartFromStorage(stationId);

        if (sessionStartTime && !isNaN(sessionStartTime)) {
          console.log(`Using localStorage time for station ${stationId}: ${new Date(sessionStartTime)}`);
          startPersonalTimer(timerEl, stationId, sessionStartTime);
        } else {
          console.log(`No valid localStorage time for station ${stationId}, trying server...`);

          // Try to get from server via AJAX
          getSessionStartTime(stationId, (startTime) => {
            if (startTime) {
              const parsedTime = parseSessionStartTime(startTime);
              if (parsedTime && !isNaN(parsedTime)) {
                console.log(`Got session start time from server: ${new Date(parsedTime)}`);
                startPersonalTimer(timerEl, stationId, parsedTime);
              } else {
                console.log(`Invalid server time, using current time`);
                startPersonalTimer(timerEl, stationId, Date.now());
              }
            } else {
              console.log(`No session start time from server, using current time`);
              startPersonalTimer(timerEl, stationId, Date.now());
            }
          });
        }
      }
    }
  });
}

function updateTimerDisplay(el, seconds) {
  const h = String(Math.floor(seconds / 3600)).padStart(2, '0');
  const m = String(Math.floor((seconds % 3600) / 60)).padStart(2, '0');
  const s = String(seconds % 60).padStart(2, '0');
  el.textContent = `${h}:${m}:${s}`;
}



// Function untuk update display member non-paket secara real-time dan update database
function updateMemberNonPackageDisplay(stationId) {
  const stationCard = document.querySelector(`[data-station-id="${stationId}"]`);
  if (!stationCard) return;

  const userInfo = stationCard.querySelector('.user-member');
  const hargaElement = stationCard.querySelector(`#harga-${stationId}`);
  const timerElement = stationCard.querySelector(`#timer-${stationId}`);

  if (userInfo && hargaElement && hargaElement.textContent.includes('Rp') && timerElement) {
    // Update saldo in database every 30 seconds
    const currentTime = Date.now();
    const lastUpdateKey = `last_saldo_update_${stationId}`;
    const lastUpdate = parseInt(localStorage.getItem(lastUpdateKey) || '0');

    if (currentTime - lastUpdate >= 30000) { // 30 seconds
      // Update database
      updateMemberNonPackageSaldoInDatabase(stationId);
      localStorage.setItem(lastUpdateKey, currentTime.toString());
    } else {
      // Just update display
      const currentSeconds = parseInt(timerElement.getAttribute('data-seconds')) || 0;
      const hargaMemberPerJam = parseInt(stationCard.getAttribute('data-harga-member')) || 8000;
      const hargaPerMenit = hargaMemberPerJam / 60;
      const hargaPerDetik = hargaPerMenit / 60;

      // Calculate current saldo based on remaining time
      const currentSaldo = Math.max(0, currentSeconds * hargaPerDetik);

      // Update display
      hargaElement.textContent = `Rp ${parseInt(currentSaldo).toLocaleString('id-ID')}`;

      console.log(`Station ${stationId} - Real-time saldo update: ${currentSaldo} (remaining: ${currentSeconds}s, rate: ${hargaPerDetik}/s)`);
    }
  }
}

// Function untuk update saldo member non-paket di database
function updateMemberNonPackageSaldoInDatabase(stationId) {
  console.log(`Updating member non-package saldo in database for station ${stationId}`);

  const formData = new FormData();
  formData.append('station_id', stationId);

  fetch('<?= base_url('dashboard/updateMemberNonPackageSaldo') ?>', {
    method: 'POST',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    },
    body: formData
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      console.log(`Database saldo update successful: ${data.current_saldo} (remaining: ${data.remaining_seconds}s)`);

      // Update display with accurate data from server
      const stationCard = document.querySelector(`[data-station-id="${stationId}"]`);
      if (stationCard) {
        const hargaElement = stationCard.querySelector(`#harga-${stationId}`);
        const timerElement = stationCard.querySelector(`#timer-${stationId}`);

        if (hargaElement) {
          hargaElement.textContent = `Rp ${parseInt(data.current_saldo).toLocaleString('id-ID')}`;
        }

        if (timerElement) {
          timerElement.setAttribute('data-seconds', data.remaining_seconds);
        }
      }

      // If session was stopped due to insufficient saldo
      if (data.session_stopped) {
        console.log(`Session ${stationId} auto-stopped due to insufficient saldo`);
        Swal.fire({
          title: 'Saldo Habis',
          text: 'Saldo member telah habis. Sesi akan dihentikan.',
          icon: 'warning',
          timer: 2000,
          showConfirmButton: false
        });

        // Reload sessions after 2 seconds
        setTimeout(() => {
          loadAllActiveSessions();
        }, 2000);
      }
    } else {
      console.error(`Error updating saldo in database: ${data.message}`);
    }
  })
  .catch(error => {
    console.error(`Error updating saldo in database: ${error}`);
  });
}

// Fungsi untuk mendapatkan waktu mulai sesi dari server
function getSessionStartTime(stationId, callback) {
  fetch(`<?= base_url('dashboard/getSessionStartTime') ?>/${stationId}`, {
    method: 'GET',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => response.json())
  .then(data => {
    console.log(`Session start time response for station ${stationId}:`, data);
    if (data.success && data.start_time) {
      callback(data.start_time);
    } else {
      callback(null);
    }
  })
  .catch(error => {
    console.error(`Error getting session start time for station ${stationId}:`, error);
    callback(null);
  });
}

// Fungsi untuk memulai timer personal
function startPersonalTimer(timerEl, stationId, startTime) {
  console.log(`Starting personal timer for station ${stationId} with start time: ${new Date(startTime)}`);

  // Validasi waktu mulai
  const now = Date.now();
  const maxAge = 24 * 60 * 60 * 1000; // 24 jam

  if (isNaN(startTime) || startTime <= 0 || startTime > now || (now - startTime) > maxAge) {
    console.log(`Invalid start time, using current time instead`);
    startTime = now;
  }

  // Simpan waktu mulai di localStorage sebagai backup
  localStorage.setItem(`session_start_${stationId}`, startTime.toString());

  const updateElapsed = () => {
    const elapsed = Math.floor((Date.now() - startTime) / 1000);

    // Pastikan elapsed time tidak negatif
    if (elapsed < 0) {
      console.log(`Negative elapsed time detected, resetting start time`);
      startTime = Date.now();
      localStorage.setItem(`session_start_${stationId}`, startTime.toString());
      return;
    }

    updateTimerDisplay(timerEl, elapsed);
    updatePersonalPrice(stationId, elapsed);
  };

  updateElapsed(); // Initial update
  currentTimers[stationId] = setInterval(updateElapsed, 1000);
}

// Fungsi untuk update harga personal setiap 15 menit
function updatePersonalPrice(stationId, elapsedSeconds) {
  const stationCard = document.querySelector(`[data-station-id="${stationId}"]`);
  if (!stationCard) return;

  const hargaPersonalPerJam = parseInt(stationCard.getAttribute('data-harga-personal')) || 8000;
  const hargaPer15Menit = hargaPersonalPerJam / 4; // Harga per jam dibagi 4 (15 menit)

  // Hitung berapa blok 15 menit yang sudah berlalu
  const elapsedMinutes = Math.floor(elapsedSeconds / 60);
  const blok15Menit = Math.floor(elapsedMinutes / 15);

  // Hitung total harga (minimal 1 blok 15 menit)
  const totalBlok = Math.max(1, blok15Menit + 1);
  const totalHarga = totalBlok * hargaPer15Menit;

  // Simpan blok terakhir untuk mendeteksi perubahan
  const lastBlokKey = `last_blok_${stationId}`;
  const lastBlok = parseInt(localStorage.getItem(lastBlokKey)) || 0;

  // Hanya update tampilan jika blok berubah atau pertama kali
  if (totalBlok !== lastBlok) {
    const hargaElement = stationCard.querySelector(`#harga-${stationId}`);
    if (hargaElement) {
      hargaElement.textContent = 'Rp ' + totalHarga.toLocaleString('id-ID');
    }

    // Simpan blok saat ini
    localStorage.setItem(lastBlokKey, totalBlok.toString());

    // Update database saat harga berubah (setiap 15 menit)
    updatePersonalPriceToDatabase(stationId, totalHarga);

    // Log saat harga berubah
    console.log(`Station ${stationId} - Harga updated: Blok ${totalBlok}, Harga: Rp ${totalHarga.toLocaleString('id-ID')}`);
  }
}

// Fungsi untuk update harga personal ke database
function updatePersonalPriceToDatabase(stationId, harga) {
  console.log(`Updating personal price to database - Station ${stationId}: ${harga}`);

  const formData = new FormData();
  formData.append('station_id', stationId);
  formData.append('harga_total', harga);

  fetch('<?= base_url('dashboard/updateHarga') ?>', {
    method: 'POST',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    },
    body: formData
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      console.log(`Personal price update successful - Station ${stationId}: ${harga}`);
    } else {
      console.error(`Personal price update failed - Station ${stationId}:`, data.message);
    }
  })
  .catch(error => {
    console.error(`Personal price update error - Station ${stationId}:`, error);
  });
}

// Fungsi untuk mendapatkan waktu mulai dari localStorage
function getSessionStartFromStorage(stationId) {
  const stored = localStorage.getItem(`session_start_${stationId}`);
  if (stored) {
    const startTime = parseInt(stored);

    // Validasi waktu yang masuk akal (tidak lebih dari 24 jam yang lalu)
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 jam dalam milliseconds

    if (!isNaN(startTime) && startTime > 0 && (now - startTime) < maxAge && startTime <= now) {
      console.log(`Found valid session start time in localStorage for station ${stationId}: ${new Date(startTime)}`);
      return startTime;
    } else {
      console.log(`Invalid or expired session start time in localStorage for station ${stationId}: ${new Date(startTime)}`);
      // Hapus data yang tidak valid
      localStorage.removeItem(`session_start_${stationId}`);
    }
  }
  return null;
}

// Fungsi untuk membersihkan localStorage saat sesi berakhir
function clearSessionStorage(stationId) {
  localStorage.removeItem(`session_start_${stationId}`);
  localStorage.removeItem(`last_blok_${stationId}`);
  localStorage.removeItem(`last_saldo_update_${stationId}`);

  // Clear data-initial-saldo attribute
  const stationCard = document.querySelector(`[data-station-id="${stationId}"]`);
  if (stationCard) {
    stationCard.removeAttribute('data-initial-saldo');
  }

  console.log(`Cleared session storage for station ${stationId}`);
}

// Fungsi untuk parsing waktu mulai sesi dengan berbagai format
function parseSessionStartTime(timeString) {
  console.log(`Parsing time string: "${timeString}"`);

  if (!timeString) {
    console.log('Empty time string');
    return null;
  }

  // Try different parsing methods
  let parsedTime = null;

  // Method 1: ISO 8601 format (2024-01-15T10:30:00+07:00)
  try {
    parsedTime = new Date(timeString).getTime();
    if (!isNaN(parsedTime) && parsedTime > 0) {
      console.log(`Parsed as ISO 8601: ${new Date(parsedTime)}`);
      return parsedTime;
    }
  } catch (e) {
    console.log('Failed to parse as ISO 8601');
  }

  // Method 2: MySQL datetime format (2024-01-15 10:30:00)
  try {
    const mysqlRegex = /^(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})$/;
    const match = timeString.match(mysqlRegex);
    if (match) {
      const [, year, month, day, hour, minute, second] = match;
      parsedTime = new Date(year, month - 1, day, hour, minute, second).getTime();
      if (!isNaN(parsedTime)) {
        console.log(`Parsed as MySQL datetime: ${new Date(parsedTime)}`);
        return parsedTime;
      }
    }
  } catch (e) {
    console.log('Failed to parse as MySQL datetime');
  }

  // Method 3: Unix timestamp (seconds)
  try {
    const timestamp = parseInt(timeString);
    if (!isNaN(timestamp) && timestamp > 1000000000) { // Valid unix timestamp
      parsedTime = timestamp * 1000; // Convert to milliseconds
      console.log(`Parsed as Unix timestamp: ${new Date(parsedTime)}`);
      return parsedTime;
    }
  } catch (e) {
    console.log('Failed to parse as Unix timestamp');
  }

  // Method 4: Unix timestamp (milliseconds)
  try {
    const timestamp = parseInt(timeString);
    if (!isNaN(timestamp) && timestamp > 1000000000000) { // Valid unix timestamp in ms
      parsedTime = timestamp;
      console.log(`Parsed as Unix timestamp (ms): ${new Date(parsedTime)}`);
      return parsedTime;
    }
  } catch (e) {
    console.log('Failed to parse as Unix timestamp (ms)');
  }

  console.log(`Failed to parse time string: "${timeString}"`);
  return null;
}

// Fungsi untuk membersihkan localStorage dari data session yang tidak valid
function cleanupInvalidSessionStorage() {
  console.log('Cleaning up invalid session storage...');
  const now = Date.now();
  const maxAge = 24 * 60 * 60 * 1000; // 24 jam
  const keysToRemove = [];

  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && key.startsWith('session_start_')) {
      const value = localStorage.getItem(key);
      const timestamp = parseInt(value);

      if (isNaN(timestamp) || timestamp <= 0 || timestamp > now || (now - timestamp) > maxAge) {
        console.log(`Removing invalid session storage: ${key} = ${value}`);
        keysToRemove.push(key);

        // Also remove corresponding last_blok entry
        const stationId = key.replace('session_start_', '');
        keysToRemove.push(`last_blok_${stationId}`);
      }
    }
  }

  keysToRemove.forEach(key => localStorage.removeItem(key));
  console.log(`Cleaned up ${keysToRemove.length} invalid session storage entries`);
}

// Fungsi untuk membersihkan semua session storage (untuk testing)
function clearAllSessionStorage() {
  console.log('Clearing all session storage for testing...');
  const keysToRemove = [];

  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && (key.startsWith('session_start_') || key.startsWith('last_blok_'))) {
      keysToRemove.push(key);
    }
  }

  keysToRemove.forEach(key => localStorage.removeItem(key));
  console.log(`Cleared ${keysToRemove.length} session storage entries`);
}

// Fungsi untuk mengecek ketersediaan member
function checkMemberAvailability(member, callback) {
  const memberId = member.id || member.member_id || member.id_member || member.user_id;

  if (!memberId) {
    console.error('Cannot check member availability: no member ID');
    callback(false, 'Unknown');
    return;
  }

  console.log('Checking member availability for ID:', memberId);

  fetch(`<?= base_url('dashboard/checkMemberAvailability') ?>/${memberId}`, {
    method: 'GET',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => response.json())
  .then(data => {
    console.log('Member availability response:', data);

    if (data.success) {
      if (data.available) {
        callback(true, null);
      } else {
        const stationName = data.current_station || 'Station tidak diketahui';
        callback(false, stationName);
      }
    } else {
      // Jika endpoint tidak ada atau error, anggap member tersedia
      console.log('Member availability check failed, assuming available');
      callback(true, null);
    }
  })
  .catch(error => {
    console.error('Error checking member availability:', error);
    // Jika error, anggap member tersedia untuk tidak memblokir user
    callback(true, null);
  });
}
</script>

<?= $this->endSection() ?>
