<?= $this->extend('layout/template') ?>

<?= $this->section('content') ?>
<div class="d-flex justify-content-between align-items-center mb-4">
    <p class="text-muted mb-0"><PERSON><PERSON><PERSON> jenis dan harga <PERSON></p>
    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addModal">
        <i class="fas fa-plus me-2"></i>Tambah PlayStation
    </button>
</div>

<div class="row">
    <?php foreach ($ps_types as $type): ?>
    <div class="col-md-4 mb-4">
        <div class="card border-0 h-100">
            <div class="card-body text-center p-4">
                <div class="bg-primary bg-opacity-10 rounded-circle p-4 mx-auto mb-4" style="width: fit-content">
                    <i class="fas fa-gamepad fa-3x text-primary"></i>
                </div>
                <h4 class="card-title mb-3"><?= $type['name'] ?></h4>
                <h3 class="text-success mb-3">
                    Rp <?= number_format($type['price_per_hour'], 0, ',', '.') ?>
                    <small class="text-muted fs-6">/jam</small>
                </h3>
                <p class="text-muted mb-4"><?= $type['description'] ?></p>
                <div class="d-flex justify-content-center gap-2">
                    <button class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#editModal<?= $type['id'] ?>">
                        <i class="fas fa-edit me-2"></i>Edit
                    </button>
                    <button class="btn btn-danger" onclick="deletePsType(<?= $type['id'] ?>)">
                        <i class="fas fa-trash me-2"></i>Hapus
                    </button>
                </div>
            </div>
        </div>

        <!-- Modal Edit -->
        <div class="modal fade" id="editModal<?= $type['id'] ?>">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Edit PlayStation</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <form action="/pstype/edit/<?= $type['id'] ?>" method="post">
                        <div class="modal-body">
                            <div class="mb-3">
                                <label class="form-label">Nama</label>
                                <input type="text" name="name" class="form-control" value="<?= $type['name'] ?>" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Harga/Jam</label>
                                <input type="number" name="price_per_hour" class="form-control" value="<?= $type['price_per_hour'] ?>" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Deskripsi</label>
                                <textarea name="description" class="form-control" rows="3"><?= $type['description'] ?></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                            <button type="submit" class="btn btn-primary">Simpan</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <?php endforeach; ?>
</div>

<!-- Modal Tambah -->
<div class="modal fade" id="addModal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">Tambah PlayStation</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form action="/pstype/add" method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Nama</label>
                        <input type="text" name="name" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Harga/Jam</label>
                        <input type="number" name="price_per_hour" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Deskripsi</label>
                        <textarea name="description" class="form-control" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    function deletePsType(id) {
        if(confirm('Yakin hapus PlayStation ini?')) {
            window.location.href = `/pstype/delete/${id}`;
        }
    }
</script>
<?= $this->endSection() ?>