<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - PlaySphere</title>

      <!-- Favicon untuk berbagai device -->
  <link rel="icon" type="image/x-icon" href="<?= base_url('assets/img/favicon.ico') ?>">
  <link rel="icon" type="image/png" sizes="32x32" href="<?= base_url('assets/img/favicon-32x32.png') ?>">
  <link rel="icon" type="image/png" sizes="16x16" href="<?= base_url('assets/img/favicon-16x16.png') ?>">
  <link rel="icon" type="image/png" sizes="512x512" href="<?= base_url('assets/img/favicon-512x512.png') ?>">
  <link rel="apple-touch-icon" href="<?= base_url('assets/img/apple-touch-icon.png') ?>">
    
    <!-- Bootstrap CSS -->
    <link href="<?= base_url('assets/css/bootstrap.min.css') ?>" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="<?= base_url('assets/css/bootstrap-icons.min.css') ?>" rel="stylesheet">
    <!-- SweetAlert2 -->
    <script src="<?= base_url('assets/sweetalert2/sweetalert2.all.min.js') ?>"></script>
    
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --success-color: #11998e;
            --danger-color: #f5576c;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            overflow: hidden;
            position: relative;
        }

        /* Animated Spheres Background */
        .sphere {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            animation: float 6s ease-in-out infinite;
        }

        .sphere:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .sphere:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 20%;
            right: 10%;
            animation-delay: 2s;
        }

        .sphere:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        .sphere:nth-child(4) {
            width: 100px;
            height: 100px;
            bottom: 10%;
            right: 20%;
            animation-delay: 1s;
        }

        .sphere:nth-child(5) {
            width: 40px;
            height: 40px;
            top: 50%;
            left: 5%;
            animation-delay: 3s;
        }

        .sphere:nth-child(6) {
            width: 70px;
            height: 70px;
            top: 60%;
            right: 5%;
            animation-delay: 5s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.7;
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
                opacity: 1;
            }
        }

        /* Login Container */
        .login-container {
            position: relative;
            z-index: 10;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-card {
            background: rgba(216, 227, 238, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 450px;
            animation: slideInUp 0.8s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .logo-container {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo-wrapper {
            display: inline-block;
            margin-bottom: 15px;
        }

        .main-logo {
            width: 150px;
            height: auto;
            max-height: 150px;
            object-fit: contain;
            animation: logoPulse 2s ease-in-out infinite;
            filter: drop-shadow(0 5px 15px rgba(0, 0, 0, 0.1));
        }

        @keyframes logoPulse {
            0% {
                transform: scale(1);
                filter: drop-shadow(0 5px 15px rgba(0, 0, 0, 0.1));
            }
            50% {
                transform: scale(1.05);
                filter: drop-shadow(0 8px 25px rgba(102, 126, 234, 0.3));
            }
            100% {
                transform: scale(1);
                filter: drop-shadow(0 5px 15px rgba(0, 0, 0, 0.1));
            }
        }

        .login-title {
            color: #333;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .login-subtitle {
            color: #666;
            margin-bottom: 30px;
        }

        .form-floating {
            margin-bottom: 20px;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 12px 16px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .password-container {
            position: relative;
        }

        .password-container .form-control {
            padding-right: 50px;
        }

        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #666;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .password-toggle:hover {
            background: rgba(102, 126, 234, 0.1);
            color: var(--primary-color);
        }

        .password-toggle i {
            font-size: 1.1rem;
        }

        .btn-login {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            border-radius: 12px;
            padding: 12px 30px;
            font-weight: 600;
            font-size: 16px;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-login:active {
            transform: translateY(0);
        }

        .btn-login.loading {
            pointer-events: none;
        }

        .spinner-border-sm {
            width: 1rem;
            height: 1rem;
        }

        .playsphere-footer {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
            border-radius: 15px;
            padding: 25px;
            margin-top: 25px;
            border: 1px solid rgba(102, 126, 234, 0.1);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
        }

        .footer-brand {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }

        .footer-logo {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            animation: pulse 2s infinite;
        }

        .footer-logo i {
            font-size: 1.2rem;
            color: white;
        }

        .footer-text {
            text-align: left;
        }

        .footer-title {
            color: var(--primary-color);
            margin: 0;
            font-weight: 700;
            font-size: 1.1rem;
        }

        .footer-subtitle {
            color: #666;
            margin: 0;
            font-size: 0.85rem;
            opacity: 0.8;
        }



        .footer-bottom {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 15px;
            border-top: 1px solid rgba(102, 126, 234, 0.1);
            font-size: 0.8rem;
            color: #888;
        }

        .copyright, .version {
            display: flex;
            align-items: center;
        }

        .copyright i, .version i {
            margin-right: 5px;
            color: var(--primary-color);
        }

        /* Responsive */
        @media (max-width: 576px) {
            .login-card {
                padding: 30px 20px;
                margin: 10px;
            }

            .sphere {
                display: none;
            }

            .main-logo {
                width: 60px;
                max-height: 60px;
            }

            .footer-bottom {
                flex-direction: column;
                text-align: center;
            }

            .version {
                margin-top: 5px;
            }
        }

        /* Custom Alert Styles */
        .swal2-popup {
            border-radius: 15px;
        }

        .swal2-title {
            color: #333;
        }
    </style>
</head>
<body>
    <!-- Animated Spheres -->
    <div class="sphere"></div>
    <div class="sphere"></div>
    <div class="sphere"></div>
    <div class="sphere"></div>
    <div class="sphere"></div>
    <div class="sphere"></div>

    <div class="login-container">
        <div class="login-card">
            <div class="logo-container">
                <div class="logo-wrapper">
                    <img src="<?= base_url('assets/img/playsphere3.png') ?>" alt="PlaySphere Logo" class="main-logo">
                </div>
                <h2 class="login-title">PlaySphere</h2>
                <p class="login-subtitle">Game Console Management</p>
            </div>

            <form id="loginForm">
                <div class="form-floating">
                    <input type="text" class="form-control" id="username" name="username" placeholder="Username" required>
                    <label for="username"><i class="bi bi-person me-2"></i>Username</label>
                </div>

                <div class="form-floating password-container">
                    <input type="password" class="form-control" id="password" name="password" placeholder="Password" required>
                    <label for="password"><i class="bi bi-lock me-2"></i>Password</label>
                    <button type="button" class="password-toggle" onclick="togglePassword()">
                        <i class="bi bi-eye" id="passwordToggleIcon"></i>
                    </button>
                </div>

                <button type="submit" class="btn btn-login" id="loginBtn">
                    <span class="btn-text">
                        <i class="bi bi-box-arrow-in-right me-2"></i>Masuk
                    </span>
                    <span class="btn-loading d-none">
                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        Memproses...
                    </span>
                </button>
            </form>

            <div class="playsphere-footer">
                <div class="footer-brand">
                    <div class="footer-logo">
                        <i class="bi bi-controller"></i>
                    </div>
                    <div class="footer-text">
                        <h6 class="footer-title">PlaySphere</h6>
                        <p class="footer-subtitle">Game Console Management</p>
                    </div>
                </div>

                <div class="footer-bottom">
                    <div class="copyright">
                        <i class="bi bi-c-circle"></i>
                        <span><?= date('Y') ?> PlaySphere</span>
                    </div>
                    <div class="version">
                        <i class="bi bi-code-square"></i>
                        <span>v3.17.3</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="<?= base_url('assets/js/bootstrap.bundle.min.js') ?>"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('loginForm');
            const loginBtn = document.getElementById('loginBtn');
            const btnText = loginBtn.querySelector('.btn-text');
            const btnLoading = loginBtn.querySelector('.btn-loading');

            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const username = document.getElementById('username').value.trim();
                const password = document.getElementById('password').value.trim();

                if (!username || !password) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Peringatan',
                        text: 'Username dan password harus diisi',
                        confirmButtonColor: '#667eea'
                    });
                    return;
                }

                // Show loading state
                loginBtn.classList.add('loading');
                btnText.classList.add('d-none');
                btnLoading.classList.remove('d-none');

                // Send login request
                fetch('<?= base_url('auth/processLogin') ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: `username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Login Berhasil!',
                            text: `Selamat datang, ${data.user.nama}`,
                            timer: 1500,
                            showConfirmButton: false,
                            confirmButtonColor: '#667eea'
                        }).then(() => {
                            window.location.href = data.redirect;
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Login Gagal',
                            text: data.message,
                            confirmButtonColor: '#667eea'
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Terjadi Kesalahan',
                        text: 'Silakan coba lagi',
                        confirmButtonColor: '#667eea'
                    });
                })
                .finally(() => {
                    // Hide loading state
                    loginBtn.classList.remove('loading');
                    btnText.classList.remove('d-none');
                    btnLoading.classList.add('d-none');
                });
            });

            // Enter key support
            document.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !loginBtn.classList.contains('loading')) {
                    loginForm.dispatchEvent(new Event('submit'));
                }
            });
        });

        // Toggle password visibility
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('passwordToggleIcon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'bi bi-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'bi bi-eye';
            }
        }
    </script>
</body>
</html>
