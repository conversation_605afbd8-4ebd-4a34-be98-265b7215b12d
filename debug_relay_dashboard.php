<?php
/**
 * Debug script untuk troubleshooting relay dan dashboard
 * Test koneksi antara dashboard dan Wemos
 */

$baseUrl = 'http://*************/playsphere';
$stationId = 10; // Ganti dengan station ID yang sesuai

echo "=== RELAY & DASHBOARD DEBUG ===\n";
echo "Station ID: $stationId\n";
echo "Base URL: $baseUrl\n";
echo "Testing relay response from dashboard...\n\n";

// Test 1: Check station registration
echo "1. Testing station registration...\n";
testStationRegistration();

// Test 2: Check API response
echo "\n2. Testing API response...\n";
testAPIResponse();

// Test 3: Simulate session start
echo "\n3. Testing session simulation...\n";
testSessionSimulation();

// Test 4: Monitor real-time
echo "\n4. Real-time monitoring (10 cycles)...\n";
monitorRealTime();

function testStationRegistration() {
    global $baseUrl;
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "$baseUrl/api/channel/find");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'X-Forwarded-For: 192.168.1.10',
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "HTTP Code: $httpCode\n";
    
    if ($httpCode == 200) {
        $data = json_decode($response, true);
        if ($data && $data['success']) {
            echo "✅ Station registered successfully\n";
            echo "Channel ID: " . $data['channel_id'] . "\n";
            echo "Station Name: " . $data['data']['channelName'] . "\n";
            echo "PS Type: " . $data['data']['psType'] . "\n";
        } else {
            echo "❌ Registration failed: " . ($data['message'] ?? 'Unknown error') . "\n";
        }
    } else {
        echo "❌ HTTP Error: $httpCode\n";
        echo "Response: $response\n";
    }
}

function testAPIResponse() {
    global $baseUrl, $stationId;
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, "$baseUrl/api/channel/status/$stationId");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "HTTP Code: $httpCode\n";
    
    if ($httpCode == 200) {
        $data = json_decode($response, true);
        if ($data && $data['success']) {
            $sessionData = $data['data'];
            echo "✅ API response successful\n";
            echo "Status: " . $sessionData['status'] . "\n";
            echo "Player: " . $sessionData['playerName'] . "\n";
            echo "Timer: " . $sessionData['timer'] . "\n";
            echo "Price: " . $sessionData['price'] . "\n";
            echo "Relay: " . $sessionData['relay'] . "\n";
            echo "Is Personal: " . ($sessionData['isPersonal'] ? 'true' : 'false') . "\n";
            echo "Is Paused: " . ($sessionData['isPaused'] ? 'true' : 'false') . "\n";
            
            // Check relay status
            if ($sessionData['status'] == 'in_use' && !$sessionData['isPaused']) {
                if ($sessionData['relay'] == 'ON') {
                    echo "✅ Relay should be ON (session active)\n";
                } else {
                    echo "⚠️  Relay is OFF but session is active\n";
                }
            } else {
                if ($sessionData['relay'] == 'OFF') {
                    echo "✅ Relay should be OFF (session not active)\n";
                } else {
                    echo "⚠️  Relay is ON but session is not active\n";
                }
            }
        } else {
            echo "❌ API error: " . ($data['message'] ?? 'Unknown error') . "\n";
        }
    } else {
        echo "❌ HTTP Error: $httpCode\n";
        echo "Response: $response\n";
    }
}

function testSessionSimulation() {
    echo "This would require database access to create test session.\n";
    echo "Manual steps:\n";
    echo "1. Go to dashboard: http://*************/playsphere/konsol\n";
    echo "2. Start a session on Station 2\n";
    echo "3. Check if Wemos relay turns ON\n";
    echo "4. Pause session and check if relay turns OFF\n";
    echo "5. Resume session and check if relay turns ON\n";
    echo "6. Stop session and check if relay turns OFF\n";
}

function monitorRealTime() {
    global $baseUrl, $stationId;
    
    for ($i = 1; $i <= 10; $i++) {
        echo "[$i/10] " . date('H:i:s') . " - ";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "$baseUrl/api/channel/status/$stationId");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode == 200) {
            $data = json_decode($response, true);
            if ($data && $data['success']) {
                $sessionData = $data['data'];
                echo "Status: " . $sessionData['status'] . " | ";
                echo "Relay: " . $sessionData['relay'] . " | ";
                echo "Timer: " . $sessionData['timer'];
                
                if ($sessionData['status'] == 'in_use') {
                    echo " | Player: " . $sessionData['playerName'];
                }
                
                echo "\n";
            } else {
                echo "API Error\n";
            }
        } else {
            echo "HTTP Error: $httpCode\n";
        }
        
        sleep(2);
    }
}

echo "\n=== TROUBLESHOOTING CHECKLIST ===\n";
echo "If relay not working:\n";
echo "1. Check Wemos serial monitor (115200 baud)\n";
echo "2. Look for 'Relay ON/OFF' messages\n";
echo "3. Check pin D1 with multimeter (should be 3.3V when ON)\n";
echo "4. Test relay module with test_relay.ino\n";
echo "5. Verify relay module wiring:\n";
echo "   VCC → 3.3V or 5V\n";
echo "   GND → GND\n";
echo "   IN  → D1 (GPIO 5)\n\n";

echo "If only showing standby:\n";
echo "1. Check database: SELECT * FROM sesi WHERE status = 'berjalan'\n";
echo "2. Check station IP: SELECT ip_address FROM station WHERE id = $stationId\n";
echo "3. Start a session from dashboard\n";
echo "4. Check API response above\n\n";

echo "If colors wrong:\n";
echo "1. Upload test_colors.ino\n";
echo "2. Try different color options (1, 2, 3)\n";
echo "3. Check TFT_eSPI User_Setup.h configuration\n";
echo "4. Try tft.invertDisplay(true) in setup\n\n";

echo "=== DEBUG COMPLETE ===\n";
?>
