<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class UpdateTopupTable extends Migration
{
    public function up()
    {
        // Check if columns exist before adding them
        $db = \Config\Database::connect();
        $fields = $db->getFieldData('topup');
        $existingColumns = array_column($fields, 'name');

        // Add metode_bayar column if not exists
        if (!in_array('metode_bayar', $existingColumns)) {
            $this->forge->addColumn('topup', [
                'metode_bayar' => [
                    'type' => 'VARCHAR',
                    'constraint' => 20,
                    'null' => true,
                    'comment' => 'Metode pembayaran: tunai, qris, debit, transfer'
                ]
            ]);
        }

        // Add tanggal_topup column if not exists
        if (!in_array('tanggal_topup', $existingColumns)) {
            $this->forge->addColumn('topup', [
                'tanggal_topup' => [
                    'type' => 'DATETIME',
                    'null' => true,
                    'comment' => 'Tanggal dan waktu top-up dilakukan'
                ]
            ]);
        }
    }

    public function down()
    {
        // Remove added columns
        $this->forge->dropColumn('topup', ['metode_bayar', 'tanggal_topup']);
    }
}
