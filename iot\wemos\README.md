# PlaySphere IoT Device - Wemos D1 Mini Configuration

## Hardware Configuration

### Wemos D1 Mini dengan LCD ST7789 TFT 240x240

**Pin Configuration:**
- SCL: D5 (GPIO 14) 
- SDA: D7 (GPIO 13) 
- RES: D4 (GPIO 2) 
- DC: D3 (GPIO 0) 
- BLK: D8 (GPIO 15)
- Relay: D1 (GPIO 5)

**Display Settings:**
- Rotation: 2
- Resolution: 240x240 pixels
- Driver: ST7789

## Software Configuration

### WiFi Settings
- SSID: PlaySphere
- Password: 1234554321

### Server Settings
- IP Server: *************
- Port: 80

### API Endpoints
- Channel Registration: `/api/channel/find`
- Status Update: `/api/channel/status/{channel_id}`

## Installation

1. Install Arduino IDE
2. Install ESP8266 Board Package
3. Install required libraries:
   - ESP8266WiFi
   - ESP8266HTTPClient
   - ArduinoJson
   - TFT_eSPI

4. Configure TFT_eSPI library:
   - Copy `User_Setup.h` to TFT_eSPI library folder
   - Or modify existing User_Setup.h with the pin configuration above

5. Upload the code to Wemos D1 Mini

## Features

### Display Modes

1. **Standby Mode (Gray Card)**
   - Shows station name and PS type
   - Timer shows 00:00:00
   - "PlaySphere Ready" status
   - Play button

2. **Active Mode (Green Card)**
   - Shows player name and timer
   - Real-time price calculation
   - Control buttons (Stop, Pause, Settings)
   - Status indicator

3. **Paused Mode (Blue Card)**
   - Shows "PAUSED" status
   - Player name and current price
   - Control buttons (Stop, Play, Settings)

4. **Time Up Mode (Red Card)**
   - Flashing "WAKTU HABIS" message
   - Final price display
   - OK button

### Relay Control
- Relay ON: When timer is running (not paused)
- Relay OFF: When timer is stopped or paused

### Status Indicators
- WiFi icon shows connection status
- Color-coded status indicators:
  - Green: SAIPUL (Active)
  - Yellow: DAMAR (Paused)

## Troubleshooting

### Error "IP tidak terdaftar"

**Penyebab:** IP address Wemos tidak terdaftar di database station.

**Solusi:**
1. **Update IP di database:**
   ```sql
   UPDATE station
   SET ip_address = '************'
   WHERE nama_station = 'Station 2';
   ```

2. **Verifikasi IP Wemos:**
   - Cek serial monitor untuk melihat IP yang didapat Wemos
   - Pastikan IP sesuai dengan yang di database

3. **Test API endpoint:**
   - Jalankan `test_api.php` untuk test koneksi
   - URL: `http://*************/playsphere/api/channel/find`

### Common Issues

1. **Display not working:**
   - Check pin connections sesuai User_Setup.h
   - Verify TFT_eSPI configuration
   - Ensure rotation = 2

2. **WiFi connection failed:**
   - SSID: PlaySphere
   - Password: 1234554321
   - Check serial monitor for error messages

3. **Server connection failed:**
   - Base URL: `http://*************/playsphere/`
   - Check API routes di Routes.php
   - Ensure station IP registered in database

4. **Relay not working:**
   - Pin D1 (GPIO 5)
   - Check relay module compatibility
   - Test with multimeter

### Serial Monitor Output
Enable serial monitor at 115200 baud for debugging information.

### Database Structure
Aplikasi menggunakan tabel `station`, bukan `channels`:
```sql
SELECT s.id, s.nama_station, s.ip_address, k.nama_konsol
FROM station s
JOIN konsol k ON k.id = s.id_konsol;
```

## API Integration

The device automatically registers with the PlaySphere server using its IP address. Make sure to add the device IP to the channels table in the database with appropriate PS type configuration.

### Database Requirements
- channels table with ip_address field
- ps_types table for console type configuration
- session_data field for real-time status updates

## Customization

### Colors
Modify color definitions in the code to match your branding:
- CARD_GREEN: Active station color
- CARD_BLUE: Paused station color  
- CARD_GRAY: Standby station color
- STATUS_RED: Stop button color
- STATUS_BLUE: Action button color

### Display Layout
Adjust button positions and text sizes in the show*Screen() functions to customize the interface layout.

### Update Intervals
- Status update: 1 second (configurable)
- Server retry: 5 seconds (configurable)
- Debug output: 30 seconds (configurable)
