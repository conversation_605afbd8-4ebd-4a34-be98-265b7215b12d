<?php namespace App\Controllers;

use App\Models\ChannelModel;
use App\Models\PsTypeModel;

class Channel extends BaseController
{
    protected $channelModel;
    protected $psTypeModel;

    public function __construct()
    {
        $this->channelModel = new ChannelModel();
        $this->psTypeModel = new PsTypeModel();
    }

    public function index()
    {
        $data = [
            'title' => 'Channel Management',
            'channels' => $this->channelModel->getChannels(),
            'ps_types' => $this->psTypeModel->findAll()
        ];
        return view('channel/index', $data);
    }
}