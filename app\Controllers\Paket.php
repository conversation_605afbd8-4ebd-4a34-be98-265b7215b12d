<?php
/**
 * Paket Controller
 * Controller untuk manajemen paket rental gaming
 * Menangani CRUD paket dengan durasi dan harga
 *
 * <AUTHOR> Team
 * @version 3.0
 */

namespace App\Controllers;

use App\Models\PaketModel;
use App\Models\KonsolModel;
use App\Controllers\BaseController;

class Paket extends BaseController
{
    /**
     * Halaman utama paket
     * Menampilkan daftar semua paket dengan informasi konsol
     *
     * @return string View paket index dengan data paket dan konsol
     */
    public function index()
    {
        // Inisialisasi model
        $paketModel = new PaketModel();
        $konsolModel = new KonsolModel();

        // Siapkan data untuk view
        $data = [
            'paket' => $paketModel->getWithKonsol(), // Paket dengan info konsol
            'konsol' => $konsolModel->findAll()      // Daftar konsol untuk dropdown
        ];

        return view('paket/index', $data);
    }

    public function simpan()
    {
        $paketModel = new PaketModel();
        $durasi = $this->request->getPost('durasi');

        $paketModel->insert([
            'nama_paket' => $this->request->getPost('nama_paket'),
            'id_konsol' => $this->request->getPost('id_konsol'),
            'durasi' => $durasi,
            'harga' => $this->request->getPost('harga'),
            'keterangan' => $this->request->getPost('keterangan'),
        ]);

        return $this->response->setStatusCode(200);
    }

    public function update($id)
    {
        $paketModel = new PaketModel();
        $durasi = $this->request->getPost('durasi');

        $paketModel->update($id, [
            'nama_paket' => $this->request->getPost('nama_paket'),
            'id_konsol' => $this->request->getPost('id_konsol'),
            'durasi' => $durasi,
            'harga' => $this->request->getPost('harga'),
            'keterangan' => $this->request->getPost('keterangan'),
        ]);

        return $this->response->setStatusCode(200);
    }

    public function hapus($id)
    {
        $paketModel = new PaketModel();
        $paketModel->delete($id);

        return redirect()->to(base_url('paket'));
    }

        public function list()
    {
        $model = new PaketModel();
        $paket = $model->findAll();

        return $this->response->setJSON($paket);
    }
}
