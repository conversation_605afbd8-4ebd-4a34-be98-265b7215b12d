/**
 * Konsol CSS
 * Styling untuk halaman manajemen konsol gaming dan station
 * Termasuk card konsol, station, dan tombol aksi
 * 
 * <AUTHOR>
 * @version 3.0
 */

/* ===== CSS VARIABLES ===== */
:root {
  /* Gradient untuk card konsol */
  --konsol-gradient: linear-gradient(135deg, #5c6bc0, #8e99f3);
  /* Gradient untuk card station */
  --station-gradient: linear-gradient(135deg, #00acc1, #26c6da);
  /* Warna teks terang */
  --text-light: #ffffff;
  /* Shadow untuk card */
  --card-shadow: 0 4px 14px rgba(0, 0, 0, 0.1);
  /* Warna tombol edit */
  --btn-edit: #fbc02d;
  /* Warna tombol hapus */
  --btn-hapus: #e53935;
}

/* ===== CARD STYLING ===== */
.card-konsol, .card-station {
  border-radius: 1rem;
  box-shadow: var(--card-shadow);
  color: var(--text-light);
  padding: 1rem 1.2rem;
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* Efek hover untuk card */
.card-konsol:hover, .card-station:hover {
  transform: translateY(-6px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* Styling khusus card konsol */
.card-konsol {
  background: var(--konsol-gradient);
  min-height: 100px;
}

/* Styling khusus card station */
.card-station {
  background: var(--station-gradient);
  text-align: center;
}

/* Icon signal di pojok kanan atas */
.signal-icon {
  position: absolute;
  top: 0.75rem;
  right: 0.9rem;
  font-size: 1.2rem;
  opacity: 0.9;
}

/* Styling judul card */
.card-title {
  font-weight: 600;
  font-size: 1.1rem;
  margin-bottom: 0.4rem;
  text-align: center;
}

/* Styling informasi harga */
.price-info {
  font-size: 0.85rem;
  text-align: center;
  margin-bottom: 0.25rem;
  white-space: nowrap;
}

/* Styling subtitle card */
.card-subtext {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.85);
  margin-bottom: 0.35rem;
}

/* ===== BUTTON STYLING ===== */
/* Container grup tombol */
.btn-icon-group {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 0.4rem;
}

/* Styling tombol icon */
.btn-icon {
  border: none;
  padding: 6px;
  width: 30px;
  height: 30px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
  color: #fff;
}

/* Tombol edit */
.btn-icon.edit {
  background-color: var(--btn-edit);
}

/* Tombol hapus */
.btn-icon.hapus {
  background-color: var(--btn-hapus);
}

/* Efek hover tombol */
.btn-icon:hover {
  opacity: 0.85;
}
