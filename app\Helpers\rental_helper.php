<?php

if (!function_exists('getRentalSettings')) {
    /**
     * Get rental settings from config file
     * 
     * @return array
     */
    function getRentalSettings()
    {
        $configPath = WRITEPATH . 'rental_config.json';

        log_message('info', 'getRentalSettings helper called. Config path: ' . $configPath);

        if (file_exists($configPath)) {
            $configContent = file_get_contents($configPath);
            $config = json_decode($configContent, true);
            if ($config) {
                log_message('info', 'Helper loaded config: ' . json_encode($config));
                return $config;
            }
            log_message('warning', 'Helper failed to decode config J<PERSON><PERSON>');
        } else {
            log_message('info', 'Helper: Config file does not exist');
        }

        // Return default settings if file doesn't exist or is invalid
        $defaultSettings = [
            'nama_rental' => 'PlaySphere Gaming Center',
            'slogan_rental' => 'Gaming Center & Internet Cafe',
            'whatsapp_rental' => '08123456789',
            'alamat_rental' => 'Jl. Gaming Center No. 123, Kota Gaming, 12345',
            'logo_rental' => 'assets/images/default-logo.svg'
        ];

        log_message('info', 'Helper returning default settings');
        return $defaultSettings;
    }
}

if (!function_exists('getRentalName')) {
    /**
     * Get rental name
     * 
     * @return string
     */
    function getRentalName()
    {
        $settings = getRentalSettings();
        return $settings['nama_rental'] ?? 'PlaySphere Gaming Center';
    }
}

if (!function_exists('getRentalLogo')) {
    /**
     * Get rental logo path
     * 
     * @return string
     */
    function getRentalLogo()
    {
        $settings = getRentalSettings();
        return $settings['logo_rental'] ?? 'assets/images/default-logo.svg';
    }
}

if (!function_exists('getRentalAddress')) {
    /**
     * Get rental address
     * 
     * @return string
     */
    function getRentalAddress()
    {
        $settings = getRentalSettings();
        return $settings['alamat_rental'] ?? 'Jl. Gaming Center No. 123, Kota Gaming, 12345';
    }
}

if (!function_exists('getRentalSlogan')) {
    /**
     * Get rental slogan
     *
     * @return string
     */
    function getRentalSlogan()
    {
        $settings = getRentalSettings();
        return $settings['slogan_rental'] ?? 'Gaming Center & Internet Cafe';
    }
}

if (!function_exists('getRentalWhatsApp')) {
    /**
     * Get rental WhatsApp number
     *
     * @return string
     */
    function getRentalWhatsApp()
    {
        $settings = getRentalSettings();
        return $settings['whatsapp_rental'] ?? '08123456789';
    }
}
