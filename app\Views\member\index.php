<?php
/**
 * Member Index View
 * Halaman manajemen data member
 * Menampilkan daftar member, saldo, dan fungsi top-up
 *
 * <AUTHOR> Team
 * @version 3.0
 */
?>
<?= $this->extend('layouts/main') ?>
<?= $this->section('content') ?>

<!-- CSS dan JS Dependencies -->
<link href="<?= base_url('assets/sweetalert2/sweetalert2.min.css') ?>" rel="stylesheet">
<script src="<?= base_url('assets/sweetalert2/sweetalert2.all.min.js') ?>"></script>
<link rel="stylesheet" href="<?= base_url('assets/css/bootstrap-icons.min.css') ?>">
<link rel="stylesheet" href="<?= base_url('assets/css/member.css') ?>">

<div class="container-fluid">
  <!-- Header dengan judul dan tombol tambah -->
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h4>Data Member</h4>
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalTambahMember">
      <i class="bi bi-person-plus me-1"></i>Tambah Member
    </button>
  </div>

  <!-- Search box untuk mencari member -->
  <div class="mb-3" style="max-width: 300px;">
    <input type="text" id="searchMember" class="form-control" placeholder="Cari ID atau Nama">
  </div>

  <!-- Grid responsive untuk menampilkan card member -->
  <div class="row g-3" id="memberList">
    <?php foreach ($member as $m): ?>
      <!-- Card individual untuk setiap member -->
      <div class="col-xxl-1-5 col-xl-2 col-lg-3 col-md-4 col-sm-6 col-6">
        <div class="card-member">
          <!-- Konten utama card -->
          <div class="card-content">
            <!-- ID Member -->
            <div class="title"><?= esc($m['id_member']) ?></div>

            <!-- Nama member dengan ikon -->
            <div class="info">
              <i class="bi bi-person-fill me-1"></i><?= esc($m['nama']) ?>
            </div>

            <!-- Nomor WhatsApp dengan ikon -->
            <div class="info">
              <i class="bi bi-whatsapp me-1"></i><?= esc($m['no_wa']) ?>
            </div>

            <!-- Saldo member dengan ikon -->
            <div class="info">
              <i class="bi bi-wallet2 me-1"></i>Rp <?= number_format($m['saldo'], 0, ',', '.') ?>
            </div>
          </div>
          
          <!-- Footer dengan tombol aksi -->
          <div class="btn-footer">
            <!-- Grup tombol untuk riwayat -->
            <div class="history-buttons">
              <!-- Tombol Riwayat Top Up -->
              <button class="btn btn-sm btn-outline-light" title="Riwayat TopUp"
                      data-bs-toggle="modal" data-bs-target="#modalRiwayat<?= $m['id'] ?>">
                <i class="bi bi-receipt"></i>
              </button>

              <!-- Tombol Riwayat Penggunaan -->
              <button class="btn btn-sm btn-outline-light" title="Riwayat Penggunaan"
                      data-bs-toggle="modal" data-bs-target="#modalRiwayatPenggunaan<?= $m['id'] ?>">
                <i class="bi bi-graph-up"></i>
              </button>
            </div>

            <!-- Grup tombol untuk edit dan hapus -->
            <div class="action-buttons">
              <!-- Tombol Edit Member -->
              <button class="btn btn-sm btn-warning" title="Edit"
                      data-bs-toggle="modal" data-bs-target="#modalEdit<?= $m['id'] ?>">
                <i class="bi bi-pencil"></i>
              </button>

              <!-- Tombol Hapus Member -->
              <button class="btn btn-sm btn-danger btn-hapus" title="Hapus"
                      data-id="<?= $m['id'] ?>">
                <i class="bi bi-trash"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    <?php endforeach; ?>
  </div>
</div>

<!-- Modal Tambah Member -->
<div class="modal fade" id="modalTambahMember" tabindex="-1" aria-labelledby="modalTambahMemberLabel" aria-hidden="true">
  <div class="modal-dialog">
    <form action="<?= base_url('member/simpan') ?>" method="post" class="modal-content ajax-form">
      <div class="modal-header">
        <h5 class="modal-title" id="modalTambahMemberLabel">Tambah Member</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="mb-3">
          <label class="form-label">Nama Member</label>
          <input type="text" name="nama" class="form-control" required>
        </div>
        <div class="mb-3">
          <label class="form-label">No. WhatsApp</label>
          <input type="text" name="no_wa" class="form-control" required>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
        <button type="submit" class="btn btn-primary">Simpan</button>
      </div>
    </form>
  </div>
</div>

<!-- Modal untuk setiap member -->
<?php foreach ($member as $m): ?>
  
  <!-- Modal Riwayat TopUp -->
  <div class="modal fade" id="modalRiwayat<?= $m['id'] ?>" tabindex="-1" aria-labelledby="riwayatLabel<?= $m['id'] ?>" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="riwayatLabel<?= $m['id'] ?>">Riwayat TopUp - <?= esc($m['nama']) ?></h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body p-3">
          <?php if (!empty($m['riwayat'])): ?>
            <ul class="list-group">
              <?php foreach ($m['riwayat'] as $r): ?>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                  <div><i class="bi bi-clock me-2"></i><?= date('d/m/Y H:i', strtotime($r['created_at'])) ?></div>
                  <strong class="text-success">+Rp <?= number_format($r['jumlah'], 0, ',', '.') ?></strong>
                </li>
              <?php endforeach; ?>
              <p class="mt-3 fw-bold text-center text-success">
  Total TopUp: Rp <?= number_format($m['total_topup'], 0, ',', '.') ?>
</p>

            </ul>
            <a href="<?= base_url('member/cetakRiwayat/' . $m['id']) ?>" class="btn btn-success btn-sm mt-3" target="_blank">
  ⬇️ Download Riwayat
</a>
          <?php else: ?>
            <div class="alert alert-info text-center mb-0">Belum ada riwayat top up.</div>
          <?php endif; ?>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal Riwayat Penggunaan Saldo -->
  <div class="modal fade" id="modalRiwayatPenggunaan<?= $m['id'] ?>" tabindex="-1" aria-labelledby="riwayatPenggunaanLabel<?= $m['id'] ?>" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-scrollable">
      <div class="modal-content">
        <div class="modal-header bg-primary text-white">
          <h5 class="modal-title" id="riwayatPenggunaanLabel<?= $m['id'] ?>">
            <i class="bi bi-graph-up me-2"></i>Riwayat Penggunaan Saldo - <?= esc($m['nama']) ?>
          </h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body p-3">
          <?php if (!empty($m['riwayat_penggunaan'])): ?>
            <div class="row mb-3">
              <div class="col-md-6">
                <div class="card bg-light">
                  <div class="card-body text-center">
                    <h6 class="card-title text-muted">Total Penggunaan</h6>
                    <h4 class="text-danger">Rp <?= number_format($m['total_penggunaan'], 0, ',', '.') ?></h4>
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="card bg-light">
                  <div class="card-body text-center">
                    <h6 class="card-title text-muted">Total Transaksi</h6>
                    <h4 class="text-info"><?= count($m['riwayat_penggunaan']) ?> Sesi</h4>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="table-responsive">
              <table class="table table-hover">
                <thead class="table-dark">
                  <tr>
                    <th>Tanggal</th>
                    <th>Station</th>
                    <th>Paket</th>
                    <th>Durasi</th>
                    <th>Biaya</th>
                    <th>Status</th>
                  </tr>
                </thead>
                <tbody>
                  <?php foreach ($m['riwayat_penggunaan'] as $p): ?>
                    <tr>
                      <td>
                        <small class="text-muted">
                          <i class="bi bi-calendar me-1"></i><?= date('d/m/Y', strtotime($p['created_at'])) ?><br>
                          <i class="bi bi-clock me-1"></i><?= date('H:i', strtotime($p['created_at'])) ?>
                        </small>
                      </td>
                      <td>
                        <strong><?= esc($p['nama_station']) ?></strong><br>
                        <small class="text-muted"><?= esc($p['nama_konsol']) ?></small>
                      </td>
                      <td>
                        <?php if ($p['nama_paket']): ?>
                          <span class="badge bg-success"><?= esc($p['nama_paket']) ?></span>
                        <?php else: ?>
                          <span class="badge bg-secondary">Non-Paket</span>
                        <?php endif; ?>
                      </td>
                      <td>
                        <?php 
                        if ($p['waktu_berhenti']) {
                          $mulai = strtotime($p['waktu_mulai']);
                          $berhenti = strtotime($p['waktu_berhenti']);
                          $durasiDetik = $berhenti - $mulai;
                          $jam = floor($durasiDetik / 3600);
                          $menit = floor(($durasiDetik % 3600) / 60);
                          echo sprintf('%02d:%02d', $jam, $menit);
                        } else {
                          echo '<span class="text-warning">Berjalan</span>';
                        }
                        ?>
                      </td>
                      <td>
                        <?php 
                        if ($p['nama_paket']) {
                          // Jika paket, tampilkan harga paket
                          $biaya = $p['harga_paket'];
                        } else {
                          // Jika non-paket, hitung biaya berdasarkan durasi dan tarif per menit
                          if ($p['waktu_berhenti']) {
                            $mulai = strtotime($p['waktu_mulai']);
                            $berhenti = strtotime($p['waktu_berhenti']);
                            $durasiDetik = $berhenti - $mulai;
                            $durasiMenit = ceil($durasiDetik / 60); // Pembulatan ke atas per menit
                            $hargaPerJam = $p['harga_member'] ?? 0;
                            $hargaPerMenit = round($hargaPerJam / 60);
                            $biaya = $durasiMenit * $hargaPerMenit;
                          } else {
                            // Jika masih berjalan, gunakan harga_total sementara
                            $biaya = $p['harga_total'];
                          }
                        }
                        ?>
                        <strong class="text-danger">-Rp <?= number_format($biaya, 0, ',', '.') ?></strong>
                      </td>
                      <td>
                        <?php if ($p['status'] === 'berjalan'): ?>
                          <span class="badge bg-warning">Berjalan</span>
                        <?php else: ?>
                          <span class="badge bg-success">Selesai</span>
                        <?php endif; ?>
                      </td>
                    </tr>
                  <?php endforeach; ?>
                </tbody>
              </table>
            </div>
            
            <div class="text-center mt-3">
              <a href="<?= base_url('member/cetakRiwayatPenggunaan/' . $m['id']) ?>" class="btn btn-primary btn-sm" target="_blank">
                <i class="bi bi-download me-1"></i>Download Riwayat Penggunaan
              </a>
            </div>
          <?php else: ?>
            <div class="alert alert-info text-center mb-0">
              <i class="bi bi-info-circle me-2"></i>Belum ada riwayat penggunaan saldo.
            </div>
          <?php endif; ?>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal Edit Member -->
  <div class="modal fade" id="modalEdit<?= $m['id'] ?>" tabindex="-1" aria-labelledby="editLabel<?= $m['id'] ?>" aria-hidden="true">
    <div class="modal-dialog">
      <form action="<?= base_url('member/update/' . $m['id']) ?>" method="post" class="modal-content ajax-form">
        <div class="modal-header">
          <h5 class="modal-title" id="editLabel<?= $m['id'] ?>">Edit Member</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="mb-3">
            <label class="form-label">Nama Member</label>
            <input type="text" name="nama" value="<?= esc($m['nama']) ?>" class="form-control" required>
          </div>
          <div class="mb-3">
            <label class="form-label">No. WhatsApp</label>
            <input type="text" name="no_wa" value="<?= esc($m['no_wa']) ?>" class="form-control" required>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
          <button type="submit" class="btn btn-primary">Simpan</button>
        </div>
      </form>
    </div>
  </div>

  <!-- Modal Topup Member -->
  <div class="modal fade" id="modalTopup<?= $m['id'] ?>" tabindex="-1" aria-labelledby="topupLabel<?= $m['id'] ?>" aria-hidden="true">
    <div class="modal-dialog">
      <form action="<?= base_url('member/topup/' . $m['id']) ?>" method="post" class="modal-content ajax-form">
        <div class="modal-header">
          <h5 class="modal-title" id="topupLabel<?= $m['id'] ?>">Top Up Saldo - <?= esc($m['nama']) ?></h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="mb-3">
            <label class="form-label">Nominal Top Up</label>
           <!-- <input type="number" name="jumlah" class="form-control" min="1000" step="1000" required> -->
            <input type="number" name="jumlah" class="form-control" required>

          </div>
          <div class="d-flex flex-wrap gap-2 topup-quick">
            <?php foreach ([5000,10000,20000,50000,100000] as $nom): ?>
              <button type="button" class="btn btn-outline-secondary btn-sm quick-amount" data-amount="<?= $nom ?>">
                Rp <?= number_format($nom, 0, ',', '.') ?>
              </button>
            <?php endforeach; ?>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
          <button type="submit" class="btn btn-success">Top Up</button>
        </div>
      </form>
    </div>
  </div>

<?php endforeach; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {

  // Handle quick amount buttons
  document.querySelectorAll('.quick-amount').forEach(button => {
    button.addEventListener('click', function() {
      const amount = this.dataset.amount;
      const form = this.closest('form');
      const input = form.querySelector('input[name="jumlah"]');
      input.value = amount;
    });
  });

  // Handle AJAX forms
  const forms = document.querySelectorAll('form.ajax-form');
  forms.forEach(form => {
    form.addEventListener('submit', async function(e) {
      e.preventDefault();

      const submitBtn = this.querySelector('button[type="submit"]');
      const originalText = submitBtn.textContent;
      submitBtn.disabled = true;
      submitBtn.textContent = 'Mengirim...';

      const formData = new FormData(this);

      try {
        const response = await fetch(this.action, {
          method: 'POST',
          body: formData
        });

        if (response.ok) {
          const modal = bootstrap.Modal.getInstance(this.closest('.modal'));
          if (modal) modal.hide();

          await Swal.fire({
            icon: 'success',
            title: 'Berhasil!',
            text: 'Data berhasil disimpan.',
            timer: 1500,
            showConfirmButton: false
          });

          location.reload();
        } else {
          const error = await response.json();
          Swal.fire({
            icon: 'error',
            title: 'Gagal!',
            text: error.message || 'Terjadi kesalahan saat menyimpan data.'
          });
        }

      } catch (error) {
        console.error('Error:', error);
        Swal.fire({
          icon: 'error',
          title: 'Error!',
          text: 'Terjadi kesalahan: ' + error.message
        });
      } finally {
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
      }
    });
  });

  // Handle delete buttons
  document.querySelectorAll('.btn-hapus').forEach(button => {
    button.addEventListener('click', function() {
      const id = this.dataset.id;
      Swal.fire({
        title: 'Yakin hapus?',
        text: 'Data akan dihapus permanen dan tidak bisa dikembalikan.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, hapus!',
        cancelButtonText: 'Batal'
      }).then((result) => {
        if (result.isConfirmed) {
          window.location.href = `<?= base_url('member/hapus/') ?>${id}`;
        }
      });
    });
  });

  // Handle search functionality
  const searchInput = document.getElementById('searchMember');
  if (searchInput) {
    searchInput.addEventListener('input', function() {
      const keyword = this.value.toLowerCase().trim();
      const memberCards = document.querySelectorAll('#memberList .member-card');

      memberCards.forEach(card => {
        const titleText = card.querySelector('.title')?.textContent.toLowerCase() || '';
        const nameText = card.querySelector('.info')?.textContent.toLowerCase() || '';
        const isMatch = titleText.includes(keyword) || nameText.includes(keyword);
        card.style.display = isMatch ? '' : 'none';
      });
    });
  }

});
</script>

<script src="<?= base_url('assets/js/member.js') ?>"></script>

<?= $this->endSection() ?>