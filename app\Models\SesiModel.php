<?php
namespace App\Models;
use CodeIgniter\Model;

class SesiModel extends Model
{
    protected $table = 'sesi';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'station_id', 'jenis_user', 'waktu_mulai', 'waktu_berhenti',
        'harga_total', 'status', 'id_member', 'id_paket', 'durasi_sisa', 'sisa_saldo',
        'metode_bayar', 'jumlah_bayar', 'waktu_bayar', 'status_bayar'
    ];
    protected $useTimestamps = true;

    /**
     * getRunningSessionsWithDetails
     * Enhanced version with proper duration calculation
     * Updated: 2025-01-XX XX:XX:XX
     */
    public function getRunningSessionsWithDetails()
    {
        $results = $this->select('sesi.*, member.nama as nama_member, paket.nama_paket, paket.durasi as durasi_paket, konsol.nama_konsol, station.nama_station, konsol.harga_personal, konsol.harga_member')
                        ->join('member', 'member.id = sesi.id_member', 'left')
                        ->join('paket', 'paket.id = sesi.id_paket', 'left')
                        ->join('station', 'station.id = sesi.station_id')
                        ->join('konsol', 'konsol.id = station.id_konsol')
                        ->where('sesi.status', 'berjalan')
                        ->findAll();

        foreach ($results as &$sesi) {
            if ($sesi['jenis_user'] === 'member') {
                if (!empty($sesi['id_paket'])) {
                    // For package sessions, use stored durasi_sisa which includes any added packages
                    // Calculate elapsed time since session started
                    $waktuMulai = strtotime($sesi['waktu_mulai']);
                    $now = time();
                    $elapsedSeconds = $now - $waktuMulai;

                    // Get stored durasi_sisa (which may include added packages)
                    $storedDurasiSisa = intval($sesi['durasi_sisa'] ?? 0);

                    // Calculate remaining duration based on stored value
                    $adjustedDurasiSisa = max(0, $storedDurasiSisa - $elapsedSeconds);
                    $sesi['durasi_sisa'] = $adjustedDurasiSisa;

                    // Log for debugging
                    log_message('debug', "SesiModel - Package Session {$sesi['id']} - Stored: {$storedDurasiSisa}s, Elapsed: {$elapsedSeconds}s, Remaining: {$adjustedDurasiSisa}s");
                } else {
                    // For non-package member sessions, use stored durasi_sisa as primary source
                    $waktuMulai = strtotime($sesi['waktu_mulai']);
                    $now = time();
                    $elapsedSeconds = $now - $waktuMulai;

                    // Get stored durasi_sisa (which was calculated from saldo at session start)
                    $storedDurasiSisa = intval($sesi['durasi_sisa'] ?? 0);

                    // Calculate remaining duration based on stored value
                    $adjustedDurasiSisa = max(0, $storedDurasiSisa - $elapsedSeconds);
                    $sesi['durasi_sisa'] = $adjustedDurasiSisa;

                    // Calculate current saldo based on remaining time
                    $hargaPerMenit = round($sesi['harga_member'] / 60);
                    $remainingMinutes = ceil($adjustedDurasiSisa / 60); // Round up to ensure saldo covers the time
                    $currentSaldo = $remainingMinutes * $hargaPerMenit;
                    $sesi['member_saldo'] = $currentSaldo;

                    // Log for debugging
                    log_message('debug', "SesiModel - Non-Package Session {$sesi['id']} - Stored duration: {$storedDurasiSisa}s, Elapsed: {$elapsedSeconds}s, Remaining: {$adjustedDurasiSisa}s, Current saldo: {$currentSaldo}");
                }
            } else {
                $sesi['durasi_sisa'] = $sesi['durasi_sisa'] ?? null;
            }
        }

        return $results;
    }
}
