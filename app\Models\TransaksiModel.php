<?php
/**
 * Transaksi Model
 * Model untuk mengelola data transaksi penjualan di kasir
 * Menangani operasi database untuk transaksi dan detail transaksi
 *
 * <AUTHOR> Team
 * @version 3.0
 */

namespace App\Models;

use CodeIgniter\Model;

class TransaksiModel extends Model
{
    // Konfigurasi tabel dan field
    protected $table = 'transaksi';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'no_transaksi', 'jenis_transaksi', 'total_harga', 'jumlah_bayar',
        'kembalian', 'metode_bayar', 'kasir', 'keterangan', 'status'
    ];
    protected $useTimestamps = true;
    
    /**
     * Generate nomor transaksi otomatis
     * Format: TRX-YYYYMMDD-XXXX
     * 
     * @return string
     */
    public function generateNoTransaksi()
    {
        $tanggal = date('Ymd');
        $prefix = 'TRX-' . $tanggal . '-';
        
        // Cari transaksi terakhir hari ini
        $lastTransaction = $this->like('no_transaksi', $prefix)
                               ->orderBy('id', 'DESC')
                               ->first();
        
        if ($lastTransaction) {
            // Ambil nomor urut terakhir
            $lastNumber = intval(substr($lastTransaction['no_transaksi'], -4));
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }
        
        return $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }
    
    /**
     * Simpan transaksi dengan detail
     * 
     * @param array $transaksiData
     * @param array $detailItems
     * @return int|false ID transaksi atau false jika gagal
     */
    public function simpanTransaksi($transaksiData, $detailItems = [])
    {
        $db = \Config\Database::connect();
        $db->transStart();
        
        try {
            // Generate nomor transaksi
            $transaksiData['no_transaksi'] = $this->generateNoTransaksi();
            
            // Simpan transaksi utama
            $transaksiId = $this->insert($transaksiData);
            
            if ($transaksiId && !empty($detailItems)) {
                $detailModel = new TransaksiDetailModel();
                $produkModel = new ProdukModel();
                
                foreach ($detailItems as $item) {
                    // Simpan detail transaksi
                    $detailModel->insert([
                        'transaksi_id' => $transaksiId,
                        'produk_id' => $item['produk_id'],
                        'nama_produk' => $item['nama_produk'],
                        'harga_satuan' => $item['harga_satuan'],
                        'jumlah' => $item['jumlah'],
                        'subtotal' => $item['subtotal']
                    ]);
                    
                    // Update stok produk
                    $produkModel->updateStok($item['produk_id'], -$item['jumlah']);
                }
            }
            
            $db->transComplete();
            
            if ($db->transStatus() === false) {
                return false;
            }
            
            return $transaksiId;
            
        } catch (\Exception $e) {
            $db->transRollback();
            log_message('error', 'Error saving transaction: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get transaksi dengan detail
     * 
     * @param int $transaksiId
     * @return array|null
     */
    public function getTransaksiWithDetail($transaksiId)
    {
        $transaksi = $this->find($transaksiId);
        if (!$transaksi) {
            return null;
        }
        
        $detailModel = new TransaksiDetailModel();
        $transaksi['detail'] = $detailModel->where('transaksi_id', $transaksiId)->findAll();
        
        return $transaksi;
    }
    
    /**
     * Get laporan transaksi harian
     * 
     * @param string $tanggal Format Y-m-d
     * @return array
     */
    public function getLaporanHarian($tanggal = null)
    {
        if (!$tanggal) {
            $tanggal = date('Y-m-d');
        }
        
        return $this->where('DATE(created_at)', $tanggal)
                   ->where('status', 'selesai')
                   ->orderBy('created_at', 'DESC')
                   ->findAll();
    }
}
