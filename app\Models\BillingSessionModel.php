<?php namespace App\Models;

use CodeIgniter\Model;

class BillingSessionModel extends Model
{
    protected $table = 'billing_sessions';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'channel_id', 
        'member_id', 
        'package_id', 
        'player_name',
        'start_time',
        'end_time',
        'pause_time',
        'total_pause_duration',
        'status',
        'total_price'
    ];
    protected $useTimestamps = true;
    
    // Relasi
    public function channel()
    {
        return $this->belongsTo('App\Models\ChannelModel', 'channel_id', 'id');
    }
    
    public function member()
    {
        return $this->belongsTo('App\Models\MemberModel', 'member_id', 'id');
    }
    
    public function package()
    {
        return $this->belongsTo('App\Models\PackageModel', 'package_id', 'id');
    }
    
    // Method untuk mendapatkan semua sesi aktif
    public function getActiveSessions()
    {
        return $this->select('billing_sessions.*, channels.number as channel_number, packages.name as package_name')
                    ->join('channels', 'channels.id = billing_sessions.channel_id')
                    ->join('packages', 'packages.id = billing_sessions.package_id')
                    ->where('billing_sessions.status', 'active')
                    ->findAll();
    }
    
    // Method untuk menghitung durasi sesi
    public function calculateSessionDuration($sessionId)
    {
        $session = $this->find($sessionId);
        if (!$session) return 0;
        
        $start = strtotime($session['start_time']);
        $end = $session['end_time'] ? strtotime($session['end_time']) : time();
        $totalPause = $session['total_pause_duration'];
        
        return ($end - $start) - $totalPause;
    }
    
    // Method untuk menghitung harga final
    public function calculateFinalPrice($sessionId)
    {
        $session = $this->find($sessionId);
        if (!$session) return 0;
        
        $package = model('PackageModel')->find($session['package_id']);
        $duration = $this->calculateSessionDuration($sessionId);
        $basePrice = $package['price'];
        
        // Jika durasi melebihi paket, hitung tambahan
        $packageDuration = $package['duration'] * 60; // konversi ke detik
        if ($duration > $packageDuration) {
            $extraTime = ceil(($duration - $packageDuration) / 3600); // pembulatan ke atas per jam
            $psType = model('ChannelModel')->find($session['channel_id'])->psType;
            $extraCharge = $extraTime * $psType['price_per_hour'];
            return $basePrice + $extraCharge;
        }
        
        return $basePrice;
    }
}