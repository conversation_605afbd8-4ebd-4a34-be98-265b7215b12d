<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card shadow-sm">
        <div class="card-header bg-gradient-primary text-white">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h5 class="card-title mb-0">
                <i class="bi bi-people-fill me-2"></i>Manajemen Operator
              </h5>
              <small class="opacity-75">Kelola akun operator dan admin sistem</small>
            </div>
            <button class="btn btn-light btn-sm" onclick="showAddModal()">
              <i class="bi bi-plus-circle me-1"></i>Tambah Operator
            </button>
          </div>
        </div>
        
        <div class="card-body">
          <!-- Statistics Cards -->
          <div class="row mb-4">
            <div class="col-md-3">
              <div class="card bg-primary text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h6 class="card-title">Total Operator</h6>
                      <h3 class="mb-0" id="totalOperators">0</h3>
                    </div>
                    <div class="align-self-center">
                      <i class="bi bi-people fs-1 opacity-75"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="card bg-success text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h6 class="card-title">Aktif</h6>
                      <h3 class="mb-0" id="activeOperators">0</h3>
                    </div>
                    <div class="align-self-center">
                      <i class="bi bi-check-circle fs-1 opacity-75"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="card bg-warning text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h6 class="card-title">Admin</h6>
                      <h3 class="mb-0" id="adminCount">0</h3>
                    </div>
                    <div class="align-self-center">
                      <i class="bi bi-shield-check fs-1 opacity-75"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="card bg-info text-white">
                <div class="card-body">
                  <div class="d-flex justify-content-between">
                    <div>
                      <h6 class="card-title">Operator</h6>
                      <h3 class="mb-0" id="operatorCount">0</h3>
                    </div>
                    <div class="align-self-center">
                      <i class="bi bi-person-badge fs-1 opacity-75"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Search and Filter -->
          <div class="row mb-3">
            <div class="col-md-6">
              <div class="input-group">
                <span class="input-group-text"><i class="bi bi-search"></i></span>
                <input type="text" class="form-control" id="searchOperator" placeholder="Cari operator...">
              </div>
            </div>
            <div class="col-md-3">
              <select class="form-select" id="filterRole">
                <option value="">Semua Role</option>
                <option value="admin">Admin</option>
                <option value="operator">Operator</option>
              </select>
            </div>
            <div class="col-md-3">
              <select class="form-select" id="filterStatus">
                <option value="">Semua Status</option>
                <option value="aktif">Aktif</option>
                <option value="nonaktif">Nonaktif</option>
              </select>
            </div>
          </div>

          <!-- Operators Table -->
          <div class="table-responsive">
            <table class="table table-hover" id="operatorsTable">
              <thead class="table-dark">
                <tr>
                  <th>Username</th>
                  <th>Nama</th>
                  <th>No. WhatsApp</th>
                  <th>Role</th>
                  <th>Status</th>
                  <th>Last Login</th>
                  <th>Aksi</th>
                </tr>
              </thead>
              <tbody id="operatorsTableBody">
                <tr>
                  <td colspan="7" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                      <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 mb-0">Memuat data operator...</p>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Add/Edit Operator Modal -->
<div class="modal fade" id="operatorModal" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header bg-primary text-white">
        <h5 class="modal-title" id="operatorModalTitle">
          <i class="bi bi-person-plus me-2"></i>Tambah Operator
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <form id="operatorForm">
          <input type="hidden" id="operatorId" name="operatorId">
          
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="username" class="form-label">Username <span class="text-danger">*</span></label>
                <div class="input-group">
                  <span class="input-group-text"><i class="bi bi-person"></i></span>
                  <input type="text" class="form-control" id="username" name="username" required>
                </div>
                <div class="invalid-feedback"></div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label for="nama" class="form-label">Nama Lengkap <span class="text-danger">*</span></label>
                <div class="input-group">
                  <span class="input-group-text"><i class="bi bi-person-badge"></i></span>
                  <input type="text" class="form-control" id="nama" name="nama" required>
                </div>
                <div class="invalid-feedback"></div>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="no_wa" class="form-label">No. WhatsApp <span class="text-danger">*</span></label>
                <div class="input-group">
                  <span class="input-group-text"><i class="bi bi-whatsapp"></i></span>
                  <input type="text" class="form-control" id="no_wa" name="no_wa" required>
                </div>
                <div class="invalid-feedback"></div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label for="password" class="form-label">Password <span class="text-danger" id="passwordRequired">*</span></label>
                <div class="input-group">
                  <span class="input-group-text"><i class="bi bi-lock"></i></span>
                  <input type="password" class="form-control" id="password" name="password">
                  <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                    <i class="bi bi-eye" id="passwordToggleIcon"></i>
                  </button>
                </div>
                <div class="invalid-feedback"></div>
                <small class="text-muted" id="passwordHelp">Minimal 6 karakter</small>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label for="role" class="form-label">Role <span class="text-danger">*</span></label>
                <select class="form-select" id="role" name="role" required>
                  <option value="">Pilih Role</option>
                  <option value="admin">Admin</option>
                  <option value="operator">Operator</option>
                </select>
                <div class="invalid-feedback"></div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                <select class="form-select" id="status" name="status" required>
                  <option value="">Pilih Status</option>
                  <option value="aktif">Aktif</option>
                  <option value="nonaktif">Nonaktif</option>
                </select>
                <div class="invalid-feedback"></div>
              </div>
            </div>
          </div>

          <div class="alert alert-info">
            <h6><i class="bi bi-info-circle me-2"></i>Informasi Role:</h6>
            <ul class="mb-0">
              <li><strong>Admin:</strong> Akses penuh ke semua menu dan fitur</li>
              <li><strong>Operator:</strong> Akses terbatas (Dashboard, Monitor, Member, Kasir)</li>
            </ul>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          <i class="bi bi-x-circle me-1"></i>Batal
        </button>
        <button type="button" class="btn btn-primary" onclick="saveOperator()">
          <i class="bi bi-check-circle me-1"></i>Simpan
        </button>
      </div>
    </div>
  </div>
</div>

<!-- SweetAlert2 -->
<script src="<?= base_url('assets/sweetalert2/sweetalert2.all.min.js') ?>"></script>

<style>
.card {
  border: none;
  border-radius: 15px;
}

.card-header {
  border-radius: 15px 15px 0 0 !important;
}

.bg-gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.table th {
  border-top: none;
  font-weight: 600;
  font-size: 0.9rem;
}

.badge {
  font-size: 0.75rem;
  padding: 0.5em 0.75em;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

.input-group-text {
  background-color: #f8f9fa;
  border-color: #dee2e6;
}

.form-control:focus, .form-select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.modal-content {
  border-radius: 15px;
  border: none;
}

.modal-header {
  border-radius: 15px 15px 0 0;
}

.table-hover tbody tr:hover {
  background-color: rgba(102, 126, 234, 0.05);
}

.spinner-border {
  width: 2rem;
  height: 2rem;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}
</style>

<script>
let operators = [];
let isEditMode = false;

document.addEventListener('DOMContentLoaded', function() {
    loadOperators();

    // Search functionality
    document.getElementById('searchOperator').addEventListener('input', filterOperators);
    document.getElementById('filterRole').addEventListener('change', filterOperators);
    document.getElementById('filterStatus').addEventListener('change', filterOperators);
});

function loadOperators() {
    fetch('<?= base_url('setting/getOperators') ?>')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                operators = data.data;
                renderOperators(operators);
                updateStatistics(operators);
            } else {
                showAlert('error', 'Gagal memuat data operator', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', 'Terjadi kesalahan', 'Gagal memuat data operator');
        });
}

function renderOperators(operatorList) {
    const tbody = document.getElementById('operatorsTableBody');

    if (operatorList.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center text-muted py-4">
                    <i class="bi bi-inbox fs-1 d-block mb-2"></i>
                    Tidak ada data operator
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = operatorList.map(operator => `
        <tr class="fade-in">
            <td>
                <div class="d-flex align-items-center">
                    <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                        <i class="bi bi-person"></i>
                    </div>
                    <strong>${operator.username}</strong>
                </div>
            </td>
            <td>${operator.nama}</td>
            <td>
                <a href="https://wa.me/${operator.no_wa}" target="_blank" class="text-decoration-none">
                    <i class="bi bi-whatsapp text-success me-1"></i>${operator.no_wa}
                </a>
            </td>
            <td>
                <span class="badge ${operator.role === 'admin' ? 'bg-warning' : 'bg-info'}">
                    <i class="bi bi-${operator.role === 'admin' ? 'shield-check' : 'person-badge'} me-1"></i>
                    ${operator.role.toUpperCase()}
                </span>
            </td>
            <td>
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox"
                           ${operator.status === 'aktif' ? 'checked' : ''}
                           onchange="toggleStatus(${operator.id})"
                           ${operator.id == <?= session()->get('operator_id') ?> ? 'disabled' : ''}>
                    <label class="form-check-label">
                        <span class="badge ${operator.status === 'aktif' ? 'bg-success' : 'bg-secondary'}">
                            ${operator.status.toUpperCase()}
                        </span>
                    </label>
                </div>
            </td>
            <td>
                <small class="text-muted">
                    <i class="bi bi-clock me-1"></i>
                    ${operator.last_login_formatted}
                </small>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="editOperator(${operator.id})" title="Edit">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteOperator(${operator.id})"
                            title="Hapus" ${operator.id == <?= session()->get('operator_id') ?> ? 'disabled' : ''}>
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

function updateStatistics(operatorList) {
    const total = operatorList.length;
    const active = operatorList.filter(op => op.status === 'aktif').length;
    const admins = operatorList.filter(op => op.role === 'admin').length;
    const operators = operatorList.filter(op => op.role === 'operator').length;

    document.getElementById('totalOperators').textContent = total;
    document.getElementById('activeOperators').textContent = active;
    document.getElementById('adminCount').textContent = admins;
    document.getElementById('operatorCount').textContent = operators;
}

function filterOperators() {
    const search = document.getElementById('searchOperator').value.toLowerCase();
    const roleFilter = document.getElementById('filterRole').value;
    const statusFilter = document.getElementById('filterStatus').value;

    const filtered = operators.filter(operator => {
        const matchSearch = operator.username.toLowerCase().includes(search) ||
                           operator.nama.toLowerCase().includes(search) ||
                           operator.no_wa.includes(search);
        const matchRole = !roleFilter || operator.role === roleFilter;
        const matchStatus = !statusFilter || operator.status === statusFilter;

        return matchSearch && matchRole && matchStatus;
    });

    renderOperators(filtered);
}

function showAddModal() {
    isEditMode = false;
    document.getElementById('operatorModalTitle').innerHTML = '<i class="bi bi-person-plus me-2"></i>Tambah Operator';
    document.getElementById('operatorForm').reset();
    document.getElementById('operatorId').value = '';
    document.getElementById('passwordRequired').style.display = 'inline';
    document.getElementById('password').required = true;
    document.getElementById('passwordHelp').textContent = 'Minimal 6 karakter';

    // Clear validation states
    clearValidationStates();

    new bootstrap.Modal(document.getElementById('operatorModal')).show();
}

function editOperator(id) {
    isEditMode = true;
    document.getElementById('operatorModalTitle').innerHTML = '<i class="bi bi-pencil me-2"></i>Edit Operator';
    document.getElementById('passwordRequired').style.display = 'none';
    document.getElementById('password').required = false;
    document.getElementById('passwordHelp').textContent = 'Kosongkan jika tidak ingin mengubah password';

    // Clear validation states
    clearValidationStates();

    fetch(`<?= base_url('setting/getOperator') ?>/${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const operator = data.data;
                document.getElementById('operatorId').value = operator.id;
                document.getElementById('username').value = operator.username;
                document.getElementById('nama').value = operator.nama;
                document.getElementById('no_wa').value = operator.no_wa;
                document.getElementById('role').value = operator.role;
                document.getElementById('status').value = operator.status;
                document.getElementById('password').value = '';

                new bootstrap.Modal(document.getElementById('operatorModal')).show();
            } else {
                showAlert('error', 'Gagal memuat data operator', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', 'Terjadi kesalahan', 'Gagal memuat data operator');
        });
}

function saveOperator() {
    const form = document.getElementById('operatorForm');
    const formData = new FormData(form);

    // Clear previous validation states
    clearValidationStates();

    const operatorId = document.getElementById('operatorId').value;
    const url = isEditMode ?
        `<?= base_url('setting/updateOperator') ?>/${operatorId}` :
        '<?= base_url('setting/createOperator') ?>';

    fetch(url, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', 'Berhasil', data.message);
            bootstrap.Modal.getInstance(document.getElementById('operatorModal')).hide();
            loadOperators();
        } else {
            if (data.errors) {
                showValidationErrors(data.errors);
            } else {
                showAlert('error', 'Gagal menyimpan', data.message);
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', 'Terjadi kesalahan', 'Gagal menyimpan data operator');
    });
}

function deleteOperator(id) {
    Swal.fire({
        title: 'Konfirmasi Hapus',
        text: 'Apakah Anda yakin ingin menghapus operator ini?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Ya, Hapus!',
        cancelButtonText: 'Batal'
    }).then((result) => {
        if (result.isConfirmed) {
            fetch(`<?= base_url('setting/deleteOperator') ?>/${id}`, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', 'Berhasil', data.message);
                    loadOperators();
                } else {
                    showAlert('error', 'Gagal menghapus', data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('error', 'Terjadi kesalahan', 'Gagal menghapus operator');
            });
        }
    });
}

function toggleStatus(id) {
    fetch(`<?= base_url('setting/toggleStatus') ?>/${id}`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', 'Berhasil', data.message);
            loadOperators();
        } else {
            showAlert('error', 'Gagal mengubah status', data.message);
            loadOperators(); // Reload to reset toggle state
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', 'Terjadi kesalahan', 'Gagal mengubah status');
        loadOperators(); // Reload to reset toggle state
    });
}

function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.getElementById('passwordToggleIcon');

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.className = 'bi bi-eye-slash';
    } else {
        passwordInput.type = 'password';
        toggleIcon.className = 'bi bi-eye';
    }
}

function clearValidationStates() {
    const form = document.getElementById('operatorForm');
    const inputs = form.querySelectorAll('.form-control, .form-select');

    inputs.forEach(input => {
        input.classList.remove('is-invalid');
        const feedback = input.parentNode.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.textContent = '';
        }
    });
}

function showValidationErrors(errors) {
    Object.keys(errors).forEach(field => {
        const input = document.getElementById(field);
        if (input) {
            input.classList.add('is-invalid');
            const feedback = input.parentNode.querySelector('.invalid-feedback');
            if (feedback) {
                feedback.textContent = errors[field];
            }
        }
    });
}

function showAlert(type, title, message) {
    Swal.fire({
        icon: type,
        title: title,
        text: message,
        confirmButtonColor: '#667eea'
    });
}
</script>

<?= $this->endSection() ?>
