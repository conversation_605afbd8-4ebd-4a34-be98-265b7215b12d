<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class SampleUnpaidSessionsSeeder extends Seeder
{
    public function run()
    {
        // Data sesi personal yang sudah selesai tapi belum dibayar untuk testing kasir
        $data = [
            [
                'station_id' => 1,
                'jenis_user' => 'personal',
                'waktu_mulai' => date('Y-m-d H:i:s', strtotime('-2 hours')),
                'waktu_berhenti' => date('Y-m-d H:i:s', strtotime('-1 hour')),
                'harga_total' => 15000,
                'status' => 'selesai',
                'id_member' => null,
                'id_paket' => null,
                'durasi_sisa' => null,
                'sisa_saldo' => null,
                'metode_bayar' => null,
                'jumlah_bayar' => null,
                'waktu_bayar' => null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'station_id' => 2,
                'jenis_user' => 'personal',
                'waktu_mulai' => date('Y-m-d H:i:s', strtotime('-3 hours')),
                'waktu_berhenti' => date('Y-m-d H:i:s', strtotime('-1.5 hours')),
                'harga_total' => 22500,
                'status' => 'selesai',
                'id_member' => null,
                'id_paket' => null,
                'durasi_sisa' => null,
                'sisa_saldo' => null,
                'metode_bayar' => null,
                'jumlah_bayar' => null,
                'waktu_bayar' => null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'station_id' => 3,
                'jenis_user' => 'personal',
                'waktu_mulai' => date('Y-m-d H:i:s', strtotime('-4 hours')),
                'waktu_berhenti' => date('Y-m-d H:i:s', strtotime('-2 hours')),
                'harga_total' => 30000,
                'status' => 'selesai',
                'id_member' => null,
                'id_paket' => null,
                'durasi_sisa' => null,
                'sisa_saldo' => null,
                'metode_bayar' => null,
                'jumlah_bayar' => null,
                'waktu_bayar' => null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'station_id' => 4,
                'jenis_user' => 'personal',
                'waktu_mulai' => date('Y-m-d H:i:s', strtotime('-1.5 hours')),
                'waktu_berhenti' => date('Y-m-d H:i:s', strtotime('-30 minutes')),
                'harga_total' => 15000,
                'status' => 'selesai',
                'id_member' => null,
                'id_paket' => null,
                'durasi_sisa' => null,
                'sisa_saldo' => null,
                'metode_bayar' => null,
                'jumlah_bayar' => null,
                'waktu_bayar' => null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        // Insert data ke tabel sesi
        $this->db->table('sesi')->insertBatch($data);
        
        echo "Sample unpaid sessions created successfully!\n";
    }
}
