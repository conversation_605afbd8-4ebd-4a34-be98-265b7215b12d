<?php
/**
 * Transaksi Detail Model
 * Model untuk mengelola detail item dalam transaksi penjualan
 * Menangani operasi database untuk detail transaksi per item
 *
 * <AUTHOR> Team
 * @version 3.0
 */

namespace App\Models;

use CodeIgniter\Model;

class TransaksiDetailModel extends Model
{
    // Konfigurasi tabel dan field
    protected $table = 'transaksi_detail';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'transaksi_id', 'produk_id', 'nama_produk', 'harga_satuan', 
        'jumlah', 'subtotal'
    ];
    protected $useTimestamps = true;
    
    /**
     * Get detail transaksi dengan info produk
     * 
     * @param int $transaksiId
     * @return array
     */
    public function getDetailWithProduk($transaksiId)
    {
        return $this->select('transaksi_detail.*, produk.kode_produk, produk.kategori')
                   ->join('produk', 'produk.id = transaksi_detail.produk_id', 'left')
                   ->where('transaksi_detail.transaksi_id', $transaksiId)
                   ->findAll();
    }
    
    /**
     * Get produk terlaris berdasarkan detail transaksi
     * 
     * @param string $startDate
     * @param string $endDate
     * @param int $limit
     * @return array
     */
    public function getProdukTerlaris($startDate = null, $endDate = null, $limit = 10)
    {
        $builder = $this->select('produk_id, nama_produk, SUM(jumlah) as total_terjual, SUM(subtotal) as total_pendapatan')
                       ->join('transaksi', 'transaksi.id = transaksi_detail.transaksi_id')
                       ->where('transaksi.status', 'selesai')
                       ->groupBy('produk_id, nama_produk')
                       ->orderBy('total_terjual', 'DESC')
                       ->limit($limit);
        
        if ($startDate) {
            $builder->where('DATE(transaksi.created_at) >=', $startDate);
        }
        
        if ($endDate) {
            $builder->where('DATE(transaksi.created_at) <=', $endDate);
        }
        
        return $builder->findAll();
    }
}
