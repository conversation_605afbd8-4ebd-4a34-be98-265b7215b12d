# Required Libraries for PlaySphere IoT Device

## Arduino IDE Library Manager
Install these libraries through Arduino IDE Library Manager:

1. **ESP8266WiFi** (Built-in with ESP8266 Core)
   - Version: Latest
   - Used for: WiFi connectivity

2. **ESP8266HTTPClient** (Built-in with ESP8266 Core)
   - Version: Latest  
   - Used for: HTTP API communication

3. **ArduinoJson**
   - Version: 6.x.x (latest stable)
   - Author: <PERSON><PERSON>
   - Used for: JSON parsing of API responses

4. **TFT_eSPI**
   - Version: 2.x.x (latest stable)
   - Author: Bodmer
   - Used for: ST7789 TFT display control
   - **Important**: Requires User_Setup.h configuration

## ESP8266 Board Package
Install ESP8266 board package in Arduino IDE:
- Board Manager URL: http://arduino.esp8266.com/stable/package_esp8266com_index.json
- Package: esp8266 by ESP8266 Community
- Version: 3.x.x (latest stable)

## Board Settings in Arduino IDE
- Board: "LOLIN(WEMOS) D1 R2 & mini"
- Upload Speed: "921600"
- CPU Frequency: "80 MHz"
- Flash Size: "4MB (FS:2MB OTA:~1019KB)"
- Debug Port: "Disabled"
- Debug Level: "None"
- IwIP Variant: "v2 Lower Memory"
- VTables: "Flash"
- Exceptions: "Legacy (new can return nullptr)"
- Erase Flash: "Only Sketch"
- SSL Support: "All SSL ciphers (most compatible)"

## TFT_eSPI Configuration
After installing TFT_eSPI library:

1. Navigate to Arduino libraries folder:
   - Windows: Documents/Arduino/libraries/TFT_eSPI/
   - Mac: ~/Documents/Arduino/libraries/TFT_eSPI/
   - Linux: ~/Arduino/libraries/TFT_eSPI/

2. Backup original User_Setup.h:
   ```
   cp User_Setup.h User_Setup.h.backup
   ```

3. Replace User_Setup.h with the provided configuration file, or manually edit:
   ```cpp
   #define ST7789_DRIVER
   #define TFT_MOSI D7
   #define TFT_SCLK D5  
   #define TFT_CS   -1
   #define TFT_DC   D3
   #define TFT_RST  D4
   #define TFT_BL   D8
   #define TFT_WIDTH  240
   #define TFT_HEIGHT 240
   ```

## Compilation Notes
- Total sketch size should be around 300-400KB
- RAM usage should be under 50KB
- If compilation fails, check library versions and board package version

## Alternative Installation Methods

### PlatformIO (Advanced Users)
```ini
[env:d1_mini]
platform = espressif8266
board = d1_mini
framework = arduino
lib_deps = 
    bodmer/TFT_eSPI@^2.5.0
    bblanchon/ArduinoJson@^6.21.0
build_flags = 
    -DUSER_SETUP_LOADED=1
    -DST7789_DRIVER=1
    -DTFT_WIDTH=240
    -DTFT_HEIGHT=240
    -DTFT_MOSI=D7
    -DTFT_SCLK=D5
    -DTFT_DC=D3
    -DTFT_RST=D4
    -DTFT_BL=D8
```

### Manual Library Installation
If automatic installation fails:
1. Download library ZIP files from GitHub
2. Extract to Arduino/libraries/ folder
3. Restart Arduino IDE
4. Verify libraries appear in Sketch > Include Library menu
