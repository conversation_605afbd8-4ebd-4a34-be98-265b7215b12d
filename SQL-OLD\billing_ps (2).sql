-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.0
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Aug 09, 2025 at 10:08 AM
-- Server version: 8.0.30
-- PHP Version: 8.1.10

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `billing_ps`
--

-- --------------------------------------------------------

--
-- Table structure for table `billing_sessions`
--

CREATE TABLE `billing_sessions` (
  `id` int NOT NULL,
  `channel_id` int DEFAULT NULL,
  `member_id` int DEFAULT NULL,
  `package_id` int DEFAULT NULL,
  `player_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `start_time` datetime DEFAULT NULL,
  `end_time` datetime DEFAULT NULL,
  `pause_time` datetime DEFAULT NULL,
  `total_pause_duration` int DEFAULT '0',
  `status` enum('active','paused','completed') COLLATE utf8mb4_general_ci DEFAULT 'active',
  `total_price` decimal(10,2) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `billing_sessions`
--

INSERT INTO `billing_sessions` (`id`, `channel_id`, `member_id`, `package_id`, `player_name`, `start_time`, `end_time`, `pause_time`, `total_pause_duration`, `status`, `total_price`, `created_at`) VALUES
(1, 15, 9, NULL, NULL, '2025-01-30 10:09:13', '2025-01-30 10:09:33', NULL, 0, 'completed', '250.00', '2025-01-30 10:09:33'),
(2, 15, 9, NULL, NULL, '2025-01-30 10:10:10', '2025-01-30 10:10:32', NULL, 0, 'completed', '250.00', '2025-01-30 10:10:32'),
(3, 15, 9, NULL, NULL, '2025-01-30 10:10:45', '2025-01-30 10:10:50', NULL, 0, 'completed', '250.00', '2025-01-30 10:10:50'),
(4, 15, 9, NULL, NULL, '2025-01-30 10:11:50', '2025-01-30 10:14:18', NULL, 0, 'completed', '500.00', '2025-01-30 10:14:17'),
(5, 15, 9, NULL, NULL, '2025-01-30 10:26:36', '2025-01-30 10:30:03', NULL, 0, 'completed', '750.00', '2025-01-30 10:30:03'),
(6, 15, 9, NULL, NULL, '2025-01-30 10:36:12', '2025-01-30 10:36:27', NULL, 0, 'completed', '250.00', '2025-01-30 10:36:27'),
(7, 15, 9, NULL, NULL, '2025-01-30 10:40:13', '2025-01-30 10:40:51', NULL, 0, 'completed', '250.00', '2025-01-30 10:40:51'),
(8, 15, 9, NULL, NULL, '2025-01-30 10:41:15', '2025-01-30 10:41:36', NULL, 0, 'completed', '250.00', '2025-01-30 10:41:36'),
(9, 15, 9, NULL, NULL, '2025-01-30 10:44:01', '2025-01-30 10:45:11', NULL, 0, 'completed', '250.00', '2025-01-30 10:45:11'),
(10, 15, 9, NULL, NULL, '2025-01-30 10:49:26', '2025-01-30 10:49:33', NULL, 0, 'completed', '250.00', '2025-01-30 10:49:33'),
(11, 15, 9, NULL, NULL, '2025-01-30 10:50:25', '2025-01-30 10:50:51', NULL, 0, 'completed', '30000.00', '2025-01-30 10:50:50'),
(12, 15, 9, NULL, NULL, '2025-01-30 11:18:12', '2025-01-30 11:18:55', NULL, 0, 'completed', '250.00', '2025-01-30 11:18:55'),
(13, 15, 9, NULL, NULL, '2025-01-30 11:19:50', '2025-01-30 11:39:36', NULL, 0, 'completed', '30000.00', '2025-01-30 11:39:36'),
(14, 15, 9, NULL, NULL, '2025-01-30 11:44:29', '2025-01-30 11:47:35', NULL, 0, 'completed', '750.00', '2025-01-30 11:47:34'),
(15, 15, 9, NULL, NULL, '2025-01-30 11:48:03', '2025-01-30 11:48:19', NULL, 0, 'completed', '5000.00', '2025-01-30 11:48:18'),
(16, 15, 9, NULL, NULL, '2025-01-30 11:49:20', '2025-01-30 11:50:34', NULL, 0, 'completed', '105000.00', '2025-01-30 11:50:33'),
(17, 15, 9, NULL, NULL, '2025-01-30 14:29:14', '2025-01-30 14:31:30', NULL, 0, 'completed', '500.00', '2025-01-30 14:31:30'),
(18, 15, 9, NULL, NULL, '2025-01-30 14:32:03', '2025-01-30 14:32:25', NULL, 0, 'completed', '5000.00', '2025-01-30 14:32:24'),
(19, 15, 9, NULL, NULL, '2025-01-30 14:32:46', '2025-01-30 14:34:19', NULL, 0, 'completed', '10000.00', '2025-01-30 14:34:19'),
(20, 15, NULL, NULL, NULL, '2025-01-30 14:45:36', '2025-01-30 14:46:37', NULL, 0, 'completed', '100000.00', '2025-01-30 14:46:36'),
(21, 15, 9, NULL, NULL, '2025-01-30 14:47:51', '2025-01-30 14:48:52', NULL, 0, 'completed', '5000.00', '2025-01-30 14:48:51'),
(22, 15, 8, NULL, NULL, '2025-01-30 14:50:40', '2025-01-30 14:51:41', NULL, 0, 'completed', '5000.00', '2025-01-30 14:51:40'),
(23, 15, NULL, NULL, NULL, '2025-01-30 14:58:18', '2025-01-30 14:59:19', NULL, 0, 'completed', '5000.00', '2025-01-30 14:59:18'),
(27, 15, 8, NULL, NULL, '2025-01-30 15:07:24', '2025-01-30 15:08:25', NULL, 0, 'completed', '5000.00', '2025-01-30 15:08:25'),
(28, 15, 9, NULL, NULL, '2025-01-30 15:09:33', '2025-01-30 15:09:43', NULL, 0, 'completed', '5000.00', '2025-01-30 15:09:43'),
(29, 15, 9, NULL, NULL, '2025-01-30 15:09:57', '2025-01-30 15:10:22', NULL, 0, 'completed', '10000.00', '2025-01-30 15:10:22'),
(30, 15, NULL, NULL, NULL, '2025-01-30 15:10:55', '2025-01-30 15:11:02', NULL, 0, 'completed', '25000.00', '2025-01-30 15:11:02'),
(31, 15, NULL, NULL, NULL, '2025-01-30 15:11:14', '2025-01-30 15:11:30', NULL, 0, 'completed', '30000.00', '2025-01-30 15:11:30'),
(32, 15, 9, NULL, NULL, '2025-01-30 15:12:19', '2025-01-30 15:16:34', NULL, 0, 'completed', '1000.00', '2025-01-30 15:16:33'),
(33, 15, 9, NULL, NULL, '2025-01-30 15:17:01', '2025-01-30 15:17:08', NULL, 0, 'completed', '250.00', '2025-01-30 15:17:07'),
(34, 15, 9, NULL, NULL, '2025-01-30 15:29:22', '2025-01-30 15:29:29', NULL, 0, 'completed', '250.00', '2025-01-30 15:29:28'),
(35, 15, 9, NULL, NULL, '2025-01-30 15:29:50', '2025-01-30 15:29:56', NULL, 0, 'completed', '250.00', '2025-01-30 15:29:56'),
(36, 15, 9, NULL, NULL, '2025-01-30 15:30:04', '2025-01-30 15:33:04', NULL, 0, 'completed', '500.00', '2025-01-30 15:33:04'),
(37, 15, 8, NULL, NULL, '2025-01-30 15:33:28', '2025-01-30 15:33:32', NULL, 0, 'completed', '250.00', '2025-01-30 15:33:32'),
(38, 15, 9, NULL, NULL, '2025-01-30 15:41:25', '2025-01-30 15:41:29', NULL, 0, 'completed', '250.00', '2025-01-30 15:41:29'),
(39, 15, 8, NULL, NULL, '2025-01-30 15:42:38', '2025-01-30 15:42:55', NULL, 0, 'completed', '250.00', '2025-01-30 15:42:55'),
(40, 15, 8, NULL, NULL, '2025-01-30 15:43:34', '2025-01-30 15:43:49', NULL, 0, 'completed', '250.00', '2025-01-30 15:43:49'),
(41, 15, NULL, NULL, NULL, '2025-01-30 15:46:23', '2025-01-30 15:47:05', NULL, 0, 'completed', '15000.00', '2025-01-30 15:47:05'),
(42, 15, NULL, NULL, NULL, '2025-01-30 15:47:21', '2025-01-30 15:47:41', NULL, 0, 'completed', '15000.00', '2025-01-30 15:47:41'),
(43, 15, 8, NULL, NULL, '2025-01-30 15:51:49', '2025-01-30 15:51:49', NULL, 0, 'completed', '250.00', '2025-01-30 15:51:49'),
(44, 15, NULL, NULL, NULL, '2025-01-30 15:51:48', '2025-01-30 15:52:04', NULL, 0, 'completed', '15000.00', '2025-01-30 15:52:03'),
(45, 15, 9, NULL, NULL, '2025-01-30 15:52:31', '2025-01-30 15:52:31', NULL, 0, 'completed', '250.00', '2025-01-30 15:52:31'),
(46, 15, NULL, NULL, NULL, '2025-01-30 15:52:30', '2025-01-30 15:56:45', NULL, 0, 'completed', '15000.00', '2025-01-30 15:56:45'),
(47, 15, 9, NULL, NULL, '2025-01-30 15:57:05', '2025-01-30 15:57:05', NULL, 0, 'completed', '250.00', '2025-01-30 15:57:05'),
(48, 15, 9, NULL, NULL, '2025-01-30 15:57:04', '2025-01-30 15:57:06', NULL, 0, 'completed', '250.00', '2025-01-30 15:57:06'),
(49, 15, NULL, NULL, NULL, '2025-01-30 16:02:46', '2025-01-30 16:03:28', NULL, 0, 'completed', '15000.00', '2025-01-30 16:03:28'),
(50, 15, NULL, NULL, NULL, '2025-01-30 16:03:43', '2025-01-30 16:04:00', NULL, 0, 'completed', '25000.00', '2025-01-30 16:03:59'),
(51, 15, NULL, NULL, NULL, '2025-01-30 16:04:08', '2025-01-30 16:04:28', NULL, 0, 'completed', '50000.00', '2025-01-30 16:04:28'),
(52, 15, 9, NULL, NULL, '2025-01-30 16:05:00', '2025-01-30 16:05:01', NULL, 0, 'completed', '250.00', '2025-01-30 16:05:00'),
(53, 15, 8, NULL, NULL, '2025-01-30 16:05:34', '2025-01-30 16:05:34', NULL, 0, 'completed', '250.00', '2025-01-30 16:05:34'),
(54, 15, NULL, NULL, NULL, '2025-01-30 16:05:33', '2025-01-30 16:10:39', NULL, 0, 'completed', '15000.00', '2025-01-30 16:10:39'),
(55, 15, NULL, NULL, NULL, '2025-01-30 16:12:20', '2025-01-30 16:13:20', NULL, 0, 'completed', '5000.00', '2025-01-30 16:13:19'),
(56, 15, NULL, NULL, NULL, '2025-01-30 16:13:58', '2025-01-30 16:15:11', NULL, 0, 'completed', '30000.00', '2025-01-30 16:15:11'),
(57, 15, 8, NULL, NULL, '2025-01-31 00:41:50', '2025-01-31 00:41:50', NULL, 0, 'completed', '250.00', '2025-01-31 00:41:50'),
(58, 15, NULL, NULL, NULL, '2025-01-31 00:41:49', '2025-01-31 00:41:57', NULL, 0, 'completed', '15000.00', '2025-01-31 00:41:57'),
(59, 15, NULL, NULL, NULL, '2025-01-31 00:47:58', '2025-01-31 00:48:31', NULL, 0, 'completed', '15000.00', '2025-01-31 00:48:31'),
(60, 15, 9, NULL, NULL, '2025-01-31 00:48:45', '2025-01-31 00:48:54', NULL, 0, 'completed', '250.00', '2025-01-31 00:48:54'),
(61, 15, NULL, NULL, NULL, '2025-01-31 00:49:05', '2025-01-31 00:49:17', NULL, 0, 'completed', '15000.00', '2025-01-31 00:49:17'),
(62, 15, 9, NULL, NULL, '2025-01-31 00:55:04', '2025-01-31 00:55:05', NULL, 0, 'completed', '250.00', '2025-01-31 00:55:05'),
(63, 15, 8, NULL, NULL, '2025-01-31 00:55:16', '2025-01-31 00:55:16', NULL, 0, 'completed', '250.00', '2025-01-31 00:55:16'),
(64, 15, 8, NULL, NULL, '2025-01-31 00:55:17', '2025-01-31 00:55:17', NULL, 0, 'completed', '250.00', '2025-01-31 00:55:17'),
(65, 15, 9, NULL, NULL, '2025-01-31 01:02:28', '2025-01-31 01:02:29', NULL, 0, 'completed', '250.00', '2025-01-31 01:02:28'),
(66, 15, 8, NULL, NULL, '2025-01-31 01:02:36', '2025-01-31 01:02:36', NULL, 0, 'completed', '250.00', '2025-01-31 01:02:36'),
(67, 15, 8, NULL, NULL, '2025-01-31 01:02:37', '2025-01-31 01:02:37', NULL, 0, 'completed', '250.00', '2025-01-31 01:02:37'),
(68, 15, 8, NULL, NULL, '2025-01-31 10:43:42', '2025-01-31 10:44:01', NULL, 0, 'completed', '250.00', '2025-01-31 10:44:01'),
(69, 15, 8, NULL, NULL, '2025-01-31 10:44:30', '2025-01-31 10:48:02', NULL, 0, 'completed', '250.00', '2025-01-31 10:48:01'),
(70, 15, 8, NULL, NULL, '2025-01-31 10:48:21', '2025-01-31 10:49:21', NULL, 0, 'completed', '5000.00', '2025-01-31 10:49:21'),
(71, 15, 9, NULL, NULL, '2025-01-31 10:57:01', '2025-01-31 10:57:07', NULL, 0, 'completed', '250.00', '2025-01-31 10:57:07'),
(72, 15, 8, NULL, NULL, '2025-01-31 12:53:19', '2025-01-31 12:54:19', NULL, 0, 'completed', '5000.00', '2025-01-31 12:54:19'),
(73, 15, 8, NULL, NULL, '2025-01-31 12:55:14', '2025-01-31 12:56:14', NULL, 0, 'completed', '5000.00', '2025-01-31 12:56:14'),
(74, 15, 8, NULL, NULL, '2025-01-31 12:59:03', '2025-01-31 13:00:03', NULL, 0, 'completed', '5000.00', '2025-01-31 13:00:02'),
(75, 15, 8, NULL, NULL, '2025-01-31 13:11:24', '2025-01-31 13:12:25', NULL, 0, 'completed', '5000.00', '2025-01-31 13:12:24'),
(76, 15, 8, NULL, NULL, '2025-01-31 17:03:38', '2025-01-31 17:04:08', NULL, 0, 'completed', '250.00', '2025-01-31 17:04:08'),
(77, 15, 8, NULL, NULL, '2025-01-31 17:04:33', '2025-01-31 17:04:45', NULL, 0, 'completed', '250.00', '2025-01-31 17:04:45'),
(78, 15, 8, NULL, NULL, '2025-01-31 17:05:34', '2025-01-31 17:06:35', NULL, 0, 'completed', '5000.00', '2025-01-31 17:06:34'),
(79, 15, 8, NULL, NULL, '2025-01-31 17:06:53', '2025-01-31 17:07:40', NULL, 0, 'completed', '250.00', '2025-01-31 17:07:40');

-- --------------------------------------------------------

--
-- Table structure for table `channels`
--

CREATE TABLE `channels` (
  `id` int NOT NULL,
  `number` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `ps_type_id` int DEFAULT NULL,
  `ip_address` varchar(15) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `status` enum('available','in_use','maintenance','connected','disconnected') COLLATE utf8mb4_general_ci DEFAULT 'disconnected',
  `connection_status` enum('connected','disconnected') COLLATE utf8mb4_general_ci DEFAULT 'disconnected',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `session_data` text COLLATE utf8mb4_general_ci,
  `wemos_data` text COLLATE utf8mb4_general_ci,
  `last_ping` timestamp NULL DEFAULT NULL,
  `last_seen` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `channels`
--

INSERT INTO `channels` (`id`, `number`, `ps_type_id`, `ip_address`, `status`, `connection_status`, `created_at`, `updated_at`, `session_data`, `wemos_data`, `last_ping`, `last_seen`) VALUES
(12, 'Channel 1', 5, '************', 'available', 'disconnected', '2025-01-26 04:50:05', '2025-01-29 08:53:43', NULL, NULL, NULL, '2025-01-29 01:53:23'),
(14, 'Channel 2', 6, '************', 'available', 'disconnected', '2025-01-26 05:05:48', '2025-01-29 05:09:16', NULL, NULL, NULL, '2025-01-28 22:08:54'),
(15, 'Channel 3', 5, '************', 'available', 'connected', '2025-01-27 02:49:22', '2025-02-02 08:53:39', NULL, NULL, NULL, '2025-02-01 02:31:03'),
(16, 'Channel 4', 6, '************', 'available', 'disconnected', '2025-01-27 02:49:43', '2025-01-29 01:50:57', NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `members`
--

CREATE TABLE `members` (
  `id` int NOT NULL,
  `member_code` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `name` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `phone` varchar(15) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `balance` decimal(10,2) DEFAULT '0.00',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `members`
--

INSERT INTO `members` (`id`, `member_code`, `name`, `phone`, `balance`, `created_at`, `updated_at`) VALUES
(8, 'PS2501304726', 'noname', '08123498524', '32000.00', '2025-01-29 23:10:10', '2025-02-01 00:07:40'),
(9, 'PS2501303386', 'Nuril', '081332499304', '0.00', '2025-01-29 23:10:24', '2025-01-30 15:57:05');

-- --------------------------------------------------------

--
-- Table structure for table `migrations`
--

CREATE TABLE `migrations` (
  `id` bigint UNSIGNED NOT NULL,
  `version` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `class` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `group` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `namespace` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `time` int NOT NULL,
  `batch` int UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `migrations`
--

INSERT INTO `migrations` (`id`, `version`, `class`, `group`, `namespace`, `time`, `batch`) VALUES
(1, '2024-01-30-000001', 'App\\Database\\Migrations\\UpdateMemberTables', 'default', 'App', 1738212218, 1);

-- --------------------------------------------------------

--
-- Table structure for table `packages`
--

CREATE TABLE `packages` (
  `id` int NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `duration` int DEFAULT NULL COMMENT 'Durasi dalam menit',
  `price` decimal(10,2) NOT NULL,
  `description` text COLLATE utf8mb4_general_ci,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `packages`
--

INSERT INTO `packages` (`id`, `name`, `duration`, `price`, `description`, `is_active`, `created_at`, `updated_at`) VALUES
(2, 'Paket 2 Jam', 120, '25000.00', 'Durasi 2 jam', 1, '2025-01-27 00:08:02', '2025-01-26 17:40:59'),
(3, 'Paket Malam', 360, '50000.00', 'Paket 6 jam malam', 1, '2025-01-27 00:08:02', '2025-01-26 17:40:58'),
(4, 'Paket khsus', 390, '40000.00', 'enam jam setengah', 1, '2025-01-26 17:20:26', '2025-07-31 17:59:15'),
(5, 'paket oye', 600, '80000.00', 'sepuluh jam', 1, '2025-01-26 17:21:05', '2025-01-26 17:21:05'),
(6, 'paket mblenek', 1440, '5000.00', 'Sedino suwengi ben kapok\r\n', 1, '2025-01-26 17:21:33', '2025-01-26 17:40:38'),
(7, 'paket diluk', 1, '100000.00', 'ben kapok', 1, '2025-01-27 06:21:43', '2025-01-27 06:22:30'),
(8, 'sak jam', 60, '500000.00', 'sak jam 500 ewu', 1, '2025-01-29 04:56:02', '2025-01-29 04:56:02'),
(9, 'rong menit', 2, '60000.00', 'rong menit tok', 1, '2025-01-29 06:01:15', '2025-01-29 06:01:15'),
(10, 'diluk murah', 1, '5000.00', 'sediluk tok', 1, '2025-01-30 07:47:33', '2025-01-30 07:47:33');

-- --------------------------------------------------------

--
-- Table structure for table `ps_types`
--

CREATE TABLE `ps_types` (
  `id` int NOT NULL,
  `name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `price_per_hour` decimal(10,2) NOT NULL,
  `description` text COLLATE utf8mb4_general_ci,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ps_types`
--

INSERT INTO `ps_types` (`id`, `name`, `price_per_hour`, `description`, `created_at`, `updated_at`) VALUES
(5, 'PS-4', '15000.00', 'PlayStation 4', '2025-01-25 23:50:39', '2025-01-26 16:56:35'),
(6, 'PS-5', '25000.00', 'PlayStation 5', '2025-01-26 16:56:28', '2025-01-26 16:56:28');

-- --------------------------------------------------------

--
-- Table structure for table `topup_history`
--

CREATE TABLE `topup_history` (
  `id` int NOT NULL,
  `member_id` int DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `transaction_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `topup_history`
--

INSERT INTO `topup_history` (`id`, `member_id`, `amount`, `transaction_date`) VALUES
(7, 9, '50000.00', '2025-01-30 02:07:10'),
(8, 8, '50000.00', '2025-01-30 07:50:16');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `billing_sessions`
--
ALTER TABLE `billing_sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `channel_id` (`channel_id`),
  ADD KEY `member_id` (`member_id`),
  ADD KEY `package_id` (`package_id`);

--
-- Indexes for table `channels`
--
ALTER TABLE `channels`
  ADD PRIMARY KEY (`id`),
  ADD KEY `channels_ibfk_1` (`ps_type_id`);

--
-- Indexes for table `members`
--
ALTER TABLE `members`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `member_code` (`member_code`);

--
-- Indexes for table `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `packages`
--
ALTER TABLE `packages`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `ps_types`
--
ALTER TABLE `ps_types`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `topup_history`
--
ALTER TABLE `topup_history`
  ADD PRIMARY KEY (`id`),
  ADD KEY `member_id` (`member_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `billing_sessions`
--
ALTER TABLE `billing_sessions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=80;

--
-- AUTO_INCREMENT for table `channels`
--
ALTER TABLE `channels`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=18;

--
-- AUTO_INCREMENT for table `members`
--
ALTER TABLE `members`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `packages`
--
ALTER TABLE `packages`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `ps_types`
--
ALTER TABLE `ps_types`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `topup_history`
--
ALTER TABLE `topup_history`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `billing_sessions`
--
ALTER TABLE `billing_sessions`
  ADD CONSTRAINT `billing_sessions_ibfk_1` FOREIGN KEY (`channel_id`) REFERENCES `channels` (`id`),
  ADD CONSTRAINT `billing_sessions_ibfk_2` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`),
  ADD CONSTRAINT `billing_sessions_ibfk_3` FOREIGN KEY (`package_id`) REFERENCES `packages` (`id`);

--
-- Constraints for table `channels`
--
ALTER TABLE `channels`
  ADD CONSTRAINT `channels_ibfk_1` FOREIGN KEY (`ps_type_id`) REFERENCES `ps_types` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `topup_history`
--
ALTER TABLE `topup_history`
  ADD CONSTRAINT `topup_history_ibfk_1` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
