<?php
/**
 * <PERSON>sir Controller
 * Controller untuk sistem Point of Sale (POS) dan transaksi kasir
 * Menangani pembayaran sesi, top-up member, penjualan barang, dan transaksi lainnya
 *
 * <AUTHOR> Team
 * @version 3.0
 */

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\SesiModel;
use App\Models\MemberModel;
use App\Models\StationModel;
use App\Models\KonsolModel;
use App\Models\TopupModel;
use App\Models\ProdukModel;
use App\Models\TransaksiModel;
use App\Models\TransaksiDetailModel;
use App\Models\TransaksiKasirModel;
use App\Models\TransaksiKasirDetailModel;

class Kasir extends BaseController
{
    /**
     * Halaman utama kasir
     * Menampilkan dashboard kasir dengan berbagai fitur transaksi
     *
     * @return string View kasir index
     */
    public function index()
    {
        $sesiModel = new SesiModel();
        $memberModel = new MemberModel();
        
        // Ambil sesi personal yang sudah selesai tapi belum dibayar
        // Debug: cek semua sesi dulu
        $allSessions = $sesiModel->findAll();
        log_message('info', 'Total sessions in database: ' . count($allSessions));

        $unpaidSessions = $sesiModel
            ->select('sesi.*, station.nama_station, konsol.nama_konsol')
            ->join('station', 'station.id = sesi.station_id')
            ->join('konsol', 'konsol.id = station.id_konsol')
            ->where('sesi.jenis_user', 'personal')
            ->where('sesi.waktu_berhenti IS NOT NULL') // Sesi sudah berhenti
            ->where('sesi.status_bayar', 'belum') // Belum dibayar
            ->orderBy('sesi.waktu_berhenti', 'DESC')
            ->limit(10)
            ->findAll();

        // Debug: log jumlah sesi yang ditemukan
        log_message('info', 'Unpaid sessions found: ' . count($unpaidSessions));
        
        // Ambil data member untuk top-up
        $members = $memberModel->findAll();

        // Ambil data produk untuk penjualan
        $produkModel = new ProdukModel();
        $produk = $produkModel->where('status', 'aktif')->findAll();

        return view('kasir/index', [
            'unpaidSessions' => $unpaidSessions,
            'members' => $members,
            'produk' => $produk
        ]);
    }
    
    /**
     * Proses pembayaran sesi personal
     * Menandai sesi sebagai sudah dibayar
     *
     * @return \CodeIgniter\HTTP\ResponseInterface JSON response
     */
    public function bayarSesi()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(405)->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ]);
        }
        
        $sesiId = $this->request->getPost('sesi_id');
        $metodeBayar = $this->request->getPost('metode_bayar');
        $jumlahBayar = $this->request->getPost('jumlah_bayar');
        
        if (!$sesiId || !$metodeBayar || !$jumlahBayar) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Data tidak lengkap'
            ]);
        }
        
        $sesiModel = new SesiModel();
        $sesi = $sesiModel->find($sesiId);
        
        if (!$sesi) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Sesi tidak ditemukan'
            ]);
        }
        
        if ($sesi['status'] !== 'selesai') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Sesi belum selesai'
            ]);
        }
        
        // Update status pembayaran
        $updated = $sesiModel->update($sesiId, [
            'status_bayar' => 'bayar',
            'metode_bayar' => $metodeBayar,
            'jumlah_bayar' => $jumlahBayar,
            'waktu_bayar' => date('Y-m-d H:i:s')
        ]);
        
        if ($updated) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Pembayaran berhasil diproses',
                'kembalian' => $jumlahBayar - $sesi['harga_total']
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Gagal memproses pembayaran'
            ]);
        }
    }
    
    /**
     * Proses top-up saldo member
     * Menambah saldo member dan mencatat transaksi
     *
     * @return \CodeIgniter\HTTP\ResponseInterface JSON response
     */
    public function topupMember()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(405)->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ]);
        }
        
        $memberId = $this->request->getPost('member_id');
        $jumlah = $this->request->getPost('jumlah');
        $metodeBayar = $this->request->getPost('metode_bayar');
        
        if (!$memberId || !$jumlah || !$metodeBayar) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Data tidak lengkap'
            ]);
        }
        
        $memberModel = new MemberModel();
        $topupModel = new TopupModel();
        
        $member = $memberModel->find($memberId);
        if (!$member) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Member tidak ditemukan'
            ]);
        }
        
        // Simpan transaksi top-up
        $topupModel->save([
            'id_member' => $memberId,
            'jumlah' => $jumlah,
            'metode_bayar' => $metodeBayar,
            'keterangan' => 'Top-up via Kasir'
        ]);
        
        // Update saldo member
        $newSaldo = $member['saldo'] + $jumlah;
        $memberModel->update($memberId, ['saldo' => $newSaldo]);
        
        return $this->response->setJSON([
            'success' => true,
            'message' => 'Top-up berhasil',
            'saldo_baru' => $newSaldo
        ]);
    }
    
    /**
     * Cetak struk pembayaran
     * Generate struk untuk transaksi yang sudah selesai
     *
     * @param int $sesiId ID sesi yang akan dicetak struknya
     * @return string View struk
     */
    public function cetakStruk($sesiId = null)
    {
        if (!$sesiId) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Sesi tidak ditemukan');
        }
        
        $sesiModel = new SesiModel();
        $sesi = $sesiModel
            ->select('sesi.*, station.nama_station, konsol.nama_konsol, member.nama as nama_member')
            ->join('station', 'station.id = sesi.station_id')
            ->join('konsol', 'konsol.id = station.id_konsol')
            ->join('member', 'member.id = sesi.id_member', 'left')
            ->find($sesiId);
        
        if (!$sesi) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Sesi tidak ditemukan');
        }
        
        return view('kasir/struk', ['sesi' => $sesi]);
    }
    
    /**
     * Get unpaid sessions untuk refresh data
     * 
     * @return \CodeIgniter\HTTP\ResponseInterface JSON response
     */
    public function getUnpaidSessions()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(405)->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ]);
        }
        
        $sesiModel = new SesiModel();
        $unpaidSessions = $sesiModel
            ->select('sesi.*, station.nama_station, konsol.nama_konsol')
            ->join('station', 'station.id = sesi.station_id')
            ->join('konsol', 'konsol.id = station.id_konsol')
            ->where('sesi.jenis_user', 'personal')
            ->where('sesi.waktu_berhenti IS NOT NULL') // Sesi sudah berhenti
            ->where('sesi.status_bayar', 'belum') // Belum dibayar
            ->orderBy('sesi.waktu_berhenti', 'DESC')
            ->limit(10)
            ->findAll();
        
        return $this->response->setJSON([
            'success' => true,
            'sessions' => $unpaidSessions
        ]);
    }

    /**
     * Process checkout - simpan ke database transaksi kasir
     *
     * @return \CodeIgniter\HTTP\ResponseInterface JSON response
     */
    public function processCheckout()
    {
        if (!$this->request->isAJAX() || $this->request->getMethod() !== 'POST') {
            return $this->response->setStatusCode(405)->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ]);
        }

        try {
            $cartItems = $this->request->getJSON(true)['cart_items'] ?? [];
            $metodePembayaran = $this->request->getJSON(true)['metode_pembayaran'] ?? 'tunai';
            $jumlahBayar = $this->request->getJSON(true)['jumlah_bayar'] ?? 0;

            if (empty($cartItems)) {
                return $this->response->setStatusCode(400)->setJSON([
                    'success' => false,
                    'message' => 'Keranjang kosong'
                ]);
            }

            // Hitung total
            $totalHarga = 0;
            $totalItem = 0;
            $detailItems = [];

            foreach ($cartItems as $item) {
                $totalHarga += $item['subtotal'];
                $totalItem += $item['quantity'] ?? 1;

                // Prepare detail item - konversi type ke jenis_item yang benar
                $jenisItem = $item['type'];
                if ($jenisItem === 'product') {
                    $jenisItem = 'product_sale'; // Convert frontend 'product' to database 'product_sale'
                }

                $detailItem = [
                    'jenis_item' => $jenisItem,
                    'item_id' => $item['sessionId'] ?? $item['memberId'] ?? $item['id'] ?? null, // Use 'id' for products
                    'nama_item' => $item['name'],
                    'harga_satuan' => $item['price'],
                    'quantity' => $item['quantity'] ?? 1,
                    'subtotal' => $item['subtotal'],
                    'detail_data' => json_encode([
                        'start_time' => $item['startTime'] ?? null,
                        'end_time' => $item['endTime'] ?? null,
                        'member_id' => $item['memberId'] ?? null,
                        'session_id' => $item['sessionId'] ?? null,
                        'product_id' => $item['id'] ?? null // Add product_id for products
                    ])
                ];
                $detailItems[] = $detailItem;
            }

            // Hitung kembalian untuk tunai
            $kembalian = 0;
            if ($metodePembayaran === 'tunai' && $jumlahBayar > 0) {
                $kembalian = max(0, $jumlahBayar - $totalHarga);
            }

            // Data transaksi header
            $transactionData = [
                'total_item' => $totalItem,
                'total_harga' => $totalHarga,
                'metode_bayar' => $metodePembayaran,
                'jumlah_bayar' => $metodePembayaran === 'tunai' ? $jumlahBayar : $totalHarga,
                'kembalian' => $kembalian,
                'kasir' => 'Kasir', // Bisa diambil dari session user
                'keterangan' => 'Transaksi kasir - ' . count($cartItems) . ' item'
            ];

            // Simpan transaksi
            $transaksiModel = new TransaksiKasirModel();
            $result = $transaksiModel->createTransactionWithDetails($transactionData, $detailItems);

            if (!$result['success']) {
                return $this->response->setStatusCode(500)->setJSON([
                    'success' => false,
                    'message' => 'Gagal menyimpan transaksi: ' . $result['message']
                ]);
            }

            // Process each item type
            $this->processCartItems($cartItems, $metodePembayaran, $jumlahBayar);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Transaksi berhasil diproses',
                'transaction_id' => $result['transaction_id'],
                'nomor_transaksi' => $result['nomor_transaksi'],
                'total_harga' => $totalHarga,
                'jumlah_bayar' => $metodePembayaran === 'tunai' ? $jumlahBayar : $totalHarga,
                'kembalian' => $kembalian,
                'metode_bayar' => $metodePembayaran
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Checkout error: ' . $e->getMessage());
            return $this->response->setStatusCode(500)->setJSON([
                'success' => false,
                'message' => 'Terjadi kesalahan sistem: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Process cart items - update database sesuai jenis item
     */
    private function processCartItems($cartItems, $metodePembayaran, $jumlahBayar)
    {
        $sesiModel = new SesiModel();
        $memberModel = new MemberModel();
        $topupModel = new TopupModel();

        foreach ($cartItems as $item) {
            switch ($item['type']) {
                case 'session_payment':
                    // Update sesi - set status_bayar = 'bayar'
                    $sesiModel->update($item['sessionId'], [
                        'status_bayar' => 'bayar',
                        'metode_bayar' => $metodePembayaran,
                        'jumlah_bayar' => $item['price'],
                        'waktu_bayar' => date('Y-m-d H:i:s')
                    ]);
                    break;

                case 'member_topup':
                    // Update saldo member
                    $member = $memberModel->find($item['memberId']);
                    if ($member) {
                        $newSaldo = $member['saldo'] + $item['price'];
                        $memberModel->update($item['memberId'], ['saldo' => $newSaldo]);

                        // Simpan ke tabel topup dengan kolom yang benar
                        $topupModel->insert([
                            'id_member' => $item['memberId'], // Gunakan id_member bukan member_id
                            'jumlah' => $item['price'],
                            'metode_bayar' => $metodePembayaran,
                            'tanggal_topup' => date('Y-m-d H:i:s')
                        ]);
                    }
                    break;

                case 'product':
                    // Update stok produk
                    $produkModel = new ProdukModel();
                    $produk = $produkModel->find($item['id']);
                    if ($produk) {
                        $stokBaru = $produk['stok'] - $item['quantity'];
                        if ($stokBaru >= 0) {
                            $produkModel->update($item['id'], ['stok' => $stokBaru]);
                        }
                    }
                    break;
            }
        }
    }

    /**
     * Get transaction list for daftar transaksi tab
     *
     * @return \CodeIgniter\HTTP\ResponseInterface JSON response
     */
    public function getTransaksiList()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(405)->setJSON([
                'success' => false,
                'message' => 'Method not allowed'
            ]);
        }

        try {
            $page = (int) ($this->request->getGet('page') ?? 1);
            $limit = (int) ($this->request->getGet('limit') ?? 10);
            $tanggal = $this->request->getGet('tanggal');

            $transaksiModel = new TransaksiKasirModel();

            // Build query
            $builder = $transaksiModel->builder();

            // Filter by date if provided
            if ($tanggal) {
                $builder->where('DATE(tanggal_transaksi)', $tanggal);
            }

            // Get total count for pagination
            $totalCount = $builder->countAllResults(false);

            // Get paginated results
            $offset = ($page - 1) * $limit;
            $transactions = $builder->orderBy('tanggal_transaksi', 'DESC')
                                  ->limit($limit, $offset)
                                  ->get()
                                  ->getResultArray();

            // Calculate pagination info
            $totalPages = ceil($totalCount / $limit);

            return $this->response->setJSON([
                'success' => true,
                'transactions' => $transactions,
                'pagination' => [
                    'current_page' => $page,
                    'total_pages' => $totalPages,
                    'total_count' => $totalCount,
                    'limit' => $limit
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Get transaction list error: ' . $e->getMessage());
            return $this->response->setStatusCode(500)->setJSON([
                'success' => false,
                'message' => 'Terjadi kesalahan sistem: ' . $e->getMessage()
            ]);
        }
    }
}
