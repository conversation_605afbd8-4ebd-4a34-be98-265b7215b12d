<?php namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class UpdateMemberTables extends Migration
{
    public function up()
    {
        // Cek apakah tabel topup_history sudah ada
        if (!$this->db->tableExists('topup_history')) {
            // Buat tabel topup_history
            $this->forge->addField([
                'id' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'unsigned' => true,
                    'auto_increment' => true
                ],
                'member_id' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'unsigned' => true
                ],
                'amount' => [
                    'type' => 'DECIMAL',
                    'constraint' => '10,2'
                ],
                'transaction_date' => [
                    'type' => 'DATETIME'
                ],
                'created_at' => [
                    'type' => 'DATETIME',
                    'null' => true
                ],
                'updated_at' => [
                    'type' => 'DATETIME',
                    'null' => true
                ]
            ]);
            $this->forge->addKey('id', true);
            $this->forge->addForeignKey('member_id', 'members', 'id', 'CASCADE', 'CASCADE');
            $this->forge->createTable('topup_history');
        }

        // Cek apakah tabel billing_sessions sudah ada
        if (!$this->db->tableExists('billing_sessions')) {
            // Buat tabel billing_sessions
            $this->forge->addField([
                'id' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'unsigned' => true,
                    'auto_increment' => true
                ],
                'member_id' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'unsigned' => true
                ],
                'start_time' => [
                    'type' => 'DATETIME'
                ],
                'end_time' => [
                    'type' => 'DATETIME',
                    'null' => true
                ],
                'duration' => [
                    'type' => 'INT',
                    'constraint' => 11,
                    'default' => 0
                ],
                'cost' => [
                    'type' => 'DECIMAL',
                    'constraint' => '10,2',
                    'default' => 0
                ],
                'status' => [
                    'type' => 'ENUM',
                    'constraint' => ['active', 'completed', 'cancelled'],
                    'default' => 'active'
                ],
                'created_at' => [
                    'type' => 'DATETIME',
                    'null' => true
                ],
                'updated_at' => [
                    'type' => 'DATETIME',
                    'null' => true
                ]
            ]);
            $this->forge->addKey('id', true);
            $this->forge->addForeignKey('member_id', 'members', 'id', 'CASCADE', 'CASCADE');
            $this->forge->createTable('billing_sessions');
        }

        // Cek dan tambah kolom yang mungkin belum ada di tabel members
        if ($this->db->tableExists('members')) {
            // Array kolom yang ingin dicek
            $fields = [
                'balance' => [
                    'type' => 'DECIMAL',
                    'constraint' => '10,2',
                    'default' => 0,
                    'after' => 'phone'
                ],
                'created_at' => [
                    'type' => 'DATETIME',
                    'null' => true,
                    'after' => 'balance'
                ],
                'updated_at' => [
                    'type' => 'DATETIME',
                    'null' => true,
                    'after' => 'created_at'
                ]
            ];

            // Cek setiap kolom
            foreach ($fields as $fieldName => $fieldSpecs) {
                if (!$this->db->fieldExists($fieldName, 'members')) {
                    $this->forge->addColumn('members', [$fieldName => $fieldSpecs]);
                }
            }
        }
    }

    public function down()
    {
        // Jika ingin menghapus tabel (hati-hati dengan data yang sudah ada)
        // $this->forge->dropTable('billing_sessions', true);
        // $this->forge->dropTable('topup_history', true);
        
        // Atau jika hanya ingin menghapus kolom yang ditambahkan
        if ($this->db->fieldExists('balance', 'members')) {
            $this->forge->dropColumn('members', 'balance');
        }
        if ($this->db->fieldExists('created_at', 'members')) {
            $this->forge->dropColumn('members', 'created_at');
        }
        if ($this->db->fieldExists('updated_at', 'members')) {
            $this->forge->dropColumn('members', 'updated_at');
        }
    }
}