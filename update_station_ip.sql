-- <PERSON>ript untuk update IP Address Station 2
-- Ganti IP address Station 2 dari ************ ke ************

UPDATE station 
SET ip_address = '************', 
    updated_at = NOW() 
WHERE nama_station = 'Station 2';

-- <PERSON><PERSON><PERSON><PERSON><PERSON>
SELECT id, nama_station, ip_address, updated_at 
FROM station 
WHERE nama_station = 'Station 2';

-- <PERSON><PERSON> ingin melihat semua station dan IP mereka
SELECT s.id, s.nama_station, s.ip_address, k.nama_konsol, s.updated_at
FROM station s
JOIN konsol k ON k.id = s.id_konsol
ORDER BY s.nama_station;
