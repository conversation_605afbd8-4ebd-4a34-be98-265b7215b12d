-- Script untuk memperbaiki data sesi yang sudah ada
-- Jalankan di phpMyAdmin atau MySQL client

-- 1. Update sesi yang sudah berhenti tapi status kosong
UPDATE sesi 
SET status = 'berhenti' 
WHERE waktu_berhenti IS NOT NULL 
AND (status = '' OR status IS NULL);

-- 2. Update sesi yang sudah berhenti tapi harga_total NULL (untuk sesi personal)
-- Asumsi: harga per menit = 2000, hitung berdasarkan durasi
UPDATE sesi 
SET harga_total = TIMESTAMPDIFF(MINUTE, waktu_mulai, waktu_berhenti) * 2000
WHERE jenis_user = 'personal' 
AND waktu_berhenti IS NOT NULL 
AND (harga_total IS NULL OR harga_total = 0);

-- 3. Update durasi_sisa untuk sesi personal yang sudah selesai
UPDATE sesi 
SET durasi_sisa = 0 
WHERE jenis_user = 'personal' 
AND waktu_berhen<PERSON> IS NOT NULL 
AND durasi_sisa IS NULL;

-- 4. Set status_bayar berdasarkan metode_bayar yang ada
UPDATE sesi 
SET status_bayar = 'bayar' 
WHERE metode_bayar IS NOT NULL AND metode_bayar != '';

UPDATE sesi 
SET status_bayar = 'belum' 
WHERE metode_bayar IS NULL OR metode_bayar = '';

-- 5. Contoh data untuk testing (opsional)
-- INSERT INTO sesi (station_id, jenis_user, waktu_mulai, waktu_berhenti, harga_total, status, durasi_sisa, sisa_saldo, status_bayar) VALUES
-- (1, 'personal', DATE_SUB(NOW(), INTERVAL 2 HOUR), DATE_SUB(NOW(), INTERVAL 1 HOUR), 120000, 'berhenti', 0, 0, 'belum'),
-- (2, 'personal', DATE_SUB(NOW(), INTERVAL 3 HOUR), DATE_SUB(NOW(), INTERVAL 1.5 HOUR), 180000, 'berhenti', 0, 0, 'belum'),
-- (3, 'personal', DATE_SUB(NOW(), INTERVAL 4 HOUR), DATE_SUB(NOW(), INTERVAL 2 HOUR), 240000, 'berhenti', 0, 0, 'belum');
