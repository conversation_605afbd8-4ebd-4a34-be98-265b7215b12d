#include <ESP8266WiFi.h>
#include <ESP8266HTTPClient.h>
#include <ArduinoJson.h>
#include <TFT_eSPI.h>
#include <SPI.h>

// Konfigurasi WiFi
const char* ssid = "PlaySphere";
const char* password = "1234554321";

// Server
const char* serverHost = "*************";  // IP Server
const int serverPort = 80;
const char* basePath = "/playsphere";  // Base path aplikasi

// Pin Relay
const int RELAY_PIN = D1;

// Setup TFT
TFT_eSPI tft = TFT_eSPI();

// Ukuran layar
#define SCREEN_WIDTH 240
#define SCREEN_HEIGHT 240

// Definisi Warna RGB565 untuk ST7789 - DIPERBAIKI
// Format RGB565: RRRRRGGGGGGBBBBB (5-6-5 bit)
#define BLACK        0x0000  // Hitam
#define WHITE        0xFFFF  // Putih
#define RED          0xF800  // Merah murni
#define GREEN        0x07E0  // Hijau murni
#define BLUE         0x001F  // Biru murni
#define YELLOW       0xFFE0  // Kuning
#define MAGENTA      0xF81F  // Magenta
#define CYAN         0x07FF  // Cyan
#define ORANGE       0xFD20  // Orange
#define PURPLE       0x780F  // Purple
#define GRAY         0x8410  // Gray
#define DARK_GRAY    0x4208  // Dark Gray
#define LIGHT_GRAY   0xC618  // Light Gray

// Warna Card PlaySphere (disesuaikan dengan dashboard)
#define CARD_GREEN   0x0400  // Hijau gelap untuk active (RGB: 0,128,0)
#define CARD_BLUE    0x001F  // Biru untuk paused (RGB: 0,0,255)
#define CARD_GRAY    0x7BEF  // Abu-abu terang untuk standby (RGB: 128,128,128)

// Warna alternatif jika yang atas tidak sesuai
#define ALT_GREEN    0x07E0  // Hijau terang
#define ALT_BLUE     0x001F  // Biru terang
#define ALT_GRAY     0x8410  // Abu-abu gelap

// Text colors
#define TEXT_WHITE   0xFFFF
#define TEXT_BLACK   0x0000

// Status colors
#define STATUS_RED   0xF800
#define STATUS_ORANGE 0xFD20

// Variabel Global
String channelId = "";
String channelName = "";
String psType = "";
String playerName = "";
String timer = "00:00:00";
String price = "Rp 0";
bool isWifiConnected = false;
bool isPersonal = true;  // Track apakah personal atau member

// Timer variables untuk countdown client-side
unsigned long lastTimerUpdate = 0;
int timerHours = 0;
int timerMinutes = 0;
int timerSeconds = 0;
bool timerRunning = false;

// Relay control
bool relayState = false;
String lastRelayCommand = "OFF";

String currentScreen = "";  // Untuk tracking screen yang sedang ditampilkan
String lastPlayerName = "";
String lastTimer = "";
String lastPrice = "";
bool lastPauseState = false;

// Update interval
unsigned long lastUpdateTime = 0;
const long UPDATE_INTERVAL = 1000;  // Update setiap 1 detik

// Helper Functions
void drawCenteredText(String text, int y, int textSize, uint16_t textColor = TEXT_WHITE, uint16_t bgColor = 0) {
    tft.setTextSize(textSize);
    tft.setTextColor(textColor);
    int textWidth = text.length() * 6 * textSize;
    int x = (SCREEN_WIDTH - textWidth) / 2;

    // Hapus area teks sebelum menggambar jika background color diberikan
    if (bgColor != 0) {
        int textHeight = 8 * textSize;  // 8 adalah tinggi dasar font
        tft.fillRect(0, y - 2, SCREEN_WIDTH, textHeight + 4, bgColor);
    }

    tft.setCursor(x, y);
    tft.println(text);
}

// Timer Functions
void parseTimer(String timerStr) {
    // Parse HH:MM:SS format
    int firstColon = timerStr.indexOf(':');
    int secondColon = timerStr.lastIndexOf(':');

    if (firstColon > 0 && secondColon > firstColon) {
        timerHours = timerStr.substring(0, firstColon).toInt();
        timerMinutes = timerStr.substring(firstColon + 1, secondColon).toInt();
        timerSeconds = timerStr.substring(secondColon + 1).toInt();

        Serial.println("Timer parsed: " + String(timerHours) + "h " + String(timerMinutes) + "m " + String(timerSeconds) + "s");
    }
}

String formatTimer() {
    return String(timerHours < 10 ? "0" : "") + String(timerHours) + ":" +
           String(timerMinutes < 10 ? "0" : "") + String(timerMinutes) + ":" +
           String(timerSeconds < 10 ? "0" : "") + String(timerSeconds);
}

void updateTimerCountdown() {
    if (!timerRunning || isPersonal) return;

    unsigned long currentTime = millis();
    if (currentTime - lastTimerUpdate >= 1000) {  // Update setiap detik
        lastTimerUpdate = currentTime;

        // Countdown untuk member
        if (timerSeconds > 0) {
            timerSeconds--;
        } else if (timerMinutes > 0) {
            timerMinutes--;
            timerSeconds = 59;
        } else if (timerHours > 0) {
            timerHours--;
            timerMinutes = 59;
            timerSeconds = 59;
        } else {
            // Timer habis
            timerRunning = false;
            Serial.println("Timer reached 00:00:00 - Time's up!");
            controlRelay(false);  // Matikan relay
            showTimeUpScreen();
            return;
        }

        timer = formatTimer();
        Serial.println("Timer countdown: " + timer);
    }
}

// Relay Control Functions
void controlRelay(bool state) {
    if (relayState != state) {
        relayState = state;
        digitalWrite(RELAY_PIN, state ? HIGH : LOW);
        Serial.println("Relay " + String(state ? "ON" : "OFF") + " (Pin D1: " + String(state ? "HIGH" : "LOW") + ")");
    }
}

void updateRelayFromServer(String relayCommand) {
    if (relayCommand != lastRelayCommand) {
        lastRelayCommand = relayCommand;
        bool newState = (relayCommand == "ON");
        controlRelay(newState);
        Serial.println("Relay command from server: " + relayCommand);
    }
}

void drawRoundedRect(int x, int y, int w, int h, int r, uint16_t color) {
    // Gambar rounded rectangle sederhana
    tft.fillRect(x + r, y, w - 2*r, h, color);
    tft.fillRect(x, y + r, w, h - 2*r, color);
    tft.fillCircle(x + r, y + r, r, color);
    tft.fillCircle(x + w - r - 1, y + r, r, color);
    tft.fillCircle(x + r, y + h - r - 1, r, color);
    tft.fillCircle(x + w - r - 1, y + h - r - 1, r, color);
}

void drawButton(int x, int y, int w, int h, String text, uint16_t bgColor, uint16_t textColor) {
    drawRoundedRect(x, y, w, h, 8, bgColor);
    tft.setTextColor(textColor);
    tft.setTextSize(1);
    int textWidth = text.length() * 6;
    int textX = x + (w - textWidth) / 2;
    int textY = y + (h - 8) / 2;
    tft.setCursor(textX, textY);
    tft.print(text);
}

// WiFi Icon
void drawWiFiIcon(int x, int y) {
    if (isWifiConnected) {
        // WiFi icon dengan 3 bar
        tft.fillRect(x-8, y+6, 3, 2, TEXT_WHITE);
        tft.fillRect(x-4, y+3, 3, 5, TEXT_WHITE);
        tft.fillRect(x, y, 3, 8, TEXT_WHITE);
        tft.fillRect(x+4, y-3, 3, 11, TEXT_WHITE);
    } else {
        // WiFi icon dengan X
        tft.drawLine(x-8, y-8, x+8, y+8, RED);
        tft.drawLine(x-8, y+8, x+8, y-8, RED);
        tft.drawRect(x-8, y-8, 16, 16, RED);
    }
}

// Function untuk memperbarui WiFi icon tanpa menghapus seluruh layar
void updateWiFiIcon() {
    static bool lastWifiStatus = false;

    if (isWifiConnected != lastWifiStatus) {
        // Hapus area WiFi icon saja
        tft.fillRect(SCREEN_WIDTH - 25, 5, 20, 20, BLACK);
        drawWiFiIcon(SCREEN_WIDTH - 15, 10);
        lastWifiStatus = isWifiConnected;
    }
}

// Screen Functions
void showSplashScreen() {
    tft.fillScreen(BLACK);
    tft.setTextColor(TEXT_WHITE);

    // Logo startup dengan styling yang lebih baik
    drawCenteredText("LANGIT INOVASI", SCREEN_HEIGHT/2 - 40, 2, TEXT_WHITE);
    drawCenteredText("PlaySphere IoT", SCREEN_HEIGHT/2 - 10, 1, GRAY);
    drawCenteredText("Device", SCREEN_HEIGHT/2 + 5, 1, GRAY);

    // Loading animation
    for(int i = 0; i < 3; i++) {
        tft.fillCircle(SCREEN_WIDTH/2 - 20 + i*20, SCREEN_HEIGHT/2 + 30, 3, TEXT_WHITE);
        delay(500);
    }
    delay(1500);
}

void connectToWiFi() {
    tft.fillScreen(BLACK);
    drawCenteredText("Menghubungkan WiFi", SCREEN_HEIGHT/2 - 30, 2, TEXT_WHITE);
    drawCenteredText("PlaySphere Network", SCREEN_HEIGHT/2, 1, GRAY);

    WiFi.begin(ssid, password);

    int dotY = SCREEN_HEIGHT/2 + 30;
    int dotX = SCREEN_WIDTH/2 - 30;
    int dotCount = 0;

    while (WiFi.status() != WL_CONNECTED) {
        delay(500);
        tft.fillCircle(dotX + (dotCount * 15), dotY, 3, TEXT_WHITE);
        dotCount++;
        if(dotCount > 4) {
            tft.fillRect(SCREEN_WIDTH/2 - 35, dotY - 5, 70, 10, BLACK);
            dotCount = 0;
        }
    }

    isWifiConnected = true;
    tft.fillScreen(BLACK);
    drawCenteredText("WiFi Terhubung", SCREEN_HEIGHT/2 - 40, 2, GREEN);
    drawCenteredText("IP Address:", SCREEN_HEIGHT/2 - 10, 1, GRAY);
    drawCenteredText(WiFi.localIP().toString(), SCREEN_HEIGHT/2 + 10, 1, TEXT_WHITE);
    drawCenteredText("Menghubungi Server...", SCREEN_HEIGHT/2 + 35, 1, YELLOW);
    delay(3000);
}

void showStandbyScreen() {
    // Cek jika screen sudah sama, tidak perlu update
    if (currentScreen == "standby") {
        return;
    }

    currentScreen = "standby";

    // Clear status lama
    lastPlayerName = "";
    lastTimer = "";
    lastPrice = "";
    lastPauseState = false;

    // Background dengan warna card abu-abu (standby) - warna diperbaiki
    tft.fillScreen(BLACK);
    drawRoundedRect(10, 10, SCREEN_WIDTH-20, SCREEN_HEIGHT-20, 15, CARD_GRAY);

    // Header dengan nama station - ukuran teks diperbesar
    tft.setTextColor(TEXT_WHITE);
    tft.setTextSize(2);
    tft.setCursor(20, 25);
    tft.print(channelName);

    // WiFi icon di pojok kanan atas
    drawWiFiIcon(SCREEN_WIDTH - 25, 25);

    // PS Type di tengah dengan font besar
    drawCenteredText(psType, 70, 4, TEXT_WHITE);

    // Timer standby - ukuran diperbesar
    drawCenteredText("00:00:00", 120, 4, TEXT_WHITE);

    // Status text - ukuran diperbesar dan warna diperbaiki
    drawCenteredText("PlaySphere", 170, 2, TEXT_WHITE);
    drawCenteredText("Ready", 195, 2, TEXT_WHITE);
}

void showInUseScreen() {
    bool needFullRefresh = false;

    // Cek apakah perlu full refresh
    if (currentScreen != "in_use") {
        needFullRefresh = true;
        currentScreen = "in_use";
    }

    if (needFullRefresh) {
        // Background dengan warna card hijau (aktif) - warna diperbaiki
        tft.fillScreen(BLACK);
        drawRoundedRect(10, 10, SCREEN_WIDTH-20, SCREEN_HEIGHT-20, 15, CARD_GREEN);

        // Header dengan nama station - ukuran teks diperbesar
        tft.setTextColor(TEXT_WHITE);
        tft.setTextSize(2);
        tft.setCursor(20, 25);
        tft.print(channelName);

        // WiFi icon di pojok kanan atas
        drawWiFiIcon(SCREEN_WIDTH - 25, 25);

        // PS Type (static) - ukuran diperbesar
        drawCenteredText(psType, 55, 3, TEXT_WHITE);
    }

    // Update player name hanya jika berubah - ukuran teks diperbesar
    if (playerName != lastPlayerName) {
        // Clear area nama player
        tft.fillRect(15, 80, SCREEN_WIDTH-30, 30, CARD_GREEN);
        drawCenteredText(playerName, 85, 3, TEXT_WHITE);
        lastPlayerName = playerName;
    }

    // Update timer hanya jika berubah (timer besar di tengah) - ukuran diperbesar
    String currentTimer = formatTimer();
    if (currentTimer != lastTimer) {
        // Clear area timer
        tft.fillRect(15, 120, SCREEN_WIDTH-30, 45, CARD_GREEN);
        drawCenteredText(currentTimer, 135, 4, TEXT_WHITE);
        lastTimer = currentTimer;
    }

    // Update price hanya jika berubah - ukuran diperbesar
    if (price != lastPrice) {
        // Clear area price
        tft.fillRect(15, 175, SCREEN_WIDTH-30, 30, CARD_GREEN);
        drawCenteredText(price, 180, 3, TEXT_WHITE);
        lastPrice = price;
    }
}

void showPausedScreen() {
    bool needFullRefresh = false;

    if (currentScreen != "paused" || !lastPauseState) {
        needFullRefresh = true;
        currentScreen = "paused";
        lastPauseState = true;
    }

    if (needFullRefresh) {
        // Background dengan warna card biru (paused) - warna diperbaiki
        tft.fillScreen(BLACK);
        drawRoundedRect(10, 10, SCREEN_WIDTH-20, SCREEN_HEIGHT-20, 15, CARD_BLUE);

        // Header dengan nama station - ukuran teks diperbesar
        tft.setTextColor(TEXT_WHITE);
        tft.setTextSize(2);
        tft.setCursor(20, 25);
        tft.print(channelName);

        // WiFi icon di pojok kanan atas
        drawWiFiIcon(SCREEN_WIDTH - 25, 25);

        // PS Type & Player - ukuran diperbesar
        drawCenteredText(psType, 55, 3, TEXT_WHITE);
        drawCenteredText(playerName, 85, 3, TEXT_WHITE);

        // PAUSED text dengan warna mencolok - ukuran diperbesar
        drawCenteredText("PAUSED", 125, 4, YELLOW);

        // Price - ukuran diperbesar
        drawCenteredText(price, 165, 3, TEXT_WHITE);
    }
}

void showTimeUpScreen() {
    // Background dengan warna card merah (waktu habis)
    tft.fillScreen(BLACK);
    drawRoundedRect(10, 10, SCREEN_WIDTH-20, SCREEN_HEIGHT-20, 15, RED);

    // Header dengan nama station
    tft.setTextColor(TEXT_WHITE);
    tft.setTextSize(2);
    tft.setCursor(20, 25);
    tft.print(channelName);

    // WiFi icon di pojok kanan atas
    drawWiFiIcon(SCREEN_WIDTH - 25, 25);

    // PS Type & Player
    drawCenteredText(psType, 55, 3, TEXT_WHITE);
    drawCenteredText(playerName, 85, 2, TEXT_WHITE);

    // WAKTU HABIS dengan animasi berkedip
    for(int i = 0; i < 5; i++) {
        drawCenteredText("WAKTU HABIS", 125, 3, YELLOW);
        delay(300);
        tft.fillRect(15, 120, SCREEN_WIDTH-30, 30, RED);
        delay(300);
    }
    drawCenteredText("WAKTU HABIS", 125, 3, YELLOW);

    // Price
    drawCenteredText(price, 165, 2, TEXT_WHITE);

    // Countdown untuk kembali ke standby
    for(int countdown = 5; countdown > 0; countdown--) {
        tft.fillRect(15, 195, SCREEN_WIDTH-30, 25, RED);
        drawCenteredText("Kembali dalam " + String(countdown), 200, 1, TEXT_WHITE);
        delay(1000);
    }
}

void showStoppedScreen() {
    // Background dengan warna card orange (stopped)
    tft.fillScreen(BLACK);
    drawRoundedRect(10, 10, SCREEN_WIDTH-20, SCREEN_HEIGHT-20, 15, ORANGE);

    // Header dengan nama station
    tft.setTextColor(TEXT_WHITE);
    tft.setTextSize(2);
    tft.setCursor(20, 25);
    tft.print(channelName);

    // WiFi icon di pojok kanan atas
    drawWiFiIcon(SCREEN_WIDTH - 25, 25);

    // PS Type & Player
    drawCenteredText(psType, 55, 3, TEXT_WHITE);
    drawCenteredText(playerName, 85, 2, TEXT_WHITE);

    // SESI DIHENTIKAN dengan animasi
    for(int i = 0; i < 3; i++) {
        drawCenteredText("SESI DIHENTIKAN", 125, 2, TEXT_WHITE);
        delay(400);
        tft.fillRect(15, 120, SCREEN_WIDTH-30, 25, ORANGE);
        delay(400);
    }
    drawCenteredText("SESI DIHENTIKAN", 125, 2, TEXT_WHITE);

    // Final price
    drawCenteredText("Total: " + price, 165, 2, TEXT_WHITE);

    // Countdown untuk kembali ke standby
    for(int countdown = 3; countdown > 0; countdown--) {
        tft.fillRect(15, 195, SCREEN_WIDTH-30, 25, ORANGE);
        drawCenteredText("Kembali dalam " + String(countdown), 200, 1, TEXT_WHITE);
        delay(1000);
    }
}

void connectToServer() {
    WiFiClient client;
    HTTPClient http;

    String url = "http://" + String(serverHost) + ":" + String(serverPort) + String(basePath) + "/api/channel/find";

    Serial.println("Connecting to server: " + url);

    http.begin(client, url);
    
    // Add timeout
    http.setTimeout(10000);  // 10 seconds timeout

    int httpCode = http.GET();
    String payload = http.getString();
    Serial.println("HTTP Response code: " + String(httpCode));
    Serial.println("Response payload: " + payload);

    if (httpCode > 0) {
        if (httpCode == HTTP_CODE_OK) {
            StaticJsonDocument<1024> doc;
            DeserializationError error = deserializeJson(doc, payload);

            if (!error && doc["success"]) {
                channelId = doc["channel_id"].as<String>();
                channelName = doc["data"]["channelName"].as<String>();
                psType = doc["data"]["psType"].as<String>();
                
                Serial.println("Connected as Channel ID: " + channelId);
                Serial.println("Channel Name: " + channelName);
                Serial.println("PS Type: " + psType);

                showStandbyScreen();
            } else {
                Serial.println("Failed to get channel ID");
                if (error) {
                    Serial.println("JSON Error: " + String(error.c_str()));
                }
                tft.fillScreen(NAVY);
                drawCenteredText("ERROR:", SCREEN_HEIGHT/2 - 20, 2);
                drawCenteredText("Channel tidak", SCREEN_HEIGHT/2 + 10, 2);
                drawCenteredText("terdaftar", SCREEN_HEIGHT/2 + 40, 2);
            }
        } else if (httpCode == HTTP_CODE_NOT_FOUND) {
            tft.fillScreen(NAVY);
            drawCenteredText("ERROR:", SCREEN_HEIGHT/2 - 20, 2);
            drawCenteredText("IP tidak", SCREEN_HEIGHT/2 + 10, 2);
            drawCenteredText("terdaftar", SCREEN_HEIGHT/2 + 40, 2);
        } else {
            tft.fillScreen(NAVY);
            drawCenteredText("ERROR:", SCREEN_HEIGHT/2 - 20, 2);
            drawCenteredText("Server Error", SCREEN_HEIGHT/2 + 10, 2);
            drawCenteredText(String(httpCode), SCREEN_HEIGHT/2 + 40, 2);
        }
    } else {
        Serial.println("Connection failed: " + http.errorToString(httpCode));
        tft.fillScreen(NAVY);
        drawCenteredText("ERROR:", SCREEN_HEIGHT/2 - 20, 2);
        drawCenteredText("Koneksi", SCREEN_HEIGHT/2 + 10, 2);
        drawCenteredText("Gagal", SCREEN_HEIGHT/2 + 40, 2);
    }

    http.end();
}

void updateChannelStatus() {
    if (channelId.isEmpty()) return;

    WiFiClient client;
    HTTPClient http;

    String url = "http://" + String(serverHost) + ":" + String(serverPort) + String(basePath) + "/api/channel/status/" + channelId;

    Serial.println("Getting status from: " + url);

    http.begin(client, url);
    http.addHeader("Content-Type", "application/json");
    
    int httpCode = http.GET();

    if (httpCode == HTTP_CODE_OK) {
        String payload = http.getString();
        Serial.println("Response: " + payload);

        StaticJsonDocument<1024> doc;
        DeserializationError error = deserializeJson(doc, payload);

        if (!error && doc["success"]) {
            JsonObject data = doc["data"];
            String status = data["status"].as<String>();
            String relay = data["relay"].as<String>();
            
            Serial.println("Status: " + status);
            Serial.println("Relay: " + relay);

            // Update relay state
            digitalWrite(RELAY_PIN, relay == "ON" ? HIGH : LOW);

            if (status == "in_use") {
                playerName = data["playerName"].as<String>();
                String newTimer = data["timer"].as<String>();
                price = data["price"].as<String>();
                bool isPaused = data["isPaused"].as<bool>();
                isPersonal = data["isPersonal"].as<bool>();
                String relayCommand = data["relay"].as<String>();

                // Update relay berdasarkan command dari server
                updateRelayFromServer(relayCommand);

                // Update timer hanya jika berbeda dari server (sync)
                if (newTimer != timer) {
                    timer = newTimer;
                    parseTimer(timer);
                    lastTimerUpdate = millis();
                    Serial.println("Timer synced from server: " + timer);
                }

                // Start/stop timer berdasarkan status
                timerRunning = !isPaused;

                // Cek jika waktu habis untuk member (countdown mencapai 00:00:00)
                if (!isPersonal && timer == "00:00:00") {
                    Serial.println("Time's up! Showing time up screen");
                    timerRunning = false;
                    controlRelay(false);  // Matikan relay
                    showTimeUpScreen();
                    return;
                }

                if (isPaused) {
                    Serial.println("Session paused - Relay should be OFF");
                    showPausedScreen();
                } else {
                    Serial.println("Session active - Relay should be ON");
                    Serial.println("Player: " + playerName);
                    Serial.println("Timer: " + timer);
                    Serial.println("Price: " + price);
                    Serial.println("Type: " + String(isPersonal ? "Personal" : "Member"));
                    Serial.println("Relay: " + relayCommand);
                    showInUseScreen();
                }
            } else if (status == "stopped") {
                // Jika sesi dihentikan manual
                Serial.println("Session stopped! Showing stopped screen");
                timerRunning = false;
                controlRelay(false);  // Matikan relay
                showStoppedScreen();
            } else {
                // Standby mode
                timerRunning = false;
                controlRelay(false);  // Matikan relay
                showStandbyScreen();
            }
        }
    } else {
        Serial.println("Error getting status: " + String(httpCode));
        // If error, try to reconnect
        connectToServer();
    }

    http.end();
}

void setup() {
    Serial.begin(115200);
    Serial.println("\n=== PlaySphere IoT Device Starting ===");

    // Setup pin relay (D1)
    pinMode(RELAY_PIN, OUTPUT);
    digitalWrite(RELAY_PIN, LOW);
    Serial.println("Relay pin configured on D1");

    // Inisialisasi TFT dengan konfigurasi pin yang benar
    // Pin Configuration LCD ST7789 TFT 240 x 240 FOR WEMOS D1 MINI:
    // SCL: D5 (GPIO 14)
    // SDA: D7 (GPIO 13)
    // RES: D4 (GPIO 2)
    // DC: D3 (GPIO 0)
    // BLK: D8 (GPIO 15)
    tft.init();
    tft.setRotation(2);  // SET ROTATION (2) sesuai permintaan
    tft.invertDisplay(false);
    tft.fillScreen(BLACK);
    tft.setTextColor(TEXT_WHITE, BLACK);

    Serial.println("TFT Display initialized with rotation 2");
    Serial.println("Pin configuration:");
    Serial.println("- SCL: D5 (GPIO 14)");
    Serial.println("- SDA: D7 (GPIO 13)");
    Serial.println("- RES: D4 (GPIO 2)");
    Serial.println("- DC: D3 (GPIO 0)");
    Serial.println("- BLK: D8 (GPIO 15)");

    // Tampilkan splash screen dengan logo Langit Inovasi
    showSplashScreen();

    // Hubungkan ke WiFi PlaySphere
    connectToWiFi();

    // Hubungkan ke server PlaySphere
    connectToServer();

    Serial.println("=== Setup complete! ===");
}

void loop() {
    // Check WiFi connection
    if (WiFi.status() != WL_CONNECTED) {
        isWifiConnected = false;
        Serial.println("[WIFI] Connection lost! Attempting to reconnect...");

        // Show WiFi disconnected on display
        tft.fillScreen(BLACK);
        drawCenteredText("WiFi Terputus", SCREEN_HEIGHT/2 - 20, 2, RED);
        drawCenteredText("Menghubungkan...", SCREEN_HEIGHT/2 + 10, 1, YELLOW);

        connectToWiFi();
        return;
    }

    // Update WiFi icon status
    updateWiFiIcon();

    // If no channel ID, try to connect to server
    if (channelId.isEmpty()) {
        static unsigned long lastRetryTime = 0;
        unsigned long currentTime = millis();
        if (currentTime - lastRetryTime >= 5000) { // Try every 5 seconds
            lastRetryTime = currentTime;
            Serial.println("[SERVER] No channel ID, retrying server connection...");
            connectToServer();
        }
        return;
    }

    // Update timer countdown setiap detik (client-side)
    updateTimerCountdown();

    // Update channel status every 5 seconds (reduced frequency)
    unsigned long currentMillis = millis();
    if (currentMillis - lastUpdateTime >= 5000) { // Update every 5 seconds
        lastUpdateTime = currentMillis;
        updateChannelStatus();

        // Debug info setiap 15 detik
        static unsigned long lastDebugTime = 0;
        if (currentMillis - lastDebugTime >= 15000) {
            lastDebugTime = currentMillis;
            Serial.println("\n=== DEBUG STATUS ===");
            Serial.println("Channel: " + channelId + " | Screen: " + currentScreen);
            Serial.println("Timer: " + timer + " | Running: " + String(timerRunning));
            Serial.println("Personal: " + String(isPersonal) + " | Player: " + playerName);
            Serial.println("Relay State: " + String(relayState ? "ON" : "OFF") + " | Pin D1: " + String(digitalRead(RELAY_PIN) ? "HIGH" : "LOW"));
            Serial.println("Last Relay Command: " + lastRelayCommand);
            Serial.println("Free heap: " + String(ESP.getFreeHeap()) + " bytes");
            Serial.println("WiFi RSSI: " + String(WiFi.RSSI()) + " dBm");
            Serial.println("===================\n");
        }
    }
}