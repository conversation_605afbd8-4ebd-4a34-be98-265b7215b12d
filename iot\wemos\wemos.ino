#include <ESP8266WiFi.h>
#include <ESP8266HTTPClient.h>
#include <ArduinoJson.h>
#include <TFT_eSPI.h>
#include <SPI.h>

// Konfigurasi WiFi
const char* ssid = "PlaySphere";
const char* password = "1234554321";

// Server
const char* serverHost = "*************";  // IP Server
const int serverPort = 80;

// Pin Relay
const int RELAY_PIN = D1;

// Setup TFT
TFT_eSPI tft = TFT_eSPI();

// Ukuran layar
#define SCREEN_WIDTH 240
#define SCREEN_HEIGHT 240

// Definisi Warna (disesuaikan untuk ST7789)
#define BLACK        0x0000
#define WHITE        0xFFFF
#define BLUE         0xF800
#define GREEN        0x07E0
#define RED          0x001F
#define YELLOW       0x07FF
#define MAGENTA      0xF81F
#define CYAN         0xFFE0
#define ORANGE       0x053F
#define PINK         0xF81F
#define PURPLE       0x780F
#define LIME         0x07E0
#define NAVY         0x8000
#define TEAL        0x0410
#define BROWN       0x0411
#define MAROON      0x0010
#define OLIVE       0x0430
#define SILVER      0xC618
#define GRAY        0x8410
#define DARK_GRAY   0x4208
#define LIGHT_BLUE  0xFD20
#define DARK_GREEN  0x03E0
#define DARK_RED    0x000F
#define GOLD        0x06BF

// Variabel Global
String channelId = "";
String channelName = "";
String psType = "";
String playerName = "";
String timer = "00:00:00";
String price = "Rp 0";
bool isWifiConnected = false;

String currentScreen = "";  // Untuk tracking screen yang sedang ditampilkan
String lastPlayerName = "";
String lastTimer = "";
String lastPrice = "";
bool lastPauseState = false;

// Update interval
unsigned long lastUpdateTime = 0;
const long UPDATE_INTERVAL = 1000;  // Update setiap 1 detik

// Helper Functions
void drawCenteredText(String text, int y, int textSize) {
    tft.setTextSize(textSize);
    int textWidth = text.length() * 6 * textSize;
    int x = (SCREEN_WIDTH - textWidth) / 2;
    
    // Hapus area teks sebelum menggambar
    int textHeight = 8 * textSize;  // 8 adalah tinggi dasar font
    tft.fillRect(0, y - 2, SCREEN_WIDTH, textHeight + 4, NAVY);
    
    tft.setCursor(x, y);
    tft.println(text);
}

// WiFi Icon
void drawWiFiIcon(int x, int y) {
    if (isWifiConnected) {
        tft.drawCircle(x, y, 8, GREEN);
        tft.drawCircle(x, y, 5, GREEN);
        tft.drawCircle(x, y, 2, GREEN);
    } else {
        tft.drawCircle(x, y, 8, RED);
        tft.drawLine(x-8, y-8, x+8, y+8, RED);
    }
}

// Function untuk memperbarui WiFi icon tanpa menghapus seluruh layar
void updateWiFiIcon() {
    static bool lastWifiStatus = false;
    
    if (isWifiConnected != lastWifiStatus) {
        // Hapus area WiFi icon saja
        tft.fillRect(SCREEN_WIDTH - 25, 5, 20, 20, BLACK);
        drawWiFiIcon(SCREEN_WIDTH - 15, 10);
        lastWifiStatus = isWifiConnected;
    }
}

// Screen Functions
void showSplashScreen() {
    tft.fillScreen(NAVY);
    drawCenteredText("Langit Inovasi", SCREEN_HEIGHT/2 - 30, 2);
    delay(3000);
}

void connectToWiFi() {
    tft.fillScreen(NAVY);
    drawCenteredText("Menghubungkan", SCREEN_HEIGHT/2 - 30, 2);
    drawCenteredText("Wifi", SCREEN_HEIGHT/2, 2);
    
    WiFi.begin(ssid, password);
    
    int dotY = SCREEN_HEIGHT/2 + 30;
    int dotX = SCREEN_WIDTH/2;
    
    while (WiFi.status() != WL_CONNECTED) {
        delay(500);
        tft.fillCircle(dotX, dotY, 3, WHITE);
        dotX += 12;
        if(dotX > SCREEN_WIDTH/2 + 36) {
            tft.fillRect(SCREEN_WIDTH/2 - 18, dotY - 3, 73, 6, NAVY);
            dotX = SCREEN_WIDTH/2;
        }
    }
    
    isWifiConnected = true;
    tft.fillScreen(NAVY);
    drawCenteredText("Terhubung", SCREEN_HEIGHT/2 - 40, 2);
    drawCenteredText("IP:", SCREEN_HEIGHT/2, 2);
    drawCenteredText(WiFi.localIP().toString(), SCREEN_HEIGHT/2 + 25, 2);
    delay(4000);
}

void showStandbyScreen() {
    // Cek jika screen sudah sama, tidak perlu update
    if (currentScreen == "standby") {
        return;
    }
    
    currentScreen = "standby";
    
    // Clear status lama
    lastPlayerName = "";
    lastTimer = "";
    lastPrice = "";
    lastPauseState = false;
    
    tft.fillScreen(NAVY);
    
    // Header
    tft.setTextSize(2);
    tft.setCursor(5, 5);
    tft.print(channelName);
    drawWiFiIcon(SCREEN_WIDTH - 15, 10);
    
    // PS Type
    drawCenteredText(psType, SCREEN_HEIGHT/2, 4);
}

void showInUseScreen() {
    bool needFullRefresh = false;
    
    // Cek apakah perlu full refresh
    if (currentScreen != "in_use") {
        needFullRefresh = true;
        currentScreen = "in_use";
    }

    if (needFullRefresh) {
        tft.fillScreen(NAVY);
        
        // Header
        tft.setTextSize(2);
        tft.setCursor(5, 5);
        tft.print(channelName);
        drawWiFiIcon(SCREEN_WIDTH - 15, 10);
        
        // PS Type (static)
        drawCenteredText(psType, 40, 3);
    }

    // Update player name hanya jika berubah
    if (playerName != lastPlayerName) {
        // Clear area nama player
        tft.fillRect(0, 60, SCREEN_WIDTH, 30, NAVY);
        drawCenteredText(playerName, 70, 3);
        lastPlayerName = playerName;
    }

    // Update timer hanya jika berubah
    if (timer != lastTimer) {
        // Clear area timer
        tft.fillRect(0, 100, SCREEN_WIDTH, 40, NAVY);
        drawCenteredText(timer, 120, 4);
        lastTimer = timer;
    }

    // Update price hanya jika berubah
    if (price != lastPrice) {
        // Clear area price
        tft.fillRect(0, 160, SCREEN_WIDTH, 40, NAVY);
        drawCenteredText(price, 180, 3);
        lastPrice = price;
    }
}

void showPausedScreen() {
    bool needFullRefresh = false;
    
    if (currentScreen != "paused" || !lastPauseState) {
        needFullRefresh = true;
        currentScreen = "paused";
        lastPauseState = true;
    }

    if (needFullRefresh) {
        tft.fillScreen(NAVY);
        
        // Header
        tft.setTextSize(2);
        tft.setCursor(5, 5);
        tft.print(channelName);
        drawWiFiIcon(SCREEN_WIDTH - 15, 10);
        
        // PS Type & Player
        drawCenteredText(psType, 40, 3);
        drawCenteredText(playerName, 70, 2);
        
        // PAUSED text
        drawCenteredText("PAUSED", 120, 4);
        
        // Price
        drawCenteredText(price, 180, 3);
    }
}

void showTimeUpScreen() {
    tft.fillScreen(NAVY);
    
    // Header
    tft.setTextSize(1);
    tft.setCursor(5, 5);
    tft.print(channelName);
    drawWiFiIcon(SCREEN_WIDTH - 15, 10);
    
    // PS Type & Player
    drawCenteredText(psType, 40, 2);
    drawCenteredText(playerName, 70, 2);
    
    // WAKTU HABIS
    drawCenteredText("WAKTU HABIS", 120, 2);
    
    // Price
    drawCenteredText(price, 180, 2);

    delay(5000);
}

void connectToServer() {
    WiFiClient client;
    HTTPClient http;
    
    String url = "http://" + String(serverHost) + ":" + String(serverPort) + "/api/channel/find";

    Serial.println("Connecting to server: " + url);

    http.begin(client, url);
    
    // Add timeout
    http.setTimeout(10000);  // 10 seconds timeout

    int httpCode = http.GET();
    String payload = http.getString();
    Serial.println("HTTP Response code: " + String(httpCode));
    Serial.println("Response payload: " + payload);

    if (httpCode > 0) {
        if (httpCode == HTTP_CODE_OK) {
            StaticJsonDocument<1024> doc;
            DeserializationError error = deserializeJson(doc, payload);

            if (!error && doc["success"]) {
                channelId = doc["channel_id"].as<String>();
                channelName = doc["data"]["channelName"].as<String>();
                psType = doc["data"]["psType"].as<String>();
                
                Serial.println("Connected as Channel ID: " + channelId);
                Serial.println("Channel Name: " + channelName);
                Serial.println("PS Type: " + psType);

                showStandbyScreen();
            } else {
                Serial.println("Failed to get channel ID");
                if (error) {
                    Serial.println("JSON Error: " + String(error.c_str()));
                }
                tft.fillScreen(NAVY);
                drawCenteredText("ERROR:", SCREEN_HEIGHT/2 - 20, 2);
                drawCenteredText("Channel tidak", SCREEN_HEIGHT/2 + 10, 2);
                drawCenteredText("terdaftar", SCREEN_HEIGHT/2 + 40, 2);
            }
        } else if (httpCode == HTTP_CODE_NOT_FOUND) {
            tft.fillScreen(NAVY);
            drawCenteredText("ERROR:", SCREEN_HEIGHT/2 - 20, 2);
            drawCenteredText("IP tidak", SCREEN_HEIGHT/2 + 10, 2);
            drawCenteredText("terdaftar", SCREEN_HEIGHT/2 + 40, 2);
        } else {
            tft.fillScreen(NAVY);
            drawCenteredText("ERROR:", SCREEN_HEIGHT/2 - 20, 2);
            drawCenteredText("Server Error", SCREEN_HEIGHT/2 + 10, 2);
            drawCenteredText(String(httpCode), SCREEN_HEIGHT/2 + 40, 2);
        }
    } else {
        Serial.println("Connection failed: " + http.errorToString(httpCode));
        tft.fillScreen(NAVY);
        drawCenteredText("ERROR:", SCREEN_HEIGHT/2 - 20, 2);
        drawCenteredText("Koneksi", SCREEN_HEIGHT/2 + 10, 2);
        drawCenteredText("Gagal", SCREEN_HEIGHT/2 + 40, 2);
    }

    http.end();
}

void updateChannelStatus() {
    if (channelId.isEmpty()) return;

    WiFiClient client;
    HTTPClient http;
    
    String url = "http://" + String(serverHost) + ":" + String(serverPort) + "/api/channel/status/" + channelId;

    Serial.println("Getting status from: " + url);

    http.begin(client, url);
    http.addHeader("Content-Type", "application/json");
    
    int httpCode = http.GET();

    if (httpCode == HTTP_CODE_OK) {
        String payload = http.getString();
        Serial.println("Response: " + payload);

        StaticJsonDocument<1024> doc;
        DeserializationError error = deserializeJson(doc, payload);

        if (!error && doc["success"]) {
            JsonObject data = doc["data"];
            String status = data["status"].as<String>();
            String relay = data["relay"].as<String>();
            
            Serial.println("Status: " + status);
            Serial.println("Relay: " + relay);

            // Update relay state
            digitalWrite(RELAY_PIN, relay == "ON" ? HIGH : LOW);

            if (status == "in_use") {
                playerName = data["playerName"].as<String>();
                timer = data["timer"].as<String>();
                price = data["price"].as<String>();
                bool isPaused = data["isPaused"].as<bool>();

                if (isPaused) {
                    showPausedScreen();
                } else {
                    Serial.println("Showing In Use Screen");
                    Serial.println("Player: " + playerName);
                    Serial.println("Timer: " + timer);
                    Serial.println("Price: " + price);
                    showInUseScreen();
                }
            } else {
                showStandbyScreen();
            }
        }
    } else {
        Serial.println("Error getting status: " + String(httpCode));
        // If error, try to reconnect
        connectToServer();
    }

    http.end();
}

void setup() {
    Serial.begin(115200);
    
    // Setup pin relay
    pinMode(RELAY_PIN, OUTPUT);
    digitalWrite(RELAY_PIN, LOW);
    
    // Inisialisasi TFT
    tft.init();
    tft.setRotation(2);
    tft.invertDisplay(true);
    tft.fillScreen(NAVY);
    tft.setTextColor(WHITE, NAVY);
    
    Serial.println("\nStarting up...");
    
    // Tampilkan splash screen
    showSplashScreen();
    
    // Hubungkan ke WiFi
    connectToWiFi();
    
    // Hubungkan ke server
    connectToServer();

    Serial.println("Setup complete!");
}

void loop() {
    // Check WiFi connection
    if (WiFi.status() != WL_CONNECTED) {
        isWifiConnected = false;
        Serial.println("WiFi disconnected! Reconnecting...");
        connectToWiFi();
        return;
    }

    // If no channel ID, try to connect
    if (channelId.isEmpty()) {
        static unsigned long lastRetryTime = 0;
        unsigned long currentTime = millis();
        if (currentTime - lastRetryTime >= 5000) { // Try every 5 seconds
            lastRetryTime = currentTime;
            Serial.println("No channel ID, retrying connection...");
            connectToServer();
        }
        return;
    }

    // Update status more frequently
    unsigned long currentMillis = millis();
    if (currentMillis - lastUpdateTime >= 1000) { // Update every 1 seconds
        lastUpdateTime = currentMillis;
        updateChannelStatus();
    }
}