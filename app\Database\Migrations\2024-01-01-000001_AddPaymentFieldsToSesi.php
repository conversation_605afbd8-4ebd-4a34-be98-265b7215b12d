<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddPaymentFieldsToSesi extends Migration
{
    public function up()
    {
        // Tambahkan kolom untuk pembayaran di tabel sesi
        $fields = [
            'metode_bayar' => [
                'type' => 'VARCHAR',
                'constraint' => 20,
                'null' => true,
                'comment' => 'Metode pembayaran: tunai, debit, qris, transfer'
            ],
            'jumlah_bayar' => [
                'type' => 'DECIMAL',
                'constraint' => '10,2',
                'null' => true,
                'comment' => 'Jumlah uang yang dibayarkan'
            ],
            'waktu_bayar' => [
                'type' => 'TIMESTAMP',
                'null' => true,
                'comment' => 'Waktu pembayaran dilakukan'
            ]
        ];
        
        $this->forge->addColumn('sesi', $fields);
    }

    public function down()
    {
        // Hapus kolom pembayaran dari tabel sesi
        $this->forge->dropColumn('sesi', ['metode_bayar', 'jumlah_bayar', 'waktu_bayar']);
    }
}
