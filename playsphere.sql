-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.2
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Aug 18, 2025 at 03:08 PM
-- Server version: 8.0.30
-- PHP Version: 8.2.29

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `playsphere`
--

-- --------------------------------------------------------

--
-- Table structure for table `konsol`
--

CREATE TABLE `konsol` (
  `id` int NOT NULL,
  `nama_konsol` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `harga_personal` int NOT NULL,
  `harga_member` int NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `konsol`
--

INSERT INTO `konsol` (`id`, `nama_konsol`, `harga_personal`, `harga_member`, `created_at`, `updated_at`) VALUES
(2, 'PS-3', 4000, 4000, '2025-06-23 00:30:55', '2025-07-01 12:50:40'),
(10, 'PS-4', 8000, 8000, '2025-06-23 09:44:46', '2025-07-15 00:55:06'),
(12, 'PS-5', 15000, 15000, '2025-06-23 09:51:48', '2025-06-25 05:29:14');

-- --------------------------------------------------------

--
-- Table structure for table `member`
--

CREATE TABLE `member` (
  `id` int NOT NULL,
  `id_member` varchar(20) COLLATE utf8mb4_general_ci NOT NULL,
  `nama` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `no_wa` varchar(20) COLLATE utf8mb4_general_ci NOT NULL,
  `saldo` int DEFAULT '5000',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `member`
--

INSERT INTO `member` (`id`, `id_member`, `nama`, `no_wa`, `saldo`, `created_at`, `updated_at`) VALUES
(5, 'PS250624174030', 'NURIL', '081230199311', 53367, '2025-06-24 17:40:30', '2025-07-17 20:29:15'),
(6, 'PS250624174206', 'ZULFAN', '081546987525', 40000, '2025-06-24 17:42:06', '2025-07-16 20:39:10'),
(10, 'PS250624204352', 'DAMAR', '0812548793', 12506, '2025-06-24 20:43:52', '2025-08-18 21:18:05'),
(12, 'PS250701162213', 'SAIPUL', '081259954687', 9920, '2025-07-01 16:22:13', '2025-08-18 21:21:29'),
(14, 'PS250715071632', 'AGUS', '5463242', 26000, '2025-07-15 07:16:32', '2025-07-16 20:23:44'),
(15, 'PS250715071644', 'manuk', '354365867', 100, '2025-07-15 07:16:44', '2025-07-17 08:16:10');

-- --------------------------------------------------------

--
-- Table structure for table `migrations`
--

CREATE TABLE `migrations` (
  `id` bigint UNSIGNED NOT NULL,
  `version` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `class` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `group` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `namespace` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `time` int NOT NULL,
  `batch` int UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `migrations`
--

INSERT INTO `migrations` (`id`, `version`, `class`, `group`, `namespace`, `time`, `batch`) VALUES
(1, '2025-01-14-144200', 'App\\Database\\Migrations\\AddSisaSaldoToSesi', 'default', 'App', 1752483516, 1),
(2, '2024-01-01-000001', 'App\\Database\\Migrations\\AddPaymentFieldsToSesi', 'default', 'App', 1752658546, 2),
(3, '2024-01-01-000002', 'App\\Database\\Migrations\\AddStatusBayarToSesi', 'default', 'App', 1752662282, 3),
(4, '2024-01-01-000003', 'App\\Database\\Migrations\\CreateTransaksiKasir', 'default', 'App', 1752668956, 4),
(5, '2024-01-01-000004', 'App\\Database\\Migrations\\UpdateTopupTable', 'default', 'App', 1752669912, 5),
(6, '2024-01-01-000005', 'App\\Database\\Migrations\\CreateProdukTable', 'default', 'App', 1754665603, 6);

-- --------------------------------------------------------

--
-- Table structure for table `operator`
--

CREATE TABLE `operator` (
  `id` int NOT NULL,
  `username` varchar(50) NOT NULL,
  `nama` varchar(100) NOT NULL,
  `no_wa` varchar(15) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','operator') NOT NULL DEFAULT 'operator',
  `status` enum('aktif','nonaktif') NOT NULL DEFAULT 'aktif',
  `last_login` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `operator`
--

INSERT INTO `operator` (`id`, `username`, `nama`, `no_wa`, `password`, `role`, `status`, `last_login`, `created_at`, `updated_at`) VALUES
(1, 'admin', 'Administrator', '081230199311', '$2y$10$vrGigVFz0mugjidNMjvmOemXZPYW/WpnRuI6SnxKgMJDV8CH3t5UK', 'admin', 'aktif', '2025-08-18 21:54:40', '2025-08-18 20:16:42', '2025-08-18 22:04:26'),
(2, 'operator', 'Operator Default', '081234567891', '$2y$10$6WPbwsC/KGQXFiy5zJIpeuyW75GoYg/1PwoSEvVg9O2mQSo/bgk7W', 'operator', 'aktif', '2025-08-18 20:19:22', '2025-08-18 20:16:42', '2025-08-18 20:20:43'),
(3, 'inidaus', 'Nuril Firdaus', '081332499304', '$2y$10$47SRFu2b.L.H9NfCPnfejeatpytqGYnU5GooX7fqvj6KLKVKL8Ynm', 'admin', 'aktif', '2025-08-18 21:15:45', '2025-08-18 20:34:10', '2025-08-18 22:04:54');

-- --------------------------------------------------------

--
-- Table structure for table `paket`
--

CREATE TABLE `paket` (
  `id` int NOT NULL,
  `nama_paket` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `id_konsol` int DEFAULT NULL,
  `durasi` time NOT NULL,
  `harga` int DEFAULT NULL,
  `keterangan` text COLLATE utf8mb4_general_ci,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `paket`
--

INSERT INTO `paket` (`id`, `nama_paket`, `id_konsol`, `durasi`, `harga`, `keterangan`, `created_at`, `updated_at`) VALUES
(1, 'PS4 1,5 Jam', 10, '01:30:00', 1000, 'Untuk sewa pendek dan hemat', '2025-06-23 17:51:27', '2025-07-15 09:51:14'),
(2, 'PS4 3 Jam', 10, '03:00:00', 15000, 'Paket paling populer', '2025-06-23 17:51:27', '2025-07-15 09:51:23'),
(3, 'PS4 Paket Harian', 10, '08:00:00', 6000, 'Khusus pelanggan loyal seharian', '2025-06-23 17:51:27', '2025-07-15 09:51:58'),
(4, 'PS5 Malam', 12, '05:00:00', 1000, 'Pakrt malam hari', '2025-06-23 13:50:51', '2025-07-15 09:52:10'),
(7, 'PS5 gratis', 12, '02:00:00', 5, 'test', '2025-06-24 09:12:44', '2025-07-15 09:51:46'),
(9, 'PS3 1 Jam', 2, '01:00:00', 3000, 'ps 3 PAKET 1 JAM', '2025-07-01 14:36:18', '2025-07-15 09:50:56'),
(10, 'PS3 1 menit', 2, '00:01:00', 1000, 'untuk coba', '2025-07-01 22:58:13', '2025-07-15 09:51:33'),
(11, 'PS4 1 Menit', 10, '00:01:00', 1000, 'Satu Menit saja', '2025-07-05 13:14:23', '2025-07-15 09:51:05');

-- --------------------------------------------------------

--
-- Table structure for table `produk`
--

CREATE TABLE `produk` (
  `id` int UNSIGNED NOT NULL,
  `kode_produk` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'Kode unik produk',
  `nama_produk` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'Nama produk',
  `kategori` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'Kategori produk (Makanan, Minuman, Snack, dll)',
  `harga_beli` decimal(10,2) NOT NULL COMMENT 'Harga beli produk',
  `harga_jual` decimal(10,2) NOT NULL COMMENT 'Harga jual produk',
  `stok` int NOT NULL DEFAULT '0' COMMENT 'Jumlah stok tersedia',
  `barcode` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Barcode produk (opsional)',
  `deskripsi` text COLLATE utf8mb4_general_ci COMMENT 'Deskripsi produk',
  `status` enum('aktif','nonaktif') COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'aktif' COMMENT 'Status produk',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `produk`
--

INSERT INTO `produk` (`id`, `kode_produk`, `nama_produk`, `kategori`, `harga_beli`, `harga_jual`, `stok`, `barcode`, `deskripsi`, `status`, `created_at`, `updated_at`) VALUES
(1, 'MKN001', 'Indomie Goreng', 'Makanan', 3500.00, 5000.00, 0, '8992388123456', 'Mie instan rasa ayam bawang', 'aktif', '2025-08-08 22:06:43', '2025-08-08 22:50:42'),
(2, 'MNM001', 'Teh Botol Sosro', 'Minuman', 3000.00, 4000.00, 23, '8992761234567', 'Teh manis dalam kemasan botol', 'aktif', '2025-08-08 22:06:43', '2025-08-18 15:57:54'),
(3, 'SNK001', 'Chitato', 'Snack', 6000.00, 8000.00, 24, '8992388345678', 'Keripik kentang rasa sapi panggang', 'aktif', '2025-08-08 22:06:43', '2025-08-18 15:57:54'),
(4, 'MNM002', 'Aqua 600ml', 'Minuman', 2000.00, 3000.00, 100, '8992761456789', 'Air mineral dalam kemasan botol 600ml', 'aktif', '2025-08-08 22:06:43', '2025-08-08 22:06:43'),
(5, 'SNK002', 'Oreo', 'Snack', 4500.00, 6000.00, 19, '8992388567890', 'Biskuit sandwich cokelat', 'aktif', '2025-08-08 22:06:43', '2025-08-18 15:42:43'),
(6, 'MKN002', 'Pop Mie', 'Makanan', 4000.00, 6000.00, 35, '8992388678901', 'Mie instan dalam cup rasa ayam', 'aktif', '2025-08-08 22:06:43', '2025-08-08 22:06:43'),
(7, 'MNM003', 'Coca Cola', 'Minuman', 4000.00, 5500.00, 40, '8992761789012', 'Minuman berkarbonasi rasa cola', 'aktif', '2025-08-08 22:06:43', '2025-08-08 22:06:43'),
(8, 'SNK003', 'Pringles', 'Snack', 12000.00, 15000.00, 15, '8992388890123', 'Keripik kentang dalam tabung', 'aktif', '2025-08-08 22:06:43', '2025-08-08 22:06:43'),
(9, 'MKN021', 'Mie Sedap Goreng', 'Makanan', 3000.00, 5000.00, 20, '305463464364', 'Mia Sedap Goreng gakl sedap', 'aktif', '2025-08-08 22:11:35', '2025-08-08 22:11:35');

-- --------------------------------------------------------

--
-- Table structure for table `sesi`
--

CREATE TABLE `sesi` (
  `id` int NOT NULL,
  `station_id` int NOT NULL,
  `jenis_user` enum('personal','member') COLLATE utf8mb4_general_ci NOT NULL,
  `waktu_mulai` datetime NOT NULL,
  `waktu_berhenti` datetime DEFAULT NULL,
  `harga_total` int DEFAULT '0',
  `status` enum('berjalan','berhenti') COLLATE utf8mb4_general_ci NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `id_member` int DEFAULT NULL,
  `id_paket` int DEFAULT NULL,
  `durasi_sisa` int DEFAULT NULL,
  `sisa_saldo` int NOT NULL DEFAULT '0',
  `metode_bayar` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Metode pembayaran: tunai, debit, qris, transfer',
  `jumlah_bayar` decimal(10,2) DEFAULT NULL COMMENT 'Jumlah uang yang dibayarkan',
  `waktu_bayar` timestamp NULL DEFAULT NULL COMMENT 'Waktu pembayaran dilakukan',
  `status_bayar` enum('belum','bayar') COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'belum' COMMENT 'Status pembayaran: belum, bayar'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `sesi`
--

INSERT INTO `sesi` (`id`, `station_id`, `jenis_user`, `waktu_mulai`, `waktu_berhenti`, `harga_total`, `status`, `created_at`, `updated_at`, `id_member`, `id_paket`, `durasi_sisa`, `sisa_saldo`, `metode_bayar`, `jumlah_bayar`, `waktu_bayar`, `status_bayar`) VALUES
(346, 10, 'personal', '2025-07-16 17:55:12', '2025-07-16 17:56:40', 1000, 'berhenti', '2025-07-16 17:55:12', '2025-07-16 19:39:18', NULL, NULL, NULL, 0, 'qris', 1000.00, '2025-07-16 12:39:18', 'bayar'),
(347, 10, 'member', '2025-07-16 18:05:52', '2025-07-16 18:07:50', 132, 'berhenti', '2025-07-16 18:05:52', '2025-07-16 18:07:50', 10, 0, 860, 1085, NULL, NULL, NULL, 'belum'),
(348, 13, 'member', '2025-07-16 18:06:36', '2025-07-16 18:07:38', 1000, 'berhenti', '2025-07-16 18:06:36', '2025-07-16 18:07:38', 12, 11, 60, 3995, NULL, NULL, NULL, 'belum'),
(349, 20, 'personal', '2025-07-16 19:20:24', '2025-07-16 19:36:22', 2000, 'berhenti', '2025-07-16 19:20:24', '2025-07-16 19:37:16', NULL, NULL, NULL, 0, 'tunai', 2000.00, '2025-07-16 12:37:16', 'bayar'),
(350, 12, 'personal', '2025-07-16 19:45:35', '2025-07-16 21:17:57', 14000, 'berhenti', '2025-07-16 19:45:35', '2025-07-16 21:18:44', NULL, NULL, NULL, 0, 'tunai', 14000.00, '2025-07-16 14:18:44', 'bayar'),
(351, 19, 'member', '2025-07-16 20:57:47', '2025-07-16 21:11:09', 3342, 'berhenti', '2025-07-16 20:57:47', '2025-07-16 21:11:09', 10, 0, 2076, 11953, NULL, NULL, NULL, 'belum'),
(352, 9, 'personal', '2025-07-16 21:03:39', '2025-07-17 07:28:12', 42000, 'berhenti', '2025-07-16 21:03:39', '2025-07-17 07:29:02', NULL, NULL, NULL, 0, 'tunai', 42000.00, '2025-07-17 00:29:02', 'bayar'),
(353, 21, 'member', '2025-07-16 21:04:09', '2025-07-16 21:05:12', 1000, 'berhenti', '2025-07-16 21:04:09', '2025-07-16 21:05:12', 12, 11, 60, 12995, NULL, NULL, NULL, 'belum'),
(354, 10, 'personal', '2025-07-17 20:22:19', '2025-07-17 20:29:27', 1000, 'berhenti', '2025-07-17 20:22:19', '2025-07-17 20:30:34', NULL, NULL, NULL, 0, 'tunai', 1000.00, '2025-07-17 13:30:34', 'bayar'),
(355, 14, 'member', '2025-07-17 20:22:56', '2025-07-17 20:29:15', 1579, 'berhenti', '2025-07-17 20:22:56', '2025-07-17 20:29:15', 5, 0, 12860, 54946, NULL, NULL, NULL, 'belum'),
(356, 21, 'member', '2025-07-17 20:24:02', '2025-07-17 20:28:14', 1000, 'berhenti', '2025-07-17 20:24:02', '2025-07-17 20:28:14', 12, 11, 60, 11995, NULL, NULL, NULL, 'belum'),
(357, 9, 'personal', '2025-07-18 07:42:45', '2025-07-19 18:56:19', 1000, 'berhenti', '2025-07-18 07:42:45', '2025-08-08 21:55:22', NULL, NULL, NULL, 0, 'qris', 1000.00, '2025-08-08 14:55:22', 'bayar'),
(358, 9, 'personal', '2025-07-19 21:18:38', '2025-07-27 13:47:03', 2000, 'berhenti', '2025-07-19 21:18:38', '2025-08-08 21:55:22', NULL, NULL, NULL, 0, 'qris', 2000.00, '2025-08-08 14:55:22', 'bayar'),
(359, 10, 'personal', '2025-08-08 21:54:40', '2025-08-08 21:55:02', 1000, 'berhenti', '2025-08-08 21:54:40', '2025-08-08 21:55:22', NULL, NULL, NULL, 0, 'qris', 1000.00, '2025-08-08 14:55:22', 'bayar'),
(360, 9, 'personal', '2025-08-08 22:29:58', '2025-08-09 12:39:42', 57000, 'berhenti', '2025-08-08 22:29:58', '2025-08-18 14:31:38', NULL, NULL, NULL, 0, 'tunai', 57000.00, '2025-08-18 07:31:38', 'bayar'),
(361, 9, 'personal', '2025-08-18 20:31:09', '2025-08-18 21:16:48', 4000, 'berhenti', '2025-08-18 20:31:09', '2025-08-18 21:28:08', NULL, NULL, NULL, 0, 'tunai', 4000.00, '2025-08-18 14:28:08', 'bayar'),
(362, 12, 'member', '2025-08-18 20:31:25', '2025-08-18 21:16:43', 6025, 'berhenti', '2025-08-18 20:31:25', '2025-08-18 21:16:43', 10, 0, 5730, 18611, NULL, NULL, NULL, 'belum'),
(363, 18, 'member', '2025-08-18 20:32:19', '2025-08-18 20:33:30', 1000, 'berhenti', '2025-08-18 20:32:19', '2025-08-18 20:33:30', 12, 11, 60, 10995, NULL, NULL, NULL, 'belum'),
(364, 9, 'personal', '2025-08-18 21:17:21', '2025-08-18 21:36:13', 2000, 'berhenti', '2025-08-18 21:17:21', '2025-08-18 21:36:13', NULL, NULL, NULL, 0, NULL, NULL, NULL, 'belum'),
(365, 12, 'member', '2025-08-18 21:17:29', '2025-08-18 21:18:05', 80, 'berhenti', '2025-08-18 21:17:29', '2025-08-18 21:18:05', 10, 0, 5670, 12586, NULL, NULL, NULL, 'belum'),
(366, 12, 'member', '2025-08-18 21:18:20', '2025-08-18 21:19:23', 1000, 'berhenti', '2025-08-18 21:18:20', '2025-08-18 21:19:23', 12, 11, 60, 9995, NULL, NULL, NULL, 'belum'),
(367, 12, 'member', '2025-08-18 21:20:55', '2025-08-18 21:21:29', 75, 'berhenti', '2025-08-18 21:20:55', '2025-08-18 21:21:29', 12, 0, 4499, 9995, NULL, NULL, NULL, 'belum');

-- --------------------------------------------------------

--
-- Table structure for table `station`
--

CREATE TABLE `station` (
  `id` int NOT NULL,
  `nama_station` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `id_konsol` int NOT NULL,
  `ip_address` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `station`
--

INSERT INTO `station` (`id`, `nama_station`, `id_konsol`, `ip_address`, `created_at`, `updated_at`) VALUES
(9, 'Station 1', 2, '************', '2025-06-23 09:41:30', '2025-06-25 09:45:10'),
(10, 'Station 2', 2, '************', '2025-06-23 09:45:05', '2025-07-15 11:06:50'),
(12, 'Station 3', 10, '************0', '2025-06-23 10:33:00', '2025-06-24 09:59:46'),
(13, 'Station 4', 10, '***********', '2025-06-23 10:34:26', '2025-06-24 10:00:07'),
(14, 'Station 5', 12, '*************', '2025-06-23 13:54:40', '2025-07-15 11:07:27'),
(16, 'Station 6', 12, '*************', '2025-06-24 10:00:26', '2025-07-15 11:07:35'),
(18, 'Station 7', 10, '************1', '2025-07-15 01:09:03', '2025-07-15 11:07:01'),
(19, 'Station 8', 12, '***********3', '2025-07-15 01:09:16', '2025-07-15 11:07:41'),
(20, 'Station 9', 10, '************', '2025-07-15 01:09:30', '2025-07-15 01:09:30'),
(21, 'Station 10', 10, '************', '2025-07-15 01:09:54', '2025-07-15 01:09:54'),
(22, 'Station 11', 10, '*************', '2025-07-15 01:10:42', '2025-07-15 11:07:10'),
(23, 'Station 12', 10, '192.168.1.23', '2025-07-15 01:10:56', '2025-07-15 11:07:19');

-- --------------------------------------------------------

--
-- Table structure for table `topup`
--

CREATE TABLE `topup` (
  `id` int NOT NULL,
  `id_member` int NOT NULL,
  `jumlah` int NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `metode_bayar` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Metode pembayaran: tunai, qris, debit, transfer',
  `tanggal_topup` datetime DEFAULT NULL COMMENT 'Tanggal dan waktu top-up dilakukan'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `topup`
--

INSERT INTO `topup` (`id`, `id_member`, `jumlah`, `created_at`, `updated_at`, `metode_bayar`, `tanggal_topup`) VALUES
(1, 5, 50000, '2025-06-24 18:38:02', '2025-06-24 18:38:02', NULL, NULL),
(2, 5, 5000, '2025-06-24 18:38:22', '2025-06-24 18:38:22', NULL, NULL),
(3, 6, 20000, '2025-06-24 18:40:05', '2025-06-24 18:40:05', NULL, NULL),
(4, 5, 5000, '2025-06-24 18:41:54', '2025-06-24 18:41:54', NULL, NULL),
(5, 5, 5000, '2025-06-24 19:01:43', '2025-06-24 19:01:43', NULL, NULL),
(6, 5, 20000, '2025-06-24 19:01:48', '2025-06-24 19:01:48', NULL, NULL),
(7, 5, 100000, '2025-06-24 19:01:51', '2025-06-24 19:01:51', NULL, NULL),
(8, 5, 10000, '2025-06-24 19:01:56', '2025-06-24 19:01:56', NULL, NULL),
(9, 5, 20000, '2025-06-24 19:02:00', '2025-06-24 19:02:00', NULL, NULL),
(10, 5, 50000, '2025-06-24 19:02:04', '2025-06-24 19:02:04', NULL, NULL),
(12, 5, 5000, '2025-06-24 20:02:29', '2025-06-24 20:02:29', NULL, NULL),
(13, 5, 5000, '2025-06-24 20:14:14', '2025-06-24 20:14:14', NULL, NULL),
(14, 5, 5000, '2025-06-24 20:30:53', '2025-06-24 20:30:53', NULL, NULL),
(16, 10, 5000, '2025-06-24 20:44:28', '2025-06-24 20:44:28', NULL, NULL),
(17, 5, 5000, '2025-06-25 09:45:22', '2025-06-25 09:45:22', NULL, NULL),
(18, 5, 20000, '2025-06-25 09:45:48', '2025-06-25 09:45:48', NULL, NULL),
(19, 12, 1000, '2025-07-01 16:22:22', '2025-07-01 16:22:22', NULL, NULL),
(20, 12, 1000, '2025-07-01 17:22:58', '2025-07-01 17:22:58', NULL, NULL),
(21, 12, 1000, '2025-07-01 22:00:01', '2025-07-01 22:00:01', NULL, NULL),
(22, 10, 1000, '2025-07-01 22:26:04', '2025-07-01 22:26:04', NULL, NULL),
(23, 10, 1000, '2025-07-01 22:33:14', '2025-07-01 22:33:14', NULL, NULL),
(24, 12, 2000, '2025-07-01 22:40:54', '2025-07-01 22:40:54', NULL, NULL),
(25, 10, 1000, '2025-07-01 22:41:01', '2025-07-01 22:41:01', NULL, NULL),
(26, 10, 50000, '2025-07-01 23:45:08', '2025-07-01 23:45:08', NULL, NULL),
(27, 5, 10000, '2025-07-02 14:40:26', '2025-07-02 14:40:26', NULL, NULL),
(28, 12, 20000, '2025-07-02 14:40:32', '2025-07-02 14:40:32', NULL, NULL),
(29, 5, 100000, '2025-07-02 21:04:20', '2025-07-02 21:04:20', NULL, NULL),
(30, 12, 50000, '2025-07-02 21:04:29', '2025-07-02 21:04:29', NULL, NULL),
(31, 10, 50000, '2025-07-02 21:04:35', '2025-07-02 21:04:35', NULL, NULL),
(32, 5, 5000, '2025-07-05 13:43:13', '2025-07-05 13:43:13', NULL, NULL),
(33, 5, 1000, '2025-07-14 17:58:28', '2025-07-14 17:58:28', NULL, NULL),
(34, 10, 5000, '2025-07-15 00:49:04', '2025-07-15 00:49:04', NULL, NULL),
(35, 10, 10000, '2025-07-15 01:18:15', '2025-07-15 01:18:15', NULL, NULL),
(36, 10, 5000, '2025-07-15 12:32:42', '2025-07-15 12:32:42', NULL, NULL),
(37, 10, 1000, '2025-07-15 13:08:50', '2025-07-15 13:08:50', NULL, NULL),
(38, 10, 1000, '2025-07-15 14:06:17', '2025-07-15 14:06:17', NULL, NULL),
(39, 10, 1000, '2025-07-15 14:22:03', '2025-07-15 14:22:03', NULL, NULL),
(40, 10, 5000, '2025-07-15 15:02:17', '2025-07-15 15:02:17', NULL, NULL),
(41, 10, 10000, '2025-07-15 15:20:20', '2025-07-15 15:20:20', NULL, NULL),
(42, 12, 10000, '2025-07-15 15:20:25', '2025-07-15 15:20:25', NULL, NULL),
(43, 6, 1000, '2025-07-15 15:32:44', '2025-07-15 15:32:44', NULL, NULL),
(44, 10, 1000, '2025-07-15 16:26:22', '2025-07-15 16:26:22', NULL, NULL),
(45, 5, 10000, '2025-07-15 18:58:30', '2025-07-15 18:58:30', NULL, NULL),
(46, 5, 25000, '2025-07-16 15:40:37', '2025-07-16 15:40:37', NULL, NULL),
(47, 6, 10000, '2025-07-16 16:10:35', '2025-07-16 16:10:35', NULL, NULL),
(48, 10, 1000, '2025-07-16 16:54:04', '2025-07-16 16:54:04', NULL, NULL),
(49, 6, 10000, '2025-07-16 17:26:53', '2025-07-16 17:26:53', NULL, NULL),
(50, 5, 10000, '2025-07-16 19:15:04', '2025-07-16 19:15:04', NULL, NULL),
(51, 6, 10000, '2025-07-16 19:19:45', '2025-07-16 19:19:45', NULL, NULL),
(54, 14, 10000, '2025-07-16 19:46:35', '2025-07-16 19:46:35', 'tunai', '2025-07-16 19:46:35'),
(55, 14, 10000, '2025-07-16 19:53:31', '2025-07-16 19:53:31', 'tunai', '2025-07-16 19:53:31'),
(56, 14, 5000, '2025-07-16 19:57:45', '2025-07-16 19:57:45', 'tunai', '2025-07-16 19:57:45'),
(57, 15, 100000, '2025-07-16 20:00:13', '2025-07-16 20:00:13', 'tunai', '2025-07-16 20:00:13'),
(58, 12, 10000, '2025-07-16 20:15:34', '2025-07-16 20:15:34', 'tunai', '2025-07-16 20:15:34'),
(59, 14, 1000, '2025-07-16 20:23:44', '2025-07-16 20:23:44', 'tunai', '2025-07-16 20:23:44'),
(60, 6, 10000, '2025-07-16 20:39:10', '2025-07-16 20:39:10', 'tunai', '2025-07-16 20:39:10'),
(61, 5, 10000, '2025-07-16 20:48:11', '2025-07-16 20:48:11', 'tunai', '2025-07-16 20:48:11'),
(62, 10, 10000, '2025-08-18 15:59:53', '2025-08-18 15:59:53', 'tunai', '2025-08-18 15:59:53');

-- --------------------------------------------------------

--
-- Table structure for table `transaksi_kasir`
--

CREATE TABLE `transaksi_kasir` (
  `id` int UNSIGNED NOT NULL,
  `nomor_transaksi` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'Nomor transaksi unik (TRX-YYYYMMDD-XXXX)',
  `tanggal_transaksi` datetime NOT NULL COMMENT 'Tanggal dan waktu transaksi',
  `total_item` int NOT NULL DEFAULT '0' COMMENT 'Jumlah total item dalam transaksi',
  `total_harga` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT 'Total harga semua item',
  `metode_bayar` enum('tunai','qris','debit','transfer') COLLATE utf8mb4_general_ci NOT NULL COMMENT 'Metode pembayaran yang digunakan',
  `jumlah_bayar` decimal(12,2) DEFAULT NULL COMMENT 'Jumlah uang yang dibayarkan (untuk tunai)',
  `kembalian` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT 'Kembalian yang diberikan',
  `kasir` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Nama kasir yang melayani',
  `keterangan` text COLLATE utf8mb4_general_ci COMMENT 'Keterangan tambahan transaksi',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `transaksi_kasir`
--

INSERT INTO `transaksi_kasir` (`id`, `nomor_transaksi`, `tanggal_transaksi`, `total_item`, `total_harga`, `metode_bayar`, `jumlah_bayar`, `kembalian`, `kasir`, `keterangan`, `created_at`, `updated_at`) VALUES
(1, 'TRX-20250716-0001', '2025-07-16 19:37:16', 1, 2000.00, 'tunai', 2000.00, 0.00, 'Kasir', 'Transaksi kasir - 1 item', '2025-07-16 19:37:16', '2025-07-16 19:37:16'),
(2, 'TRX-20250716-0002', '2025-07-16 19:39:18', 3, 15000.00, 'qris', 15000.00, 0.00, 'Kasir', 'Transaksi kasir - 3 item', '2025-07-16 19:39:18', '2025-07-16 19:39:18'),
(3, 'TRX-20250716-0003', '2025-07-16 19:41:38', 1, 1000.00, 'tunai', 1000.00, 0.00, 'Kasir', 'Transaksi kasir - 1 item', '2025-07-16 19:41:38', '2025-07-16 19:41:38'),
(4, 'TRX-20250716-0004', '2025-07-16 19:42:55', 1, 4000.00, 'tunai', 4000.00, 0.00, 'Kasir', 'Transaksi kasir - 1 item', '2025-07-16 19:42:55', '2025-07-16 19:42:55'),
(5, 'TRX-20250716-0005', '2025-07-16 19:46:35', 1, 10000.00, 'tunai', 10000.00, 0.00, 'Kasir', 'Transaksi kasir - 1 item', '2025-07-16 19:46:35', '2025-07-16 19:46:35'),
(6, 'TRX-20250716-0006', '2025-07-16 19:53:31', 3, 17000.00, 'tunai', 17000.00, 0.00, 'Kasir', 'Transaksi kasir - 3 item', '2025-07-16 19:53:31', '2025-07-16 19:53:31'),
(7, 'TRX-20250716-0007', '2025-07-16 19:57:45', 2, 9000.00, 'tunai', 20000.00, 11000.00, 'Kasir', 'Transaksi kasir - 2 item', '2025-07-16 19:57:45', '2025-07-16 19:57:45'),
(8, 'TRX-20250716-0008', '2025-07-16 20:00:13', 4, 114000.00, 'tunai', 120000.00, 6000.00, 'Kasir', 'Transaksi kasir - 3 item', '2025-07-16 20:00:13', '2025-07-16 20:00:13'),
(9, 'TRX-20250716-0009', '2025-07-16 20:02:22', 1, 8000.00, 'tunai', 10000.00, 2000.00, 'Kasir', 'Transaksi kasir - 1 item', '2025-07-16 20:02:22', '2025-07-16 20:02:22'),
(10, 'TRX-20250716-0010', '2025-07-16 20:05:21', 4, 19000.00, 'tunai', 20000.00, 1000.00, 'Kasir', 'Transaksi kasir - 2 item', '2025-07-16 20:05:21', '2025-07-16 20:05:21'),
(11, 'TRX-20250716-0011', '2025-07-16 20:13:50', 4, 18000.00, 'tunai', 20000.00, 2000.00, 'Kasir', 'Transaksi kasir - 2 item', '2025-07-16 20:13:50', '2025-07-16 20:13:50'),
(12, 'TRX-20250716-0012', '2025-07-16 20:15:34', 4, 25000.00, 'tunai', 30000.00, 5000.00, 'Kasir', 'Transaksi kasir - 3 item', '2025-07-16 20:15:34', '2025-07-16 20:15:34'),
(13, 'TRX-20250716-0013', '2025-07-16 20:23:44', 4, 19000.00, 'tunai', 20000.00, 1000.00, 'Kasir', 'Transaksi kasir - 3 item', '2025-07-16 20:23:44', '2025-07-16 20:23:44'),
(14, 'TRX-20250716-0014', '2025-07-16 20:34:34', 2, 9000.00, 'tunai', 10000.00, 1000.00, 'Kasir', 'Transaksi kasir - 2 item', '2025-07-16 20:34:34', '2025-07-16 20:34:34'),
(15, 'TRX-20250716-0015', '2025-07-16 20:39:10', 5, 39500.00, 'tunai', 40000.00, 500.00, 'Kasir', 'Transaksi kasir - 5 item', '2025-07-16 20:39:10', '2025-07-16 20:39:10'),
(16, 'TRX-20250716-0016', '2025-07-16 20:48:11', 5, 27000.00, 'tunai', 30000.00, 3000.00, 'Kasir', 'Transaksi kasir - 4 item', '2025-07-16 20:48:11', '2025-07-16 20:48:11'),
(17, 'TRX-20250716-0017', '2025-07-16 21:18:44', 5, 37000.00, 'tunai', 50000.00, 13000.00, 'Kasir', 'Transaksi kasir - 4 item', '2025-07-16 21:18:44', '2025-07-16 21:18:44'),
(18, 'TRX-20250717-0001', '2025-07-17 07:29:02', 5, 59000.00, 'tunai', 100000.00, 41000.00, 'Kasir', 'Transaksi kasir - 4 item', '2025-07-17 07:29:02', '2025-07-17 07:29:02'),
(19, 'TRX-20250717-0002', '2025-07-17 07:33:30', 2, 8000.00, 'tunai', 10000.00, 2000.00, 'Kasir', 'Transaksi kasir - 2 item', '2025-07-17 07:33:30', '2025-07-17 07:33:30'),
(20, 'TRX-20250717-0003', '2025-07-17 14:57:04', 1, 4000.00, 'qris', 4000.00, 0.00, 'Kasir', 'Transaksi kasir - 1 item', '2025-07-17 14:57:04', '2025-07-17 14:57:04'),
(21, 'TRX-20250717-0004', '2025-07-17 15:54:49', 1, 4000.00, 'qris', 4000.00, 0.00, 'Kasir', 'Transaksi kasir - 1 item', '2025-07-17 15:54:49', '2025-07-17 15:54:49'),
(22, 'TRX-20250717-0005', '2025-07-17 15:55:39', 1, 4000.00, 'transfer', 4000.00, 0.00, 'Kasir', 'Transaksi kasir - 1 item', '2025-07-17 15:55:39', '2025-07-17 15:55:39'),
(23, 'TRX-20250717-0006', '2025-07-17 16:49:29', 1, 8000.00, 'transfer', 8000.00, 0.00, 'Kasir', 'Transaksi kasir - 1 item', '2025-07-17 16:49:29', '2025-07-17 16:49:29'),
(24, 'TRX-20250717-0007', '2025-07-17 20:30:34', 4, 14000.00, 'tunai', 20000.00, 6000.00, 'Kasir', 'Transaksi kasir - 3 item', '2025-07-17 20:30:34', '2025-07-17 20:30:34'),
(25, 'TRX-20250717-0008', '2025-07-17 23:01:25', 1, 4000.00, 'qris', 4000.00, 0.00, 'Kasir', 'Transaksi kasir - 1 item', '2025-07-17 23:01:25', '2025-07-17 23:01:25'),
(26, 'TRX-20250717-0009', '2025-07-17 23:23:03', 1, 4000.00, 'qris', 4000.00, 0.00, 'Kasir', 'Transaksi kasir - 1 item', '2025-07-17 23:23:03', '2025-07-17 23:23:03'),
(27, 'TRX-20250808-0001', '2025-08-08 21:55:22', 3, 4000.00, 'qris', 4000.00, 0.00, 'Kasir', 'Transaksi kasir - 3 item', '2025-08-08 21:55:22', '2025-08-08 21:55:22'),
(28, 'TRX-20250808-0002', '2025-08-08 22:50:03', 1, 5000.00, 'tunai', 5000.00, 0.00, 'Kasir', 'Transaksi kasir - 1 item', '2025-08-08 22:50:03', '2025-08-08 22:50:03'),
(29, 'TRX-20250808-0003', '2025-08-08 22:50:42', 1, 5000.00, 'tunai', 5000.00, 0.00, 'Kasir', 'Transaksi kasir - 1 item', '2025-08-08 22:50:42', '2025-08-08 22:50:42'),
(30, 'TRX-20250809-0001', '2025-08-09 12:40:01', 1, 5000.00, 'tunai', 5000.00, 0.00, 'Kasir', 'Transaksi kasir - 1 item', '2025-08-09 12:40:01', '2025-08-09 12:40:01'),
(31, 'TRX-20250818-0001', '2025-08-18 14:24:41', 2, 61000.00, 'tunai', 65000.00, 4000.00, 'Kasir', 'Transaksi kasir - 2 item', '2025-08-18 14:24:41', '2025-08-18 14:24:41'),
(32, 'TRX-20250818-0002', '2025-08-18 14:31:38', 3, 65000.00, 'tunai', 65000.00, 0.00, 'Kasir', 'Transaksi kasir - 2 item', '2025-08-18 14:31:38', '2025-08-18 14:31:38'),
(33, 'TRX-20250818-0003', '2025-08-18 14:32:10', 1, 4000.00, 'tunai', 5000.00, 1000.00, 'Kasir', 'Transaksi kasir - 1 item', '2025-08-18 14:32:10', '2025-08-18 14:32:10'),
(34, 'TRX-20250818-0004', '2025-08-18 15:42:43', 2, 10000.00, 'tunai', 10000.00, 0.00, 'Kasir', 'Transaksi kasir - 2 item', '2025-08-18 15:42:43', '2025-08-18 15:42:43'),
(35, 'TRX-20250818-5937', '2025-08-18 15:47:19', 3, 18000.00, 'tunai', 20000.00, 2000.00, 'Test Kasir', 'Sample transaksi produk untuk testing laporan', '2025-08-18 15:47:19', '2025-08-18 15:47:19'),
(36, 'TRX-20250818-5938', '2025-08-18 15:50:56', 1, 4000.00, 'tunai', 4000.00, 0.00, 'Kasir', 'Transaksi kasir - 1 item', '2025-08-18 15:50:56', '2025-08-18 15:50:56'),
(37, 'TRX-20250818-5939', '2025-08-18 15:57:54', 2, 12000.00, 'tunai', 12000.00, 0.00, 'Kasir', 'Transaksi kasir - 2 item', '2025-08-18 15:57:54', '2025-08-18 15:57:54'),
(38, 'TRX-20250818-5940', '2025-08-18 15:59:53', 1, 10000.00, 'tunai', 10000.00, 0.00, 'Kasir', 'Transaksi kasir - 1 item', '2025-08-18 15:59:53', '2025-08-18 15:59:53'),
(39, 'TRX-20250818-5941', '2025-08-18 21:28:08', 1, 4000.00, 'tunai', 5000.00, 1000.00, 'Kasir', 'Transaksi kasir - 1 item', '2025-08-18 21:28:08', '2025-08-18 21:28:08');

-- --------------------------------------------------------

--
-- Table structure for table `transaksi_kasir_detail`
--

CREATE TABLE `transaksi_kasir_detail` (
  `id` int UNSIGNED NOT NULL,
  `transaksi_kasir_id` int UNSIGNED NOT NULL COMMENT 'Foreign key ke tabel transaksi_kasir',
  `jenis_item` enum('session_payment','member_topup','product_sale') COLLATE utf8mb4_general_ci NOT NULL COMMENT 'Jenis item: pembayaran sesi, top-up member, penjualan produk',
  `item_id` int DEFAULT NULL COMMENT 'ID item terkait (sesi_id, member_id, produk_id)',
  `nama_item` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'Nama/deskripsi item',
  `harga_satuan` decimal(10,2) NOT NULL COMMENT 'Harga per satuan item',
  `quantity` int NOT NULL DEFAULT '1' COMMENT 'Jumlah item',
  `subtotal` decimal(10,2) NOT NULL COMMENT 'Subtotal (harga_satuan * quantity)',
  `detail_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT 'Data detail tambahan dalam format JSON',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
) ;

--
-- Dumping data for table `transaksi_kasir_detail`
--

INSERT INTO `transaksi_kasir_detail` (`id`, `transaksi_kasir_id`, `jenis_item`, `item_id`, `nama_item`, `harga_satuan`, `quantity`, `subtotal`, `detail_data`, `created_at`, `updated_at`) VALUES
(1, 1, 'session_payment', 349, 'Personal - Station 9 - PS-4 - 19.20 - 19.36', 2000.00, 1, 2000.00, '{\"start_time\":\"19.20\",\"end_time\":\"19.36\",\"member_id\":null,\"session_id\":349}', '2025-07-16 19:37:16', '2025-07-16 19:37:16'),
(2, 2, 'session_payment', 346, 'Personal - Station 2 - PS-3 - 17.55 - 17.56', 1000.00, 1, 1000.00, '{\"start_time\":\"17.55\",\"end_time\":\"17.56\",\"member_id\":null,\"session_id\":346}', '2025-07-16 19:39:18', '2025-07-16 19:39:18'),
(3, 2, 'member_topup', 10, 'Top Up Saldo - DAMAR', 10000.00, 1, 10000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":10,\"session_id\":null}', '2025-07-16 19:39:18', '2025-07-16 19:39:18'),
(4, 2, '', NULL, 'Teh Botol Sosro', 4000.00, 1, 4000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-16 19:39:18', '2025-07-16 19:39:18'),
(5, 3, 'member_topup', 10, 'Top Up Saldo - DAMAR', 1000.00, 1, 1000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":10,\"session_id\":null}', '2025-07-16 19:41:38', '2025-07-16 19:41:38'),
(6, 4, '', NULL, 'Teh Botol Sosro', 4000.00, 1, 4000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-16 19:42:55', '2025-07-16 19:42:55'),
(7, 5, 'member_topup', 14, 'Top Up Saldo - AGUS', 10000.00, 1, 10000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":14,\"session_id\":null}', '2025-07-16 19:46:35', '2025-07-16 19:46:35'),
(8, 6, '', NULL, 'Teh Botol Sosro', 4000.00, 1, 4000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-16 19:53:31', '2025-07-16 19:53:31'),
(9, 6, '', NULL, 'Aqua 600ml', 3000.00, 1, 3000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-16 19:53:31', '2025-07-16 19:53:31'),
(10, 6, 'member_topup', 14, 'Top Up Saldo - AGUS', 10000.00, 1, 10000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":14,\"session_id\":null}', '2025-07-16 19:53:31', '2025-07-16 19:53:31'),
(11, 7, 'member_topup', 14, 'Top Up Saldo - AGUS', 5000.00, 1, 5000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":14,\"session_id\":null}', '2025-07-16 19:57:45', '2025-07-16 19:57:45'),
(12, 7, '', NULL, 'Teh Botol Sosro', 4000.00, 1, 4000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-16 19:57:45', '2025-07-16 19:57:45'),
(13, 8, 'member_topup', 15, 'Top Up Saldo - manuk', 100000.00, 1, 100000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":15,\"session_id\":null}', '2025-07-16 20:00:13', '2025-07-16 20:00:13'),
(14, 8, '', NULL, 'Indomie Goreng', 5000.00, 2, 10000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-16 20:00:13', '2025-07-16 20:00:13'),
(15, 8, '', NULL, 'Teh Botol Sosro', 4000.00, 1, 4000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-16 20:00:13', '2025-07-16 20:00:13'),
(16, 9, '', NULL, 'Chitato', 8000.00, 1, 8000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-16 20:02:22', '2025-07-16 20:02:22'),
(17, 10, '', NULL, 'Indomie Goreng', 5000.00, 3, 15000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-16 20:05:21', '2025-07-16 20:05:21'),
(18, 10, '', NULL, 'Teh Botol Sosro', 4000.00, 1, 4000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-16 20:05:21', '2025-07-16 20:05:21'),
(19, 11, '', NULL, 'Teh Botol Sosro', 4000.00, 2, 8000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-16 20:13:50', '2025-07-16 20:13:50'),
(20, 11, '', NULL, 'Indomie Goreng', 5000.00, 2, 10000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-16 20:13:50', '2025-07-16 20:13:50'),
(21, 12, 'member_topup', 12, 'Top Up Saldo - SAIPUL', 10000.00, 1, 10000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":12,\"session_id\":null}', '2025-07-16 20:15:34', '2025-07-16 20:15:34'),
(22, 12, '', NULL, 'Indomie Goreng', 5000.00, 2, 10000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-16 20:15:34', '2025-07-16 20:15:34'),
(23, 12, '', NULL, 'Coca Cola', 5000.00, 1, 5000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-16 20:15:34', '2025-07-16 20:15:34'),
(24, 13, 'member_topup', 14, 'Top Up Saldo - AGUS', 1000.00, 1, 1000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":14,\"session_id\":null}', '2025-07-16 20:23:44', '2025-07-16 20:23:44'),
(25, 13, '', NULL, 'Chitato', 8000.00, 1, 8000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-16 20:23:44', '2025-07-16 20:23:44'),
(26, 13, '', NULL, 'Indomie Goreng', 5000.00, 2, 10000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-16 20:23:44', '2025-07-16 20:23:44'),
(27, 14, '', NULL, 'Teh Botol Sosro', 4000.00, 1, 4000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-16 20:34:34', '2025-07-16 20:34:34'),
(28, 14, '', NULL, 'Indomie Goreng', 5000.00, 1, 5000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-16 20:34:34', '2025-07-16 20:34:34'),
(29, 15, 'member_topup', 6, 'Top Up Saldo - ZULFAN', 10000.00, 1, 10000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":6,\"session_id\":null}', '2025-07-16 20:39:10', '2025-07-16 20:39:10'),
(30, 15, '', NULL, 'Indomie Goreng', 5000.00, 1, 5000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-16 20:39:10', '2025-07-16 20:39:10'),
(31, 15, '', NULL, 'Coca Cola', 5000.00, 1, 5000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-16 20:39:10', '2025-07-16 20:39:10'),
(32, 15, '', NULL, 'Pringles', 15000.00, 1, 15000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-16 20:39:10', '2025-07-16 20:39:10'),
(33, 15, '', NULL, 'Fanta', 4500.00, 1, 4500.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-16 20:39:10', '2025-07-16 20:39:10'),
(34, 16, 'member_topup', 5, 'Top Up Saldo - NURIL', 10000.00, 1, 10000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":5,\"session_id\":null}', '2025-07-16 20:48:11', '2025-07-16 20:48:11'),
(35, 16, '', NULL, 'Indomie Goreng', 5000.00, 2, 10000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-16 20:48:11', '2025-07-16 20:48:11'),
(36, 16, '', NULL, 'Teh Botol Sosro', 4000.00, 1, 4000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-16 20:48:11', '2025-07-16 20:48:11'),
(37, 16, '', NULL, 'Aqua 600ml', 3000.00, 1, 3000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-16 20:48:11', '2025-07-16 20:48:11'),
(38, 17, 'session_payment', 350, 'Personal - Station 3 - PS-4 - 19.45 - 21.17', 14000.00, 1, 14000.00, '{\"start_time\":\"19.45\",\"end_time\":\"21.17\",\"member_id\":null,\"session_id\":350}', '2025-07-16 21:18:44', '2025-07-16 21:18:44'),
(39, 17, '', NULL, 'Indomie Goreng', 5000.00, 2, 10000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-16 21:18:44', '2025-07-16 21:18:44'),
(40, 17, '', NULL, 'Chitato', 8000.00, 1, 8000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-16 21:18:44', '2025-07-16 21:18:44'),
(41, 17, '', NULL, 'Coca Cola', 5000.00, 1, 5000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-16 21:18:44', '2025-07-16 21:18:44'),
(42, 18, 'session_payment', 352, 'Personal - Station 1 - PS-3 - 21.03 - 07.28', 42000.00, 1, 42000.00, '{\"start_time\":\"21.03\",\"end_time\":\"07.28\",\"member_id\":null,\"session_id\":352}', '2025-07-17 07:29:02', '2025-07-17 07:29:02'),
(43, 18, '', NULL, 'Indomie Goreng', 5000.00, 2, 10000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-17 07:29:02', '2025-07-17 07:29:02'),
(44, 18, '', NULL, 'Teh Botol Sosro', 4000.00, 1, 4000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-17 07:29:02', '2025-07-17 07:29:02'),
(45, 18, '', NULL, 'Aqua 600ml', 3000.00, 1, 3000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-17 07:29:02', '2025-07-17 07:29:02'),
(46, 19, '', NULL, 'Indomie Goreng', 5000.00, 1, 5000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-17 07:33:30', '2025-07-17 07:33:30'),
(47, 19, '', NULL, 'Aqua 600ml', 3000.00, 1, 3000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-17 07:33:30', '2025-07-17 07:33:30'),
(48, 20, '', NULL, 'Teh Botol Sosro', 4000.00, 1, 4000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-17 14:57:04', '2025-07-17 14:57:04'),
(49, 21, '', NULL, 'Teh Botol Sosro', 4000.00, 1, 4000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-17 15:54:49', '2025-07-17 15:54:49'),
(50, 22, '', NULL, 'Teh Botol Sosro', 4000.00, 1, 4000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-17 15:55:39', '2025-07-17 15:55:39'),
(51, 23, '', NULL, 'Chitato', 8000.00, 1, 8000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-17 16:49:29', '2025-07-17 16:49:29'),
(52, 24, 'session_payment', 354, 'Personal - Station 2 - PS-3 - 20.22 - 20.29', 1000.00, 1, 1000.00, '{\"start_time\":\"20.22\",\"end_time\":\"20.29\",\"member_id\":null,\"session_id\":354}', '2025-07-17 20:30:34', '2025-07-17 20:30:34'),
(53, 24, '', NULL, 'Indomie Goreng', 5000.00, 2, 10000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-17 20:30:34', '2025-07-17 20:30:34'),
(54, 24, '', NULL, 'Aqua 600ml', 3000.00, 1, 3000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-17 20:30:34', '2025-07-17 20:30:34'),
(55, 25, '', NULL, 'Teh Botol Sosro', 4000.00, 1, 4000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-17 23:01:25', '2025-07-17 23:01:25'),
(56, 26, '', NULL, 'Teh Botol Sosro', 4000.00, 1, 4000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-07-17 23:23:03', '2025-07-17 23:23:03'),
(57, 27, 'session_payment', 359, 'Personal - Station 2 - PS-3 - 21.54 - 21.55', 1000.00, 1, 1000.00, '{\"start_time\":\"21.54\",\"end_time\":\"21.55\",\"member_id\":null,\"session_id\":359}', '2025-08-08 21:55:22', '2025-08-08 21:55:22'),
(58, 27, 'session_payment', 358, 'Personal - Station 1 - PS-3 - 21.18 - 13.47', 2000.00, 1, 2000.00, '{\"start_time\":\"21.18\",\"end_time\":\"13.47\",\"member_id\":null,\"session_id\":358}', '2025-08-08 21:55:22', '2025-08-08 21:55:22'),
(59, 27, 'session_payment', 357, 'Personal - Station 1 - PS-3 - 07.42 - 18.56', 1000.00, 1, 1000.00, '{\"start_time\":\"07.42\",\"end_time\":\"18.56\",\"member_id\":null,\"session_id\":357}', '2025-08-08 21:55:22', '2025-08-08 21:55:22'),
(60, 28, '', NULL, 'Indomie Goreng', 5000.00, 1, 5000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-08-08 22:50:03', '2025-08-08 22:50:03'),
(61, 29, '', NULL, 'Indomie Goreng', 5000.00, 1, 5000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-08-08 22:50:42', '2025-08-08 22:50:42'),
(62, 30, '', NULL, 'Indomie Goreng', 5000.00, 1, 5000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-08-09 12:40:01', '2025-08-09 12:40:01'),
(63, 31, '', NULL, 'Teh Botol Sosro', 4000.00, 1, 4000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-08-18 14:24:41', '2025-08-18 14:24:41'),
(64, 31, 'session_payment', 360, 'Personal - Station 1 - PS-3 - 22.29 - 12.39', 57000.00, 1, 57000.00, '{\"start_time\":\"22.29\",\"end_time\":\"12.39\",\"member_id\":null,\"session_id\":360}', '2025-08-18 14:24:41', '2025-08-18 14:24:41'),
(65, 32, '', NULL, 'Teh Botol Sosro', 4000.00, 2, 8000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-08-18 14:31:38', '2025-08-18 14:31:38'),
(66, 32, 'session_payment', 360, 'Personal - Station 1 - PS-3 - 22.29 - 12.39', 57000.00, 1, 57000.00, '{\"start_time\":\"22.29\",\"end_time\":\"12.39\",\"member_id\":null,\"session_id\":360}', '2025-08-18 14:31:38', '2025-08-18 14:31:38'),
(67, 33, '', NULL, 'Teh Botol Sosro', 4000.00, 1, 4000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-08-18 14:32:10', '2025-08-18 14:32:10'),
(68, 34, '', NULL, 'Teh Botol Sosro', 4000.00, 1, 4000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-08-18 15:42:43', '2025-08-18 15:42:43'),
(69, 34, '', NULL, 'Oreo', 6000.00, 1, 6000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-08-18 15:42:43', '2025-08-18 15:42:43'),
(70, 35, 'product_sale', 1, 'Indomie Goreng', 5000.00, 2, 10000.00, NULL, '2025-08-18 15:47:19', '2025-08-18 15:47:19'),
(71, 35, 'product_sale', 2, 'Teh Botol Sosro', 4000.00, 2, 8000.00, NULL, '2025-08-18 15:47:19', '2025-08-18 15:47:19'),
(72, 36, '', NULL, 'Teh Botol Sosro', 4000.00, 1, 4000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null}', '2025-08-18 15:50:56', '2025-08-18 15:50:56'),
(73, 37, 'product_sale', 2, 'Teh Botol Sosro', 4000.00, 1, 4000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null,\"product_id\":2}', '2025-08-18 15:57:54', '2025-08-18 15:57:54'),
(74, 37, 'product_sale', 3, 'Chitato', 8000.00, 1, 8000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":null,\"session_id\":null,\"product_id\":3}', '2025-08-18 15:57:54', '2025-08-18 15:57:54'),
(75, 38, 'member_topup', 10, 'Top Up Saldo - DAMAR', 10000.00, 1, 10000.00, '{\"start_time\":null,\"end_time\":null,\"member_id\":10,\"session_id\":null,\"product_id\":\"topup_10\"}', '2025-08-18 15:59:53', '2025-08-18 15:59:53'),
(76, 39, 'session_payment', 361, 'Personal - Station 1 - PS-3 - 20.31 - 21.16', 4000.00, 1, 4000.00, '{\"start_time\":\"20.31\",\"end_time\":\"21.16\",\"member_id\":null,\"session_id\":361,\"product_id\":\"session_361\"}', '2025-08-18 21:28:08', '2025-08-18 21:28:08');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `konsol`
--
ALTER TABLE `konsol`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `member`
--
ALTER TABLE `member`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `id_member` (`id_member`);

--
-- Indexes for table `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `operator`
--
ALTER TABLE `operator`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD KEY `role` (`role`),
  ADD KEY `status` (`status`);

--
-- Indexes for table `paket`
--
ALTER TABLE `paket`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `produk`
--
ALTER TABLE `produk`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `kode_produk` (`kode_produk`),
  ADD KEY `kategori` (`kategori`),
  ADD KEY `status` (`status`);

--
-- Indexes for table `sesi`
--
ALTER TABLE `sesi`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `station`
--
ALTER TABLE `station`
  ADD PRIMARY KEY (`id`),
  ADD KEY `id_konsol` (`id_konsol`);

--
-- Indexes for table `topup`
--
ALTER TABLE `topup`
  ADD PRIMARY KEY (`id`),
  ADD KEY `id_member` (`id_member`);

--
-- Indexes for table `transaksi_kasir`
--
ALTER TABLE `transaksi_kasir`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `nomor_transaksi` (`nomor_transaksi`),
  ADD KEY `tanggal_transaksi` (`tanggal_transaksi`);

--
-- Indexes for table `transaksi_kasir_detail`
--
ALTER TABLE `transaksi_kasir_detail`
  ADD PRIMARY KEY (`id`),
  ADD KEY `transaksi_kasir_id` (`transaksi_kasir_id`),
  ADD KEY `jenis_item` (`jenis_item`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `konsol`
--
ALTER TABLE `konsol`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `member`
--
ALTER TABLE `member`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `operator`
--
ALTER TABLE `operator`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `paket`
--
ALTER TABLE `paket`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `produk`
--
ALTER TABLE `produk`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `sesi`
--
ALTER TABLE `sesi`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=368;

--
-- AUTO_INCREMENT for table `station`
--
ALTER TABLE `station`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=24;

--
-- AUTO_INCREMENT for table `topup`
--
ALTER TABLE `topup`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=63;

--
-- AUTO_INCREMENT for table `transaksi_kasir`
--
ALTER TABLE `transaksi_kasir`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=40;

--
-- AUTO_INCREMENT for table `transaksi_kasir_detail`
--
ALTER TABLE `transaksi_kasir_detail`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `station`
--
ALTER TABLE `station`
  ADD CONSTRAINT `station_ibfk_1` FOREIGN KEY (`id_konsol`) REFERENCES `konsol` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `topup`
--
ALTER TABLE `topup`
  ADD CONSTRAINT `topup_ibfk_1` FOREIGN KEY (`id_member`) REFERENCES `member` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `transaksi_kasir_detail`
--
ALTER TABLE `transaksi_kasir_detail`
  ADD CONSTRAINT `transaksi_kasir_detail_transaksi_kasir_id_foreign` FOREIGN KEY (`transaksi_kasir_id`) REFERENCES `transaksi_kasir` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
