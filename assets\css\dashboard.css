/**
 * Dashboard CSS
 * Styling untuk halaman monitoring station gaming
 * Termasuk card station, timer, tombol kontrol, dan responsive design
 *
 * <AUTHOR>
 * @version 3.0
 */

/* ===== RESPONSIVE GRID SYSTEM ===== */
/* Custom responsive grid for better card layout */
@media (min-width: 1400px) {
  .col-xxl-1-5 {
    flex: 0 0 auto;
    width: 12.5%; /* 8 columns for 1920x1080 and larger */
  }
}

@media (min-width: 1600px) {
  .col-xxl-1-5 {
    width: 11.11%; /* 9 columns for very large screens */
  }
}

@media (min-width: 1920px) {
  .col-xxl-1-5 {
    width: 10%; /* 10 columns for ultra-wide screens */
  }
}

/* Responsive adjustments for card content */
@media (max-width: 576px) {
  .card-konsol {
    min-height: 130px !important;
    padding: 10px !important;
  }

  .fw-bold.mb-2.text-white.text-start.w-100 {
    font-size: 0.8rem !important;
    margin-bottom: 0.1rem !important;
  }

  .card-title {
    font-size: 0.85rem !important;
    margin-bottom: 0.1rem !important;
  }

  .timer {
    font-size: 1.4rem !important;
    font-weight: 900 !important;
    margin: 0.2rem 0 !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3) !important;
  }

  .paket-info, .user-info {
    font-size: 0.75rem !important;
    margin: 0.1rem 0 !important;
  }

  .user-member {
    font-size: 0.7rem !important;
  }
}

@media (min-width: 577px) and (max-width: 768px) {
  .card-konsol {
    min-height: 150px;
    padding: 14px;
  }
}

@media (min-width: 1200px) {
  .card-konsol {
    min-height: 170px;
    padding: 18px;
  }

  .timer {
    font-size: 1.7rem;
  }
}

@media (min-width: 1400px) {
  .card-konsol {
    min-height: 150px !important;
    padding: 12px !important;
  }

  .fw-bold.mb-2.text-white.text-start.w-100 {
    font-size: 0.8rem !important;
    margin-bottom: 0.15rem !important;
  }

  .card-title {
    font-size: 0.9rem !important;
    margin-bottom: 0.1rem !important;
  }

  .timer {
    font-size: 1.5rem !important;
    font-weight: 900 !important;
    margin: 0.2rem 0 !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3) !important;
  }

  .paket-info, .user-info {
    font-size: 0.8rem !important;
    line-height: 1.1 !important;
    margin: 0.1rem 0 !important;
  }

  .user-member {
    font-size: 0.7rem !important;
  }
}

/* ===== CARD STATION STYLING ===== */
.card-konsol {
  border-radius: 16px !important;
  background: #455A64 !important;
  color: white !important;
  padding: 16px !important;
  box-shadow: 0 8px 16px rgba(0,0,0,0.1) !important;
  position: relative !important;
  transition: all 0.2s ease-in-out !important;
  text-align: center !important;
  min-height: 160px !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: space-between !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

.card-konsol:hover {
  transform: translateY(-2px) !important;
}

.card-konsol.active {
  background: #2E7D32 !important;
  box-shadow: 0 8px 20px rgba(46, 125, 50, 0.3) !important;
}

.signal-icon {
  position: absolute !important;
  top: 12px !important;
  right: 14px !important;
  font-size: 1.2rem !important;
  opacity: 0.8 !important;
}

/* Styling untuk nama station */
.fw-bold.mb-2.text-white.text-start.w-100 {
  font-size: 0.9rem !important;
  margin-bottom: 0.5rem !important;
}

/* Styling untuk status kosong */
.card-konsol[data-status="kosong"] {
  background: #455A64 !important;
}

/* Styling untuk status tidak aktif */
.card-konsol[data-status="tidak_aktif"] {
  background: #616161 !important;
  opacity: 0.7 !important;
}

/* Styling untuk nama station */
.fw-bold.mb-2.text-white.text-start.w-100 {
  font-size: 0.85rem !important;
  margin-bottom: 0.2rem !important;
  line-height: 1.1 !important;
}

.card-title {
  font-weight: bold !important;
  font-size: 0.95rem !important;
  margin-bottom: 0.15rem !important;
  line-height: 1.1 !important;
}

.timer {
  font-size: 1.4rem !important;
  font-weight: 800 !important;
  margin: 0.3rem 0 !important;
  font-family: 'Lucida Console', 'Consolas', monospace !important;
  line-height: 1 !important;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3) !important;
}

.paket-info, .user-info {
  font-size: 0.85rem !important;
  opacity: 0.9 !important;
  margin: 0.15rem 0 !important;
  display: block !important;
  visibility: visible !important;
  line-height: 1.1 !important;
}

.user-member {
  font-size: 0.75rem !important;
  color: #FFEB3B !important;
  font-weight: bold !important;
}

/* ===== BUTTON STYLING ===== */
.btn-icon-group {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 8px;
}

.btn-icon {
  width: 32px;
  height: 32px;
  font-size: 0.9rem;
  border: none;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  transition: all 0.2s ease;
}

/* Responsive button adjustments */
@media (min-width: 1400px) {
  .btn-icon-group {
    gap: 3px;
    margin-top: 6px;
  }

  .btn-icon {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
    border-radius: 5px;
  }
}

@media (min-width: 1600px) {
  .btn-icon {
    width: 30px;
    height: 30px;
    font-size: 0.85rem;
  }
}

@media (max-width: 768px) {
  .btn-icon-group {
    gap: 5px;
    margin-top: 10px;
  }

  .btn-icon {
    width: 34px;
    height: 34px;
    font-size: 0.95rem;
    border-radius: 7px;
  }
}

.btn-play {
  background-color: #4FC3F7 !important;
  color: #212121 !important;
}

.btn-stop {
  background-color: #EF5350 !important;
  color: white !important;
}

.btn-move {
  background-color: #26C6DA !important;
  color: white !important;
}

.btn-plus, .btn-add-package {
  background-color: #BA68C8 !important;
  color: white !important;
}

/* Hover effects untuk tombol */
.btn-play:hover {
  background-color: #29B6F6 !important;
}

.btn-stop:hover {
  background-color: #E53935 !important;
}

.btn-move:hover {
  background-color: #00BCD4 !important;
}

.btn-plus:hover, .btn-add-package:hover {
  background-color: #AB47BC !important;
}

.btn-icon:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-icon:hover:not(:disabled) {
  transform: scale(1.05);
}

/* Special layout for 3 buttons (member with package) */
.btn-icon-group.three-buttons {
  justify-content: center;
  max-width: 100%;
  gap: 3px;
}

@media (min-width: 1400px) {
  .btn-icon-group.three-buttons {
    gap: 2px;
  }

  .btn-icon-group.three-buttons .btn-icon {
    width: 26px;
    height: 26px;
    font-size: 0.75rem;
  }
}

@media (min-width: 1600px) {
  .btn-icon-group.three-buttons {
    gap: 3px;
  }

  .btn-icon-group.three-buttons .btn-icon {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
  }
}

/* Container responsive adjustments */
.container-fluid {
  padding-left: 15px;
  padding-right: 15px;
}

@media (min-width: 1400px) {
  .container-fluid {
    padding-left: 30px;
    padding-right: 30px;
  }
}

/* Row gap adjustments */
.row.gx-3.gy-4 {
  --bs-gutter-x: 1rem;
  --bs-gutter-y: 1.5rem;
}

@media (max-width: 576px) {
  .row.gx-3.gy-4 {
    --bs-gutter-x: 0.75rem;
    --bs-gutter-y: 1rem;
  }
}

@media (min-width: 1200px) {
  .row.gx-3.gy-4 {
    --bs-gutter-x: 1.25rem;
    --bs-gutter-y: 1.75rem;
  }
}
