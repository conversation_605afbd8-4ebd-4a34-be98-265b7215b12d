<?php namespace App\Controllers;

use CodeIgniter\Controller;
use App\Models\BillingSessionModel;
use App\Models\ChannelModel;

class Billing extends BaseController
{
    protected $sessionModel;
    protected $channelModel;

    public function __construct()
    {
        $this->sessionModel = new BillingSessionModel();
        $this->channelModel = new ChannelModel();
    }

    public function startSession()
    {
        $rules = [
            'channel_id' => 'required|numeric',
            'package_id' => 'required|numeric',
            'player_name' => 'required|min_length[3]',
            'member_id' => 'permit_empty|numeric'
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'success' => false,
                'errors' => $this->validator->getErrors()
            ]);
        }

        // Start session
        $sessionData = [
            'channel_id' => $this->request->getPost('channel_id'),
            'package_id' => $this->request->getPost('package_id'),
            'player_name' => $this->request->getPost('player_name'),
            'member_id' => $this->request->getPost('member_id') ?: null,
            'start_time' => date('Y-m-d H:i:s'),
            'status' => 'active'
        ];

        $this->sessionModel->insert($sessionData);
        $this->channelModel->update($sessionData['channel_id'], ['status' => 'in_use']);

        return $this->response->setJSON(['success' => true]);
    }

    public function pauseSession($id)
    {
        $session = $this->sessionModel->find($id);
        if ($session && $session['status'] == 'active') {
            $this->sessionModel->update($id, [
                'status' => 'paused',
                'pause_time' => date('Y-m-d H:i:s')
            ]);
            return $this->response->setJSON(['success' => true]);
        }
        return $this->response->setJSON(['success' => false]);
    }

    public function resumeSession($id)
    {
        $session = $this->sessionModel->find($id);
        if ($session && $session['status'] == 'paused') {
            $pauseDuration = strtotime(date('Y-m-d H:i:s')) - strtotime($session['pause_time']);
            $this->sessionModel->update($id, [
                'status' => 'active',
                'total_pause_duration' => $session['total_pause_duration'] + $pauseDuration,
                'pause_time' => null
            ]);
            return $this->response->setJSON(['success' => true]);
        }
        return $this->response->setJSON(['success' => false]);
    }

    public function endSession($id)
    {
        $session = $this->sessionModel->find($id);
        if ($session) {
            $finalPrice = $this->sessionModel->calculateFinalPrice($id);
            $this->sessionModel->update($id, [
                'status' => 'completed',
                'end_time' => date('Y-m-d H:i:s'),
                'total_price' => $finalPrice
            ]);
            $this->channelModel->update($session['channel_id'], ['status' => 'available']);
            
            // If member session, deduct balance
            if ($session['member_id']) {
                $memberModel = new \App\Models\MemberModel();
                $memberModel->deductBalance($session['member_id'], $finalPrice);
            }
            
            return $this->response->setJSON([
                'success' => true,
                'final_price' => $finalPrice
            ]);
        }
        return $this->response->setJSON(['success' => false]);
    }

    public function addTime($id)
    {
        $rules = [
            'package_id' => 'required|numeric'
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'success' => false,
                'errors' => $this->validator->getErrors()
            ]);
        }

        $session = $this->sessionModel->find($id);
        $packageModel = new \App\Models\PackageModel();
        $package = $packageModel->find($this->request->getPost('package_id'));

        if ($session && $package) {
            // Tambah durasi dari paket baru
            $additionalMinutes = $package['duration'];
            $this->sessionModel->update($id, [
                'total_price' => $session['total_price'] + $package['price']
            ]);

            return $this->response->setJSON(['success' => true]);
        }
        return $this->response->setJSON(['success' => false]);
    }

    public function switchPS($id)
    {
        $rules = [
            'new_channel_id' => 'required|numeric'
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'success' => false,
                'errors' => $this->validator->getErrors()
            ]);
        }

        $session = $this->sessionModel->find($id);
        $newChannelId = $this->request->getPost('new_channel_id');
        
        if ($session) {
            // Update channel lama menjadi available
            $this->channelModel->update($session['channel_id'], ['status' => 'available']);
            
            // Update channel baru menjadi in_use
            $this->channelModel->update($newChannelId, ['status' => 'in_use']);
            
            // Update session dengan channel baru
            $this->sessionModel->update($id, ['channel_id' => $newChannelId]);
            
            return $this->response->setJSON(['success' => true]);
        }
        return $this->response->setJSON(['success' => false]);
    }

    public function getSessionStatus($id)
    {
        $session = $this->sessionModel->find($id);
        if ($session) {
            $duration = $this->sessionModel->calculateSessionDuration($id);
            return $this->response->setJSON([
                'success' => true,
                'status' => $session['status'],
                'duration' => $duration,
                'total_price' => $this->sessionModel->calculateFinalPrice($id)
            ]);
        }
        return $this->response->setJSON(['success' => false]);
    }
}