<?php
/**
 * Member Model
 * Model untuk mengelola data member gaming
 * Menangani operasi database untuk member, saldo, dan riwayat transaksi
 *
 * <AUTHOR> Team
 * @version 3.0
 */

namespace App\Models;

use CodeIgniter\Model;
use App\Models\TopupModel;
use App\Models\SesiModel;

class MemberModel extends Model
{
    // Konfigurasi tabel dan field
    protected $table = 'member';
    protected $primaryKey = 'id';
    protected $allowedFields = ['id_member', 'nama', 'no_wa', 'saldo'];
    protected $useTimestamps = true;

    /**
     * Ambil data member dengan riwayat top-up dan penggunaan
     * Menggabungkan data member dengan riwayat transaksi lengkap
     *
     * @return array Data member dengan riwayat
     */
    public function getWithRiwayat()
    {
        // Ambil semua data member
        $members = $this->findAll();
        $topupModel = new TopupModel();
        $sesiModel = new SesiModel();

        // Loop setiap member untuk menambahkan riwayat
        foreach ($members as &$member) {
            // Ambil riwayat top-up member
            $member['riwayat'] = $topupModel
                ->where('id_member', $member['id'])
                ->orderBy('created_at', 'DESC')
                ->findAll();

            // Riwayat Penggunaan Saldo
            $member['riwayat_penggunaan'] = $sesiModel
                ->select('sesi.*, station.nama_station, konsol.nama_konsol, konsol.harga_member, paket.nama_paket, paket.harga as harga_paket')
                ->join('station', 'station.id = sesi.station_id')
                ->join('konsol', 'konsol.id = station.id_konsol')
                ->join('paket', 'paket.id = sesi.id_paket', 'left')
                ->where('sesi.id_member', $member['id'])
                ->where('sesi.harga_total >', 0)
                ->orderBy('sesi.created_at', 'DESC')
                ->findAll();

            // Hitung total penggunaan saldo
            $totalPenggunaan = 0;
            foreach ($member['riwayat_penggunaan'] as $penggunaan) {
                if ($penggunaan['nama_paket']) {
                    // Jika paket, gunakan harga paket
                    $biaya = $penggunaan['harga_paket'];
                } else {
                    // Jika non-paket, hitung biaya berdasarkan durasi dan tarif per menit
                    if ($penggunaan['waktu_berhenti']) {
                        $mulai = strtotime($penggunaan['waktu_mulai']);
                        $berhenti = strtotime($penggunaan['waktu_berhenti']);
                        $durasiDetik = $berhenti - $mulai;
                        $durasiMenit = ceil($durasiDetik / 60); // Pembulatan ke atas per menit
                        $hargaPerJam = $penggunaan['harga_member'] ?? 0;
                        $hargaPerMenit = round($hargaPerJam / 60);
                        $biaya = $durasiMenit * $hargaPerMenit;
                    } else {
                        // Jika masih berjalan, gunakan harga_total sementara
                        $biaya = $penggunaan['harga_total'];
                    }
                }
                $totalPenggunaan += $biaya;
            }
            $member['total_penggunaan'] = $totalPenggunaan;
        }

        return $members;
    }

    public function getRiwayatPenggunaan($memberId)
    {
        $sesiModel = new SesiModel();
        
        return $sesiModel
            ->select('sesi.*, station.nama_station, konsol.nama_konsol, konsol.harga_member, paket.nama_paket, paket.harga as harga_paket')
            ->join('station', 'station.id = sesi.station_id')
            ->join('konsol', 'konsol.id = station.id_konsol')
            ->join('paket', 'paket.id = sesi.id_paket', 'left')
            ->where('sesi.id_member', $memberId)
            ->where('sesi.harga_total >', 0)
            ->orderBy('sesi.created_at', 'DESC')
            ->findAll();
    }

    public function reduceSaldo($memberId, $amount)
    {
        $member = $this->find($memberId);
        if (!$member) {
            return false;
        }

        if ($member['saldo'] < $amount) {
            return false;
        }

        $newSaldo = $member['saldo'] - $amount;
        return $this->update($memberId, ['saldo' => $newSaldo]);
    }
}
