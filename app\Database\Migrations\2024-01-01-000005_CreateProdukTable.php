<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateProdukTable extends Migration
{
    public function up()
    {
        // Tabel produk untuk manajemen stok kasir
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'kode_produk' => [
                'type' => 'VARCHAR',
                'constraint' => 20,
                'null' => false,
                'comment' => 'Kode unik produk'
            ],
            'nama_produk' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => false,
                'comment' => 'Nama produk'
            ],
            'kategori' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => false,
                'comment' => 'Kategori produk (Makanan, Minuman, Snack, dll)'
            ],
            'harga_beli' => [
                'type' => 'DECIMAL',
                'constraint' => '10,2',
                'null' => false,
                'comment' => 'Harga beli produk'
            ],
            'harga_jual' => [
                'type' => 'DECIMAL',
                'constraint' => '10,2',
                'null' => false,
                'comment' => 'Harga jual produk'
            ],
            'stok' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 0,
                'comment' => 'Jumlah stok tersedia'
            ],
            'barcode' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
                'comment' => 'Barcode produk (opsional)'
            ],
            'deskripsi' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Deskripsi produk'
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['aktif', 'nonaktif'],
                'default' => 'aktif',
                'comment' => 'Status produk'
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('kategori');
        $this->forge->addKey('status');
        $this->forge->addUniqueKey('kode_produk');
        $this->forge->createTable('produk');

        // Insert sample data
        $data = [
            [
                'kode_produk' => 'MKN001',
                'nama_produk' => 'Indomie Goreng',
                'kategori' => 'Makanan',
                'harga_beli' => 3500,
                'harga_jual' => 5000,
                'stok' => 50,
                'barcode' => '8992388123456',
                'deskripsi' => 'Mie instan rasa ayam bawang',
                'status' => 'aktif',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'kode_produk' => 'MNM001',
                'nama_produk' => 'Teh Botol Sosro',
                'kategori' => 'Minuman',
                'harga_beli' => 3000,
                'harga_jual' => 4000,
                'stok' => 30,
                'barcode' => '8992761234567',
                'deskripsi' => 'Teh manis dalam kemasan botol',
                'status' => 'aktif',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'kode_produk' => 'SNK001',
                'nama_produk' => 'Chitato',
                'kategori' => 'Snack',
                'harga_beli' => 6000,
                'harga_jual' => 8000,
                'stok' => 25,
                'barcode' => '8992388345678',
                'deskripsi' => 'Keripik kentang rasa sapi panggang',
                'status' => 'aktif',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'kode_produk' => 'MNM002',
                'nama_produk' => 'Aqua 600ml',
                'kategori' => 'Minuman',
                'harga_beli' => 2000,
                'harga_jual' => 3000,
                'stok' => 100,
                'barcode' => '8992761456789',
                'deskripsi' => 'Air mineral dalam kemasan botol 600ml',
                'status' => 'aktif',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'kode_produk' => 'SNK002',
                'nama_produk' => 'Oreo',
                'kategori' => 'Snack',
                'harga_beli' => 4500,
                'harga_jual' => 6000,
                'stok' => 20,
                'barcode' => '8992388567890',
                'deskripsi' => 'Biskuit sandwich cokelat',
                'status' => 'aktif',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'kode_produk' => 'MKN002',
                'nama_produk' => 'Pop Mie',
                'kategori' => 'Makanan',
                'harga_beli' => 4000,
                'harga_jual' => 6000,
                'stok' => 35,
                'barcode' => '8992388678901',
                'deskripsi' => 'Mie instan dalam cup rasa ayam',
                'status' => 'aktif',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'kode_produk' => 'MNM003',
                'nama_produk' => 'Coca Cola',
                'kategori' => 'Minuman',
                'harga_beli' => 4000,
                'harga_jual' => 5500,
                'stok' => 40,
                'barcode' => '8992761789012',
                'deskripsi' => 'Minuman berkarbonasi rasa cola',
                'status' => 'aktif',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'kode_produk' => 'SNK003',
                'nama_produk' => 'Pringles',
                'kategori' => 'Snack',
                'harga_beli' => 12000,
                'harga_jual' => 15000,
                'stok' => 15,
                'barcode' => '8992388890123',
                'deskripsi' => 'Keripik kentang dalam tabung',
                'status' => 'aktif',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        $this->db->table('produk')->insertBatch($data);
    }

    public function down()
    {
        $this->forge->dropTable('produk');
    }
}
