<?php
namespace App\Models;
use CodeIgniter\Model;

class KonsolModel extends Model
{
    protected $table = 'konsol';
    protected $primaryKey = 'id';
    protected $allowedFields = ['nama_konsol', 'harga_personal', 'harga_member'];
    protected $useTimestamps = true;

    public function getHargaMember($konsolId)
    {
        $konsol = $this->find($konsolId);
        return $konsol ? $konsol['harga_member'] : 0;
    }
}