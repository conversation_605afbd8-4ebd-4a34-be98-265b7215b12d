<?php
/**
 * Produk Model
 * Model untuk mengelola data produk/barang yang dijual di kasir
 * Menangani operasi database untuk produk, stok, dan kategori
 *
 * <AUTHOR> Team
 * @version 3.0
 */

namespace App\Models;

use CodeIgniter\Model;

class ProdukModel extends Model
{
    // Konfigurasi tabel dan field
    protected $table = 'produk';
    protected $primaryKey = 'id';
    protected $allowedFields = [
        'kode_produk', 'nama_produk', 'kategori', 'harga_beli', 
        'harga_jual', 'stok', 'barcode', 'deskripsi', 'status'
    ];
    protected $useTimestamps = true;
    
    /**
     * Ambil produk berdasarkan barcode
     * 
     * @param string $barcode
     * @return array|null
     */
    public function getByBarcode($barcode)
    {
        return $this->where('barcode', $barcode)
                   ->where('status', 'aktif')
                   ->first();
    }
    
    /**
     * Ambil produk berdasarkan kategori
     * 
     * @param string $kategori
     * @return array
     */
    public function getByKategori($kategori)
    {
        return $this->where('kategori', $kategori)
                   ->where('status', 'aktif')
                   ->where('stok >', 0)
                   ->findAll();
    }
    
    /**
     * Cari produk berdasarkan nama atau kode
     * 
     * @param string $keyword
     * @return array
     */
    public function searchProduk($keyword)
    {
        return $this->groupStart()
                   ->like('nama_produk', $keyword)
                   ->orLike('kode_produk', $keyword)
                   ->groupEnd()
                   ->where('status', 'aktif')
                   ->findAll();
    }
    
    /**
     * Update stok produk
     * 
     * @param int $id
     * @param int $jumlah (bisa negatif untuk pengurangan)
     * @return bool
     */
    public function updateStok($id, $jumlah)
    {
        $produk = $this->find($id);
        if (!$produk) {
            return false;
        }
        
        $stokBaru = $produk['stok'] + $jumlah;
        if ($stokBaru < 0) {
            return false; // Stok tidak boleh negatif
        }
        
        return $this->update($id, ['stok' => $stokBaru]);
    }
    
    /**
     * Get produk dengan stok rendah
     * 
     * @param int $batasStok
     * @return array
     */
    public function getStokRendah($batasStok = 10)
    {
        return $this->where('stok <=', $batasStok)
                   ->where('status', 'aktif')
                   ->findAll();
    }
}
