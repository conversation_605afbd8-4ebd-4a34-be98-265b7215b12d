<?php namespace App\Controllers;

class Api extends BaseController
{
    protected $channelModel;

    public function __construct()
    {
        $this->channelModel = new \App\Models\ChannelModel();
    }

    public function findChannelByIp()
    {
        try {
            $clientIP = $this->request->getIPAddress();
            
            // Find channel by IP
            $channel = $this->channelModel->select('channels.id, channels.number, ps_types.name as ps_type_name')
                                        ->join('ps_types', 'ps_types.id = channels.ps_type_id')
                                        ->where('channels.ip_address', $clientIP)
                                        ->first();

            if (!$channel) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'IP Address tidak terdaftar: ' . $clientIP
                ])->setStatusCode(404);
            }

            // Update using query builder
            $db = \Config\Database::connect();
            $db->table('channels')
               ->where('id', $channel['id'])
               ->update([
                   'connection_status' => 'connected',
                   'last_seen' => date('Y-m-d H:i:s')
               ]);

            return $this->response->setJSON([
                'success' => true,
                'channel_id' => $channel['id'],
                'data' => [
                    'channelName' => $channel['number'],
                    'psType' => $channel['ps_type_name']
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', '[findChannelByIp] Error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Server error'
            ])->setStatusCode(500);
        }
    }

    public function getChannelStatus($channelId)
    {
        try {
            $channel = $this->channelModel->select('channels.*, ps_types.name as ps_type_name')
                                        ->join('ps_types', 'ps_types.id = channels.ps_type_id')
                                        ->find($channelId);
        
            if (!$channel) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Channel not found'
                ])->setStatusCode(404);
            }

            // Update last seen
            $db = \Config\Database::connect();
            $db->table('channels')
               ->where('id', $channelId)
               ->update([
                   'last_seen' => date('Y-m-d H:i:s')
               ]);
            
            $sessionData = json_decode($channel['session_data'], true) ?? [];
            
            $response = [
                'success' => true,
                'data' => [
                    'channelName' => $channel['number'],
                    'psType' => $channel['ps_type_name'],
                    'status' => $channel['status'],
                    'playerName' => '',
                    'timer' => '00:00:00',
                    'price' => 'Rp 0',
                    'isPaused' => false,
                    'relay' => 'OFF'
                ]
            ];
        
            if ($sessionData && $channel['status'] === 'in_use') {
                $response['data'] = array_merge($response['data'], [
                    'status' => 'in_use',
                    'playerName' => $sessionData['playerName'] ?? '',
                    'timer' => $sessionData['timer'] ?? '00:00:00',
                    'price' => $sessionData['price'] ?? 'Rp 0',
                    'isPaused' => $sessionData['isPaused'] ?? false,
                    'relay' => ($sessionData['isPaused'] ?? false) ? 'OFF' : 'ON'
                ]);
            }
        
            return $this->response->setJSON($response);
            
        } catch (\Exception $e) {
            log_message('error', '[getChannelStatus] Error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Server error'
            ])->setStatusCode(500);
        }
    }
}