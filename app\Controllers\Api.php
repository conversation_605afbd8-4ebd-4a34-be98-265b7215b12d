<?php namespace App\Controllers;

class Api extends BaseController
{
    protected $stationModel;

    public function __construct()
    {
        $this->stationModel = new \App\Models\StationModel();
    }

    public function findChannelByIp()
    {
        try {
            $clientIP = $this->request->getIPAddress();

            // Find station by IP
            $station = $this->stationModel->select('station.id, station.nama_station, konsol.nama_konsol')
                                        ->join('konsol', 'konsol.id = station.id_konsol')
                                        ->where('station.ip_address', $clientIP)
                                        ->first();

            if (!$station) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'IP Address tidak terdaftar: ' . $clientIP
                ])->setStatusCode(404);
            }

            // Update station last seen (optional - bisa ditambahkan field jika diperlukan)
            $db = \Config\Database::connect();
            $db->table('station')
               ->where('id', $station['id'])
               ->update([
                   'updated_at' => date('Y-m-d H:i:s')
               ]);

            return $this->response->setJSON([
                'success' => true,
                'channel_id' => $station['id'],
                'data' => [
                    'channelName' => $station['nama_station'],
                    'psType' => $station['nama_konsol']
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', '[findChannelByIp] Error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Server error'
            ])->setStatusCode(500);
        }
    }

    public function getChannelStatus($stationId)
    {
        try {
            // Get station info
            $station = $this->stationModel->select('station.*, konsol.nama_konsol')
                                        ->join('konsol', 'konsol.id = station.id_konsol')
                                        ->find($stationId);

            if (!$station) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Station not found'
                ])->setStatusCode(404);
            }

            // Update last seen
            $db = \Config\Database::connect();
            $db->table('station')
               ->where('id', $stationId)
               ->update([
                   'updated_at' => date('Y-m-d H:i:s')
               ]);

            // Check for active session
            $sesiModel = new \App\Models\SesiModel();
            $activeSesi = $sesiModel->where('station_id', $stationId)
                                   ->where('status', 'berjalan')
                                   ->first();

            // Check for recently finished session (dalam 10 detik terakhir)
            $recentFinished = $sesiModel->where('station_id', $stationId)
                                       ->where('status', 'selesai')
                                       ->where('waktu_selesai >', date('Y-m-d H:i:s', strtotime('-10 seconds')))
                                       ->first();

            $response = [
                'success' => true,
                'data' => [
                    'channelName' => $station['nama_station'],
                    'psType' => $station['nama_konsol'],
                    'status' => 'available',
                    'playerName' => '',
                    'timer' => '00:00:00',
                    'price' => 'Rp 0',
                    'isPaused' => false,
                    'relay' => 'OFF',
                    'isPersonal' => true
                ]
            ];

            // Jika ada sesi yang baru selesai, tampilkan status stopped
            if ($recentFinished) {
                $memberModel = new \App\Models\MemberModel();
                $member = $memberModel->find($recentFinished['id_member']);
                $playerName = $member ? $member['nama'] : 'Personal';

                $response['data'] = array_merge($response['data'], [
                    'status' => 'stopped',
                    'playerName' => $playerName,
                    'price' => 'Rp ' . number_format($recentFinished['harga_total'], 0, ',', '.'),
                    'isPersonal' => !$recentFinished['id_member']
                ]);
            } else if ($activeSesi) {
                $startTime = new \DateTime($activeSesi['waktu_mulai']);
                $currentTime = new \DateTime();
                $interval = $startTime->diff($currentTime);

                // Get member info if exists
                $playerName = 'Personal';
                $isPersonal = true;
                $timer = sprintf('%02d:%02d:%02d',
                    $interval->h,
                    $interval->i,
                    $interval->s
                );

                if ($activeSesi['id_member']) {
                    $memberModel = new \App\Models\MemberModel();
                    $member = $memberModel->find($activeSesi['id_member']);
                    $playerName = $member ? $member['nama'] : 'Member';
                    $isPersonal = false;

                    if ($activeSesi['id_paket'] && $activeSesi['durasi_sisa'] > 0) {
                        // Member dengan paket - countdown dari durasi_sisa
                        // Kurangi durasi_sisa dengan waktu yang sudah berjalan
                        $waktuBerjalanMenit = ($interval->h * 60) + $interval->i + ($interval->s / 60);
                        $remainingMinutes = max(0, $activeSesi['durasi_sisa'] - $waktuBerjalanMenit);

                        $hours = floor($remainingMinutes / 60);
                        $minutes = floor($remainingMinutes % 60);
                        $seconds = floor(($remainingMinutes - floor($remainingMinutes)) * 60);
                        $timer = sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);

                        // Update durasi_sisa di database untuk tracking real-time
                        $db = \Config\Database::connect();
                        $db->table('sesi')
                           ->where('id', $activeSesi['id'])
                           ->update(['durasi_sisa' => floor($remainingMinutes)]);

                    } else {
                        // Member tanpa paket - countdown dari saldo
                        $konsolModel = new \App\Models\KonsolModel();
                        $konsol = $konsolModel->find($station['id_konsol']);
                        $hargaPerJam = $konsol ? $konsol['harga_member'] : 5000;

                        $sisaSaldo = $member['saldo'] ?? 0;
                        $maxWaktuMenit = ($sisaSaldo / $hargaPerJam) * 60;

                        // Hitung sisa waktu berdasarkan saldo
                        $waktuBerjalanMenit = ($interval->h * 60) + $interval->i + ($interval->s / 60);
                        $sisaWaktuMenit = max(0, $maxWaktuMenit - $waktuBerjalanMenit);

                        $hours = floor($sisaWaktuMenit / 60);
                        $minutes = floor($sisaWaktuMenit % 60);
                        $seconds = floor(($sisaWaktuMenit - floor($sisaWaktuMenit)) * 60);
                        $timer = sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
                    }
                }

                $response['data'] = array_merge($response['data'], [
                    'status' => 'in_use',
                    'playerName' => $playerName,
                    'timer' => $timer,
                    'price' => 'Rp ' . number_format($activeSesi['harga_total'], 0, ',', '.'),
                    'isPaused' => false, // Bisa ditambahkan logic pause jika diperlukan
                    'relay' => 'ON',
                    'isPersonal' => $isPersonal
                ]);
            }

            return $this->response->setJSON($response);
            
        } catch (\Exception $e) {
            log_message('error', '[getChannelStatus] Error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Server error'
            ])->setStatusCode(500);
        }
    }
}