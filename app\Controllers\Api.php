<?php namespace App\Controllers;

class Api extends BaseController
{
    protected $stationModel;

    public function __construct()
    {
        $this->stationModel = new \App\Models\StationModel();
    }

    public function findChannelByIp()
    {
        try {
            $clientIP = $this->request->getIPAddress();

            // Find station by IP
            $station = $this->stationModel->select('station.id, station.nama_station, konsol.nama_konsol')
                                        ->join('konsol', 'konsol.id = station.id_konsol')
                                        ->where('station.ip_address', $clientIP)
                                        ->first();

            if (!$station) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'IP Address tidak terdaftar: ' . $clientIP
                ])->setStatusCode(404);
            }

            // Update station last seen (optional - bisa ditambahkan field jika diperlukan)
            $db = \Config\Database::connect();
            $db->table('station')
               ->where('id', $station['id'])
               ->update([
                   'updated_at' => date('Y-m-d H:i:s')
               ]);

            return $this->response->setJSON([
                'success' => true,
                'channel_id' => $station['id'],
                'data' => [
                    'channelName' => $station['nama_station'],
                    'psType' => $station['nama_konsol']
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', '[findChannelByIp] Error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Server error'
            ])->setStatusCode(500);
        }
    }

    public function getChannelStatus($stationId)
    {
        try {
            // Get station info
            $station = $this->stationModel->select('station.*, konsol.nama_konsol')
                                        ->join('konsol', 'konsol.id = station.id_konsol')
                                        ->find($stationId);

            if (!$station) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Station not found'
                ])->setStatusCode(404);
            }

            // Update last seen
            $db = \Config\Database::connect();
            $db->table('station')
               ->where('id', $stationId)
               ->update([
                   'updated_at' => date('Y-m-d H:i:s')
               ]);

            // Check for active session
            $sesiModel = new \App\Models\SesiModel();
            $activeSesi = $sesiModel->where('station_id', $stationId)
                                   ->where('status', 'berjalan')
                                   ->first();

            $response = [
                'success' => true,
                'data' => [
                    'channelName' => $station['nama_station'],
                    'psType' => $station['nama_konsol'],
                    'status' => 'available',
                    'playerName' => '',
                    'timer' => '00:00:00',
                    'price' => 'Rp 0',
                    'isPaused' => false,
                    'relay' => 'OFF'
                ]
            ];

            if ($activeSesi) {
                // Calculate elapsed time
                $startTime = new \DateTime($activeSesi['waktu_mulai']);
                $currentTime = new \DateTime();
                $interval = $startTime->diff($currentTime);
                $timer = sprintf('%02d:%02d:%02d',
                    $interval->h,
                    $interval->i,
                    $interval->s
                );

                // Get member info if exists
                $playerName = 'Personal';
                if ($activeSesi['id_member']) {
                    $memberModel = new \App\Models\MemberModel();
                    $member = $memberModel->find($activeSesi['id_member']);
                    $playerName = $member ? $member['nama'] : 'Member';
                }

                $response['data'] = array_merge($response['data'], [
                    'status' => 'in_use',
                    'playerName' => $playerName,
                    'timer' => $timer,
                    'price' => 'Rp ' . number_format($activeSesi['harga_total'], 0, ',', '.'),
                    'isPaused' => false, // Bisa ditambahkan logic pause jika diperlukan
                    'relay' => 'ON'
                ]);
            }

            return $this->response->setJSON($response);
            
        } catch (\Exception $e) {
            log_message('error', '[getChannelStatus] Error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Server error'
            ])->setStatusCode(500);
        }
    }
}