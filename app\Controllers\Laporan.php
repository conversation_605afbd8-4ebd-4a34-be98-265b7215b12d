<?php
/**
 * Laporan Controller
 * Controller untuk menampilkan berbagai laporan dan statistik
 * Menangani laporan konsol, station, member, paket, topup, dan pendapatan
 *
 * <AUTHOR> Team
 * @version 3.0
 */

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\SesiModel;
use App\Models\StationModel;
use App\Models\KonsolModel;
use App\Models\MemberModel;
use App\Models\PaketModel;
use App\Models\TopupModel;
use App\Models\TransaksiKasirModel;
use App\Models\TransaksiKasirDetailModel;
use App\Models\ProdukModel;

class Laporan extends BaseController
{
    protected $sesiModel;
    protected $stationModel;
    protected $konsolModel;
    protected $memberModel;
    protected $paketModel;
    protected $topupModel;
    protected $transaksiKasirModel;
    protected $transaksiKasirDetailModel;
    protected $produkModel;

    public function __construct()
    {
        $this->sesiModel = new SesiModel();
        $this->stationModel = new StationModel();
        $this->konsolModel = new KonsolModel();
        $this->memberModel = new MemberModel();
        $this->paketModel = new PaketModel();
        $this->topupModel = new TopupModel();
        $this->transaksiKasirModel = new TransaksiKasirModel();
        $this->transaksiKasirDetailModel = new TransaksiKasirDetailModel();
        $this->produkModel = new ProdukModel();
    }

    /**
     * Halaman utama laporan
     */
    public function index()
    {
        $data = [
            'title' => 'Laporan & Statistik',
            'totalKonsol' => $this->konsolModel->countAll(),
            'totalStation' => $this->stationModel->countAll(),
            'totalMember' => $this->memberModel->countAll(),
            'totalProduk' => $this->produkModel->where('status', 'aktif')->countAllResults()
        ];

        return view('laporan/index', $data);
    }

    /**
     * Debug method to check data
     */
    public function debug()
    {
        $tanggal = date('Y-m-d'); // Today's date

        // Check sesi data
        $sesiData = $this->sesiModel->where('DATE(created_at)', $tanggal)->findAll();

        // Check transaksi kasir hari ini
        $transaksiKasir = $this->transaksiKasirModel->where('DATE(tanggal_transaksi)', $tanggal)->findAll();

        // Check transaksi kasir detail untuk produk
        $transaksiDetail = $this->transaksiKasirDetailModel
                               ->select('transaksi_kasir_detail.*, transaksi_kasir.tanggal_transaksi')
                               ->join('transaksi_kasir', 'transaksi_kasir.id = transaksi_kasir_detail.transaksi_kasir_id')
                               ->where('DATE(transaksi_kasir.tanggal_transaksi)', $tanggal)
                               ->findAll();

        // Check specifically product sales
        $produkSales = $this->transaksiKasirDetailModel
                           ->select('transaksi_kasir_detail.*, transaksi_kasir.tanggal_transaksi')
                           ->join('transaksi_kasir', 'transaksi_kasir.id = transaksi_kasir_detail.transaksi_kasir_id')
                           ->where('DATE(transaksi_kasir.tanggal_transaksi)', $tanggal)
                           ->where('transaksi_kasir_detail.jenis_item', 'product_sale')
                           ->findAll();

        return $this->response->setJSON([
            'tanggal' => $tanggal,
            'total_sesi' => count($sesiData),
            'total_transaksi_kasir' => count($transaksiKasir),
            'total_transaksi_detail' => count($transaksiDetail),
            'total_produk_sales' => count($produkSales),
            'transaksi_kasir_sample' => array_slice($transaksiKasir, 0, 2),
            'transaksi_detail_sample' => array_slice($transaksiDetail, 0, 3),
            'produk_sales_sample' => array_slice($produkSales, 0, 3),
            'jenis_item_breakdown' => $this->getJenisItemBreakdown($tanggal)
        ]);
    }

    /**
     * Get breakdown of jenis_item in transactions
     */
    private function getJenisItemBreakdown($tanggal)
    {
        $breakdown = $this->transaksiKasirDetailModel
                         ->select('jenis_item, COUNT(*) as count, SUM(subtotal) as total')
                         ->join('transaksi_kasir', 'transaksi_kasir.id = transaksi_kasir_detail.transaksi_kasir_id')
                         ->where('DATE(transaksi_kasir.tanggal_transaksi)', $tanggal)
                         ->groupBy('jenis_item')
                         ->findAll();

        return $breakdown;
    }

    /**
     * Debug produk specifically
     */
    public function debugProduk()
    {
        $tanggal = $this->request->getGet('tanggal') ?: date('Y-m-d');

        // Raw query untuk debug
        $db = \Config\Database::connect();

        // Check all transaksi kasir today
        $transaksiKasir = $db->query("
            SELECT * FROM transaksi_kasir
            WHERE DATE(tanggal_transaksi) = ?
            ORDER BY tanggal_transaksi DESC
        ", [$tanggal])->getResultArray();

        // Check all transaksi detail today
        $transaksiDetail = $db->query("
            SELECT tkd.*, tk.tanggal_transaksi, tk.nomor_transaksi
            FROM transaksi_kasir_detail tkd
            JOIN transaksi_kasir tk ON tk.id = tkd.transaksi_kasir_id
            WHERE DATE(tk.tanggal_transaksi) = ?
            ORDER BY tk.tanggal_transaksi DESC
        ", [$tanggal])->getResultArray();

        // Check product sales specifically
        $produkSales = $db->query("
            SELECT tkd.*, tk.tanggal_transaksi, tk.nomor_transaksi, p.nama_produk
            FROM transaksi_kasir_detail tkd
            JOIN transaksi_kasir tk ON tk.id = tkd.transaksi_kasir_id
            LEFT JOIN produk p ON p.id = tkd.item_id
            WHERE DATE(tk.tanggal_transaksi) = ?
            AND tkd.jenis_item = 'product_sale'
            ORDER BY tk.tanggal_transaksi DESC
        ", [$tanggal])->getResultArray();

        // Test the exact query used in getLaporanProdukHarian
        $testQuery = $this->transaksiKasirDetailModel
                         ->select('transaksi_kasir_detail.*, produk.nama_produk, produk.kategori, produk.harga_jual')
                         ->join('transaksi_kasir', 'transaksi_kasir.id = transaksi_kasir_detail.transaksi_kasir_id')
                         ->join('produk', 'produk.id = transaksi_kasir_detail.item_id', 'left')
                         ->where('DATE(transaksi_kasir.tanggal_transaksi)', $tanggal)
                         ->where('transaksi_kasir_detail.jenis_item', 'product_sale')
                         ->findAll();

        return $this->response->setJSON([
            'tanggal' => $tanggal,
            'total_transaksi_kasir' => count($transaksiKasir),
            'total_transaksi_detail' => count($transaksiDetail),
            'total_produk_sales' => count($produkSales),
            'test_query_result' => count($testQuery),
            'transaksi_kasir' => $transaksiKasir,
            'transaksi_detail' => $transaksiDetail,
            'produk_sales' => $produkSales,
            'test_query_data' => $testQuery,
            'query_info' => [
                'date_filter' => "DATE(transaksi_kasir.tanggal_transaksi) = '{$tanggal}'",
                'jenis_item_filter' => "jenis_item = 'product_sale'"
            ]
        ]);
    }

    /**
     * Check if there are any product transactions today
     */
    public function checkProdukToday()
    {
        $tanggal = date('Y-m-d');

        // Check if there are any transactions today
        $transaksiHariIni = $this->transaksiKasirModel
                                ->where('DATE(tanggal_transaksi)', $tanggal)
                                ->countAllResults();

        // Check if there are any product sales today
        $produkSalesHariIni = $this->transaksiKasirDetailModel
                                  ->join('transaksi_kasir', 'transaksi_kasir.id = transaksi_kasir_detail.transaksi_kasir_id')
                                  ->where('DATE(transaksi_kasir.tanggal_transaksi)', $tanggal)
                                  ->where('transaksi_kasir_detail.jenis_item', 'product_sale')
                                  ->countAllResults();

        // Get sample data
        $sampleTransaksi = $this->transaksiKasirModel
                               ->where('DATE(tanggal_transaksi)', $tanggal)
                               ->orderBy('tanggal_transaksi', 'DESC')
                               ->limit(3)
                               ->findAll();

        $sampleProdukSales = $this->transaksiKasirDetailModel
                                 ->select('transaksi_kasir_detail.*, transaksi_kasir.nomor_transaksi, transaksi_kasir.tanggal_transaksi')
                                 ->join('transaksi_kasir', 'transaksi_kasir.id = transaksi_kasir_detail.transaksi_kasir_id')
                                 ->where('DATE(transaksi_kasir.tanggal_transaksi)', $tanggal)
                                 ->where('transaksi_kasir_detail.jenis_item', 'product_sale')
                                 ->orderBy('transaksi_kasir.tanggal_transaksi', 'DESC')
                                 ->limit(5)
                                 ->findAll();

        return $this->response->setJSON([
            'tanggal' => $tanggal,
            'total_transaksi' => $transaksiHariIni,
            'total_produk_sales' => $produkSalesHariIni,
            'sample_transaksi' => $sampleTransaksi,
            'sample_produk_sales' => $sampleProdukSales,
            'message' => $produkSalesHariIni > 0 ?
                "Ada {$produkSalesHariIni} transaksi produk hari ini" :
                "Tidak ada transaksi produk hari ini"
        ]);
    }

    /**
     * Create sample product transaction for testing
     */
    public function createSampleProdukTransaction()
    {
        $db = \Config\Database::connect();
        $db->transStart();

        try {
            // Create transaksi kasir
            $transaksiData = [
                'nomor_transaksi' => 'TRX-' . date('Ymd') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT),
                'tanggal_transaksi' => date('Y-m-d H:i:s'),
                'total_item' => 3,
                'total_harga' => 18000,
                'metode_bayar' => 'tunai',
                'jumlah_bayar' => 20000,
                'kembalian' => 2000,
                'kasir' => 'Test Kasir',
                'keterangan' => 'Sample transaksi produk untuk testing laporan',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $transaksiId = $this->transaksiKasirModel->insert($transaksiData);

            // Create transaksi detail for products
            $detailData = [
                [
                    'transaksi_kasir_id' => $transaksiId,
                    'jenis_item' => 'product_sale',
                    'item_id' => 1, // Assuming produk ID 1 exists
                    'nama_item' => 'Indomie Goreng',
                    'harga_satuan' => 5000,
                    'quantity' => 2,
                    'subtotal' => 10000,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'transaksi_kasir_id' => $transaksiId,
                    'jenis_item' => 'product_sale',
                    'item_id' => 2, // Assuming produk ID 2 exists
                    'nama_item' => 'Teh Botol Sosro',
                    'harga_satuan' => 4000,
                    'quantity' => 2,
                    'subtotal' => 8000,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]
            ];

            foreach ($detailData as $detail) {
                $this->transaksiKasirDetailModel->insert($detail);
            }

            $db->transComplete();

            if ($db->transStatus() === false) {
                throw new \Exception('Transaction failed');
            }

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Sample transaksi produk berhasil dibuat',
                'transaksi_id' => $transaksiId,
                'nomor_transaksi' => $transaksiData['nomor_transaksi'],
                'total_harga' => $transaksiData['total_harga'],
                'detail_count' => count($detailData)
            ]);

        } catch (\Exception $e) {
            $db->transRollback();
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Gagal membuat sample transaksi: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Debug all product transactions today with detailed analysis
     */
    public function debugAllProdukToday()
    {
        $tanggal = date('Y-m-d');
        $db = \Config\Database::connect();

        // Get all transaksi kasir today
        $allTransaksi = $db->query("
            SELECT * FROM transaksi_kasir
            WHERE DATE(tanggal_transaksi) = ?
            ORDER BY tanggal_transaksi DESC
        ", [$tanggal])->getResultArray();

        // Get all transaksi detail today (all types)
        $allDetail = $db->query("
            SELECT tkd.*, tk.tanggal_transaksi, tk.nomor_transaksi, tk.kasir
            FROM transaksi_kasir_detail tkd
            JOIN transaksi_kasir tk ON tk.id = tkd.transaksi_kasir_id
            WHERE DATE(tk.tanggal_transaksi) = ?
            ORDER BY tk.tanggal_transaksi DESC, tkd.id DESC
        ", [$tanggal])->getResultArray();

        // Get only product sales
        $produkSales = $db->query("
            SELECT tkd.*, tk.tanggal_transaksi, tk.nomor_transaksi, tk.kasir, p.nama_produk, p.kategori
            FROM transaksi_kasir_detail tkd
            JOIN transaksi_kasir tk ON tk.id = tkd.transaksi_kasir_id
            LEFT JOIN produk p ON p.id = tkd.item_id
            WHERE DATE(tk.tanggal_transaksi) = ?
            AND tkd.jenis_item = 'product_sale'
            ORDER BY tk.tanggal_transaksi DESC, tkd.id DESC
        ", [$tanggal])->getResultArray();

        // Get breakdown by jenis_item
        $jenisBreakdown = $db->query("
            SELECT tkd.jenis_item, COUNT(*) as count, SUM(tkd.subtotal) as total_subtotal
            FROM transaksi_kasir_detail tkd
            JOIN transaksi_kasir tk ON tk.id = tkd.transaksi_kasir_id
            WHERE DATE(tk.tanggal_transaksi) = ?
            GROUP BY tkd.jenis_item
            ORDER BY count DESC
        ", [$tanggal])->getResultArray();

        // Test the exact query from getLaporanProdukHarian
        $testLaporanQuery = $this->transaksiKasirDetailModel
                                ->select('transaksi_kasir_detail.*, produk.nama_produk, produk.kategori, produk.harga_jual')
                                ->join('transaksi_kasir', 'transaksi_kasir.id = transaksi_kasir_detail.transaksi_kasir_id')
                                ->join('produk', 'produk.id = transaksi_kasir_detail.item_id', 'left')
                                ->where('DATE(transaksi_kasir.tanggal_transaksi)', $tanggal)
                                ->where('transaksi_kasir_detail.jenis_item', 'product_sale')
                                ->findAll();

        // Get the actual laporan result
        $laporanResult = $this->getLaporanProdukHarian($tanggal);

        return $this->response->setJSON([
            'tanggal' => $tanggal,
            'summary' => [
                'total_transaksi_kasir' => count($allTransaksi),
                'total_detail_items' => count($allDetail),
                'total_product_sales' => count($produkSales),
                'test_query_count' => count($testLaporanQuery)
            ],
            'all_transaksi_kasir' => $allTransaksi,
            'all_detail_items' => $allDetail,
            'product_sales_only' => $produkSales,
            'jenis_item_breakdown' => $jenisBreakdown,
            'test_laporan_query' => $testLaporanQuery,
            'actual_laporan_result' => $laporanResult,
            'debug_info' => [
                'current_time' => date('Y-m-d H:i:s'),
                'filter_date' => $tanggal,
                'query_used' => "DATE(transaksi_kasir.tanggal_transaksi) = '{$tanggal}' AND jenis_item = 'product_sale'"
            ]
        ]);
    }

    /**
     * Check what jenis_item values exist in database
     */
    public function checkJenisItem()
    {
        $db = \Config\Database::connect();

        // Get all unique jenis_item values
        $jenisItems = $db->query("
            SELECT DISTINCT jenis_item, COUNT(*) as count
            FROM transaksi_kasir_detail
            GROUP BY jenis_item
            ORDER BY count DESC
        ")->getResultArray();

        // Get recent transactions with their jenis_item
        $recentTransactions = $db->query("
            SELECT tkd.jenis_item, tkd.nama_item, tkd.item_id, tk.tanggal_transaksi, tk.nomor_transaksi
            FROM transaksi_kasir_detail tkd
            JOIN transaksi_kasir tk ON tk.id = tkd.transaksi_kasir_id
            ORDER BY tk.tanggal_transaksi DESC, tkd.id DESC
            LIMIT 20
        ")->getResultArray();

        // Check today's transactions specifically
        $todayTransactions = $db->query("
            SELECT tkd.jenis_item, tkd.nama_item, tkd.item_id, tk.tanggal_transaksi, tk.nomor_transaksi
            FROM transaksi_kasir_detail tkd
            JOIN transaksi_kasir tk ON tk.id = tkd.transaksi_kasir_id
            WHERE DATE(tk.tanggal_transaksi) = ?
            ORDER BY tk.tanggal_transaksi DESC, tkd.id DESC
        ", [date('Y-m-d')])->getResultArray();

        return $this->response->setJSON([
            'all_jenis_item_types' => $jenisItems,
            'recent_transactions' => $recentTransactions,
            'today_transactions' => $todayTransactions,
            'expected_jenis_item' => 'product_sale',
            'note' => 'Cek apakah transaksi produk menggunakan jenis_item yang benar'
        ]);
    }

    /**
     * Debug paket data untuk troubleshooting
     */
    public function debugPaket()
    {
        $periode = $this->request->getGet('periode') ?: 'tahun';
        $tahun = $this->request->getGet('tahun') ?: '2025';

        $db = \Config\Database::connect();

        // Check all sesi data
        $allSesi = $db->query("
            SELECT id, id_paket, created_at, status_bayar, harga_total
            FROM sesi
            ORDER BY created_at DESC
            LIMIT 20
        ")->getResultArray();

        // Check sesi for specific year
        $sesiTahun = $db->query("
            SELECT id, id_paket, created_at, status_bayar, harga_total
            FROM sesi
            WHERE YEAR(created_at) = ?
            ORDER BY created_at DESC
        ", [$tahun])->getResultArray();

        // Check paket data
        $allPaket = $db->query("
            SELECT p.*, k.nama_konsol
            FROM paket p
            LEFT JOIN konsol k ON k.id = p.id_konsol
        ")->getResultArray();

        // Test query for specific paket
        $testPaket = $allPaket[0] ?? null;
        $testResult = [];

        if ($testPaket) {
            $testResult = $db->query("
                SELECT COUNT(*) as total_penggunaan,
                       SUM(CASE WHEN status_bayar != 'belum' THEN harga_total ELSE 0 END) as total_pendapatan
                FROM sesi
                WHERE id_paket = ? AND YEAR(created_at) = ?
            ", [$testPaket['id'], $tahun])->getRowArray();
        }

        return $this->response->setJSON([
            'periode' => $periode,
            'tahun' => $tahun,
            'total_sesi_all' => count($allSesi),
            'total_sesi_tahun' => count($sesiTahun),
            'total_paket' => count($allPaket),
            'sample_sesi_all' => array_slice($allSesi, 0, 5),
            'sample_sesi_tahun' => array_slice($sesiTahun, 0, 5),
            'all_paket' => $allPaket,
            'test_paket' => $testPaket,
            'test_result' => $testResult
        ]);
    }

    /**
     * Debug member data untuk troubleshooting
     */
    public function debugMember()
    {
        $periode = $this->request->getGet('periode') ?: 'hari';
        $tanggal = $this->request->getGet('tanggal') ?: date('Y-m-d');
        $bulan = $this->request->getGet('bulan') ?: date('Y-m');
        $tahun = $this->request->getGet('tahun') ?: date('Y');

        $db = \Config\Database::connect();

        // Check all member data
        $allMembers = $db->query("
            SELECT id, id_member, nama, no_wa, saldo, created_at
            FROM member
            ORDER BY created_at DESC
            LIMIT 10
        ")->getResultArray();

        // Check sesi data for members
        $sesiData = $db->query("
            SELECT id_member, COUNT(*) as total_sesi, SUM(harga_total) as total_pengeluaran
            FROM sesi
            WHERE status_bayar != 'belum'
            GROUP BY id_member
            ORDER BY total_sesi DESC
            LIMIT 10
        ")->getResultArray();

        // Check topup data for members
        $topupData = $db->query("
            SELECT id_member, COUNT(*) as total_transaksi, SUM(jumlah) as total_topup
            FROM topup
            GROUP BY id_member
            ORDER BY total_topup DESC
            LIMIT 10
        ")->getResultArray();

        // Test specific period filter
        $testMember = $allMembers[0] ?? null;
        $testResult = [];

        if ($testMember) {
            switch ($periode) {
                case 'hari':
                    $testResult = $db->query("
                        SELECT
                            (SELECT COUNT(*) FROM sesi WHERE id_member = ? AND DATE(created_at) = ?) as sesi_hari,
                            (SELECT SUM(jumlah) FROM topup WHERE id_member = ? AND DATE(created_at) = ?) as topup_hari,
                            (SELECT SUM(harga_total) FROM sesi WHERE id_member = ? AND DATE(created_at) = ? AND status_bayar != 'belum') as pengeluaran_hari
                    ", [$testMember['id'], $tanggal, $testMember['id'], $tanggal, $testMember['id'], $tanggal])->getRowArray();
                    break;
                case 'bulan':
                    $testResult = $db->query("
                        SELECT
                            (SELECT COUNT(*) FROM sesi WHERE id_member = ? AND DATE_FORMAT(created_at, '%Y-%m') = ?) as sesi_bulan,
                            (SELECT SUM(jumlah) FROM topup WHERE id_member = ? AND DATE_FORMAT(created_at, '%Y-%m') = ?) as topup_bulan,
                            (SELECT SUM(harga_total) FROM sesi WHERE id_member = ? AND DATE_FORMAT(created_at, '%Y-%m') = ? AND status_bayar != 'belum') as pengeluaran_bulan
                    ", [$testMember['id'], $bulan, $testMember['id'], $bulan, $testMember['id'], $bulan])->getRowArray();
                    break;
                case 'tahun':
                    $testResult = $db->query("
                        SELECT
                            (SELECT COUNT(*) FROM sesi WHERE id_member = ? AND YEAR(created_at) = ?) as sesi_tahun,
                            (SELECT SUM(jumlah) FROM topup WHERE id_member = ? AND YEAR(created_at) = ?) as topup_tahun,
                            (SELECT SUM(harga_total) FROM sesi WHERE id_member = ? AND YEAR(created_at) = ? AND status_bayar != 'belum') as pengeluaran_tahun
                    ", [$testMember['id'], $tahun, $testMember['id'], $tahun, $testMember['id'], $tahun])->getRowArray();
                    break;
            }
        }

        return $this->response->setJSON([
            'periode' => $periode,
            'tanggal' => $tanggal,
            'bulan' => $bulan,
            'tahun' => $tahun,
            'total_members' => count($allMembers),
            'all_members' => $allMembers,
            'sesi_data' => $sesiData,
            'topup_data' => $topupData,
            'test_member' => $testMember,
            'test_result' => $testResult
        ]);
    }

    /**
     * Laporan penggunaan konsol
     */
    public function laporanKonsol()
    {
        $periode = $this->request->getGet('periode') ?: 'hari';
        $tanggal = $this->request->getGet('tanggal') ?: date('Y-m-d');
        
        try {
            $data = [];
            
            switch ($periode) {
                case 'hari':
                    $data = $this->getLaporanKonsolHarian($tanggal);
                    break;
                case 'bulan':
                    $bulan = $this->request->getGet('bulan') ?: date('Y-m');
                    $data = $this->getLaporanKonsolBulanan($bulan);
                    break;
                case 'tahun':
                    $tahun = $this->request->getGet('tahun') ?: date('Y');
                    $data = $this->getLaporanKonsolTahunan($tahun);
                    break;
            }
            
            return $this->response->setJSON([
                'success' => true,
                'data' => $data,
                'periode' => $periode
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Gagal mengambil laporan konsol: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Laporan penggunaan station
     */
    public function laporanStation()
    {
        $periode = $this->request->getGet('periode') ?: 'hari';
        $tanggal = $this->request->getGet('tanggal') ?: date('Y-m-d');
        
        try {
            $data = [];
            
            switch ($periode) {
                case 'hari':
                    $data = $this->getLaporanStationHarian($tanggal);
                    break;
                case 'bulan':
                    $bulan = $this->request->getGet('bulan') ?: date('Y-m');
                    $data = $this->getLaporanStationBulanan($bulan);
                    break;
                case 'tahun':
                    $tahun = $this->request->getGet('tahun') ?: date('Y');
                    $data = $this->getLaporanStationTahunan($tahun);
                    break;
            }
            
            return $this->response->setJSON([
                'success' => true,
                'data' => $data,
                'periode' => $periode
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Gagal mengambil laporan station: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Laporan member dengan filter periode
     */
    public function laporanMember()
    {
        $periode = $this->request->getGet('periode') ?: 'hari';
        $tanggal = $this->request->getGet('tanggal') ?: date('Y-m-d');
        $bulan = $this->request->getGet('bulan') ?: date('Y-m');
        $tahun = $this->request->getGet('tahun') ?: date('Y');

        try {
            $members = $this->memberModel->findAll();
            $memberStats = [];

            foreach ($members as $member) {
                // Build queries based on periode
                $sesiQuery = $this->sesiModel->where('id_member', $member['id']);
                $topupQuery = $this->topupModel->selectSum('jumlah')->where('id_member', $member['id']);
                $pengeluaranQuery = $this->sesiModel->selectSum('harga_total')
                                                   ->where('id_member', $member['id'])
                                                   ->where('status_bayar !=', 'belum');

                // Apply date filter based on periode
                switch ($periode) {
                    case 'hari':
                        $sesiQuery->where('DATE(created_at)', $tanggal);
                        $topupQuery->where('DATE(created_at)', $tanggal);
                        $pengeluaranQuery->where('DATE(created_at)', $tanggal);
                        break;
                    case 'bulan':
                        $sesiQuery->where('DATE_FORMAT(created_at, "%Y-%m")', $bulan);
                        $topupQuery->where('DATE_FORMAT(created_at, "%Y-%m")', $bulan);
                        $pengeluaranQuery->where('DATE_FORMAT(created_at, "%Y-%m")', $bulan);
                        break;
                    case 'tahun':
                        $sesiQuery->where('YEAR(created_at)', $tahun);
                        $topupQuery->where('YEAR(created_at)', $tahun);
                        $pengeluaranQuery->where('YEAR(created_at)', $tahun);
                        break;
                }

                // Get results
                $totalSesi = $sesiQuery->countAllResults();
                $topupResult = $topupQuery->first();
                $totalTopup = $topupResult['jumlah'] ?: 0;
                $pengeluaranResult = $pengeluaranQuery->first();
                $totalPengeluaran = $pengeluaranResult['harga_total'] ?: 0;

                // Include all members, but with filtered activity data
                $memberStats[] = [
                    'id' => $member['id'],
                    'id_member' => $member['id_member'],
                    'nama' => $member['nama'],
                    'no_wa' => $member['no_wa'],
                    'saldo' => $member['saldo'],
                    'total_sesi' => $totalSesi,
                    'total_topup' => $totalTopup,
                    'total_pengeluaran' => $totalPengeluaran,
                    'created_at' => $member['created_at'],
                    'has_activity' => ($totalSesi > 0 || $totalTopup > 0 || $totalPengeluaran > 0)
                ];
            }

            // Sort by total sesi descending
            usort($memberStats, function($a, $b) {
                return $b['total_sesi'] - $a['total_sesi'];
            });

            return $this->response->setJSON([
                'success' => true,
                'data' => $memberStats,
                'periode_info' => [
                    'periode' => $periode,
                    'tanggal' => $tanggal,
                    'bulan' => $bulan,
                    'tahun' => $tahun
                ]
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Gagal mengambil laporan member: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Laporan paket dengan filter periode
     */
    public function laporanPaket()
    {
        $periode = $this->request->getGet('periode') ?: 'hari';
        $tanggal = $this->request->getGet('tanggal') ?: date('Y-m-d');
        $bulan = $this->request->getGet('bulan') ?: date('Y-m');
        $tahun = $this->request->getGet('tahun') ?: date('Y');

        try {
            // Get pakets with konsol information
            $pakets = $this->paketModel->select('paket.*, konsol.nama_konsol')
                                      ->join('konsol', 'konsol.id = paket.id_konsol', 'left')
                                      ->findAll();
            $paketStats = [];

            foreach ($pakets as $paket) {
                // Build query based on periode
                $sesiQuery = $this->sesiModel->where('id_paket', $paket['id']);
                $pendapatanQuery = $this->sesiModel->selectSum('harga_total')
                                                  ->where('id_paket', $paket['id'])
                                                  ->where('status_bayar !=', 'belum');

                // Apply date filter based on periode
                switch ($periode) {
                    case 'hari':
                        $sesiQuery->where('DATE(created_at)', $tanggal);
                        $pendapatanQuery->where('DATE(created_at)', $tanggal);
                        break;
                    case 'bulan':
                        $sesiQuery->where('DATE_FORMAT(created_at, "%Y-%m")', $bulan);
                        $pendapatanQuery->where('DATE_FORMAT(created_at, "%Y-%m")', $bulan);
                        break;
                    case 'tahun':
                        $sesiQuery->where('YEAR(created_at)', $tahun);
                        $pendapatanQuery->where('YEAR(created_at)', $tahun);
                        break;
                }

                // Get results
                $penggunaan = $sesiQuery->countAllResults();
                $pendapatanResult = $pendapatanQuery->first();
                $totalPendapatan = $pendapatanResult['harga_total'] ?: 0;

                $paketStats[] = [
                    'id' => $paket['id'],
                    'nama_paket' => $paket['nama_paket'],
                    'nama_konsol' => $paket['nama_konsol'] ?? 'Tidak ada konsol',
                    'harga' => $paket['harga'],
                    'durasi' => $paket['durasi'],
                    'total_penggunaan' => $penggunaan,
                    'total_pendapatan' => $totalPendapatan
                ];
            }

            // Sort by total penggunaan descending
            usort($paketStats, function($a, $b) {
                return $b['total_penggunaan'] - $a['total_penggunaan'];
            });

            return $this->response->setJSON([
                'success' => true,
                'data' => $paketStats,
                'periode_info' => [
                    'periode' => $periode,
                    'tanggal' => $tanggal,
                    'bulan' => $bulan,
                    'tahun' => $tahun
                ]
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Gagal mengambil laporan paket: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Laporan topup member
     */
    public function laporanTopup()
    {
        $periode = $this->request->getGet('periode') ?: 'hari';
        $tanggal = $this->request->getGet('tanggal') ?: date('Y-m-d');
        
        try {
            $data = [];
            
            switch ($periode) {
                case 'hari':
                    $data = $this->getLaporanTopupHarian($tanggal);
                    break;
                case 'bulan':
                    $bulan = $this->request->getGet('bulan') ?: date('Y-m');
                    $data = $this->getLaporanTopupBulanan($bulan);
                    break;
                case 'tahun':
                    $tahun = $this->request->getGet('tahun') ?: date('Y');
                    $data = $this->getLaporanTopupTahunan($tahun);
                    break;
            }
            
            return $this->response->setJSON([
                'success' => true,
                'data' => $data,
                'periode' => $periode
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Gagal mengambil laporan topup: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Laporan pendapatan
     */
    public function laporanPendapatan()
    {
        $periode = $this->request->getGet('periode') ?: 'hari';
        $tanggal = $this->request->getGet('tanggal') ?: date('Y-m-d');
        
        try {
            $data = [];
            
            switch ($periode) {
                case 'hari':
                    $data = $this->getLaporanPendapatanHarian($tanggal);
                    break;
                case 'bulan':
                    $bulan = $this->request->getGet('bulan') ?: date('Y-m');
                    $data = $this->getLaporanPendapatanBulanan($bulan);
                    break;
                case 'tahun':
                    $tahun = $this->request->getGet('tahun') ?: date('Y');
                    $data = $this->getLaporanPendapatanTahunan($tahun);
                    break;
            }
            
            return $this->response->setJSON([
                'success' => true,
                'data' => $data,
                'periode' => $periode
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Gagal mengambil laporan pendapatan: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Laporan penjualan produk
     */
    public function laporanProduk()
    {
        $periode = $this->request->getGet('periode') ?: 'hari';
        $tanggal = $this->request->getGet('tanggal') ?: date('Y-m-d');
        
        try {
            $data = [];
            
            switch ($periode) {
                case 'hari':
                    $data = $this->getLaporanProdukHarian($tanggal);
                    break;
                case 'bulan':
                    $bulan = $this->request->getGet('bulan') ?: date('Y-m');
                    $data = $this->getLaporanProdukBulanan($bulan);
                    break;
                case 'tahun':
                    $tahun = $this->request->getGet('tahun') ?: date('Y');
                    $data = $this->getLaporanProdukTahunan($tahun);
                    break;
            }
            
            return $this->response->setJSON([
                'success' => true,
                'data' => $data,
                'periode' => $periode
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Gagal mengambil laporan produk: ' . $e->getMessage()
            ]);
        }
    }

    // ========== HELPER METHODS FOR DATA QUERIES ==========

    /**
     * Get laporan konsol harian
     */
    private function getLaporanKonsolHarian($tanggal)
    {
        $konsols = $this->konsolModel->findAll();
        $data = [];

        foreach ($konsols as $konsol) {
            // Get stations for this konsol
            $stations = $this->stationModel->where('id_konsol', $konsol['id'])->findAll();
            $stationIds = array_column($stations, 'id');

            $totalSesi = 0;
            $totalDurasi = 0;
            $totalPendapatan = 0;

            if (!empty($stationIds)) {
                // Count sessions for this konsol's stations
                $totalSesi = $this->sesiModel->whereIn('station_id', $stationIds)
                                            ->where('DATE(created_at)', $tanggal)
                                            ->countAllResults();

                // Get all sessions for duration calculation
                $sesiList = $this->sesiModel->whereIn('station_id', $stationIds)
                                           ->where('DATE(created_at)', $tanggal)
                                           ->findAll();

                foreach ($sesiList as $sesi) {
                    if ($sesi['waktu_mulai'] && $sesi['waktu_berhenti']) {
                        $start = new \DateTime($sesi['waktu_mulai']);
                        $end = new \DateTime($sesi['waktu_berhenti']);
                        $diff = $end->diff($start);
                        $totalDurasi += ($diff->h * 60) + $diff->i;
                    }
                }

                // Sum revenue for paid sessions
                $pendapatanResult = $this->sesiModel->selectSum('harga_total')
                                                   ->whereIn('station_id', $stationIds)
                                                   ->where('DATE(created_at)', $tanggal)
                                                   ->where('status_bayar !=', 'belum')
                                                   ->first();
                $totalPendapatan = $pendapatanResult['harga_total'] ?: 0;
            }

            $data[] = [
                'konsol' => $konsol['nama_konsol'],
                'total_sesi' => $totalSesi,
                'total_durasi' => $totalDurasi,
                'total_pendapatan' => $totalPendapatan
            ];
        }

        return $data;
    }

    /**
     * Get laporan konsol bulanan
     */
    private function getLaporanKonsolBulanan($bulan)
    {
        $konsols = $this->konsolModel->findAll();
        $data = [];

        foreach ($konsols as $konsol) {
            // Get stations for this konsol
            $stations = $this->stationModel->where('id_konsol', $konsol['id'])->findAll();
            $stationIds = array_column($stations, 'id');

            if (empty($stationIds)) {
                $totalSesi = 0;
                $totalDurasi = 0;
                $totalPendapatan = 0;
            } else {
                $totalSesi = $this->sesiModel->whereIn('station_id', $stationIds)
                                            ->where('DATE_FORMAT(created_at, "%Y-%m")', $bulan)
                                            ->countAllResults();

                // Calculate duration from waktu_mulai and waktu_berhenti
                $sesiList = $this->sesiModel->whereIn('station_id', $stationIds)
                                           ->where('DATE_FORMAT(created_at, "%Y-%m")', $bulan)
                                           ->findAll();

                $totalDurasi = 0;
                foreach ($sesiList as $sesi) {
                    if ($sesi['waktu_mulai'] && $sesi['waktu_berhenti']) {
                        $start = new \DateTime($sesi['waktu_mulai']);
                        $end = new \DateTime($sesi['waktu_berhenti']);
                        $diff = $end->diff($start);
                        $totalDurasi += ($diff->h * 60) + $diff->i;
                    }
                }

                $totalPendapatan = $this->sesiModel->selectSum('harga_total')
                                                  ->whereIn('station_id', $stationIds)
                                                  ->where('DATE_FORMAT(created_at, "%Y-%m")', $bulan)
                                                  ->where('status_bayar !=', 'belum')
                                                  ->first()['harga_total'] ?: 0;
            }

            $data[] = [
                'konsol' => $konsol['nama_konsol'],
                'total_sesi' => $totalSesi,
                'total_durasi' => $totalDurasi,
                'total_pendapatan' => $totalPendapatan
            ];
        }

        return $data;
    }

    /**
     * Get laporan konsol tahunan
     */
    private function getLaporanKonsolTahunan($tahun)
    {
        $konsols = $this->konsolModel->findAll();
        $data = [];

        foreach ($konsols as $konsol) {
            // Get stations for this konsol
            $stations = $this->stationModel->where('id_konsol', $konsol['id'])->findAll();
            $stationIds = array_column($stations, 'id');

            if (empty($stationIds)) {
                $totalSesi = 0;
                $totalDurasi = 0;
                $totalPendapatan = 0;
            } else {
                $totalSesi = $this->sesiModel->whereIn('station_id', $stationIds)
                                            ->where('YEAR(created_at)', $tahun)
                                            ->countAllResults();

                // Calculate duration from waktu_mulai and waktu_berhenti
                $sesiList = $this->sesiModel->whereIn('station_id', $stationIds)
                                           ->where('YEAR(created_at)', $tahun)
                                           ->findAll();

                $totalDurasi = 0;
                foreach ($sesiList as $sesi) {
                    if ($sesi['waktu_mulai'] && $sesi['waktu_berhenti']) {
                        $start = new \DateTime($sesi['waktu_mulai']);
                        $end = new \DateTime($sesi['waktu_berhenti']);
                        $diff = $end->diff($start);
                        $totalDurasi += ($diff->h * 60) + $diff->i;
                    }
                }

                $totalPendapatan = $this->sesiModel->selectSum('harga_total')
                                                  ->whereIn('station_id', $stationIds)
                                                  ->where('YEAR(created_at)', $tahun)
                                                  ->where('status_bayar !=', 'belum')
                                                  ->first()['harga_total'] ?: 0;
            }

            $data[] = [
                'konsol' => $konsol['nama_konsol'],
                'total_sesi' => $totalSesi,
                'total_durasi' => $totalDurasi,
                'total_pendapatan' => $totalPendapatan
            ];
        }

        return $data;
    }

    /**
     * Get laporan station harian
     */
    private function getLaporanStationHarian($tanggal)
    {
        $stations = $this->stationModel->findAll();
        $data = [];

        foreach ($stations as $station) {
            $totalSesi = $this->sesiModel->where('station_id', $station['id'])
                                        ->where('DATE(created_at)', $tanggal)
                                        ->countAllResults();

            // Calculate duration from waktu_mulai and waktu_berhenti
            $sesiList = $this->sesiModel->where('station_id', $station['id'])
                                       ->where('DATE(created_at)', $tanggal)
                                       ->findAll();

            $totalDurasi = 0;
            foreach ($sesiList as $sesi) {
                if ($sesi['waktu_mulai'] && $sesi['waktu_berhenti']) {
                    $start = new \DateTime($sesi['waktu_mulai']);
                    $end = new \DateTime($sesi['waktu_berhenti']);
                    $diff = $end->diff($start);
                    $totalDurasi += ($diff->h * 60) + $diff->i;
                }
            }

            $pendapatanResult = $this->sesiModel->selectSum('harga_total')
                                               ->where('station_id', $station['id'])
                                               ->where('DATE(created_at)', $tanggal)
                                               ->where('status_bayar !=', 'belum')
                                               ->first();
            $totalPendapatan = $pendapatanResult['harga_total'] ?: 0;

            // Only include stations with data
            if ($totalSesi > 0 || $totalDurasi > 0 || $totalPendapatan > 0) {
                $data[] = [
                    'station' => $station['nama_station'],
                    'total_sesi' => $totalSesi,
                    'total_durasi' => $totalDurasi,
                    'total_pendapatan' => $totalPendapatan
                ];
            }
        }

        return $data;
    }

    /**
     * Get laporan station bulanan
     */
    private function getLaporanStationBulanan($bulan)
    {
        $stations = $this->stationModel->findAll();
        $data = [];

        foreach ($stations as $station) {
            $totalSesi = $this->sesiModel->where('station_id', $station['id'])
                                        ->where('DATE_FORMAT(created_at, "%Y-%m")', $bulan)
                                        ->countAllResults();

            // Calculate duration from waktu_mulai and waktu_berhenti
            $sesiList = $this->sesiModel->where('station_id', $station['id'])
                                       ->where('DATE_FORMAT(created_at, "%Y-%m")', $bulan)
                                       ->findAll();

            $totalDurasi = 0;
            foreach ($sesiList as $sesi) {
                if ($sesi['waktu_mulai'] && $sesi['waktu_berhenti']) {
                    $start = new \DateTime($sesi['waktu_mulai']);
                    $end = new \DateTime($sesi['waktu_berhenti']);
                    $diff = $end->diff($start);
                    $totalDurasi += ($diff->h * 60) + $diff->i;
                }
            }

            $totalPendapatan = $this->sesiModel->selectSum('harga_total')
                                              ->where('station_id', $station['id'])
                                              ->where('DATE_FORMAT(created_at, "%Y-%m")', $bulan)
                                              ->where('status_bayar !=', 'belum')
                                              ->first()['harga_total'] ?: 0;

            $data[] = [
                'station' => $station['nama_station'],
                'total_sesi' => $totalSesi,
                'total_durasi' => $totalDurasi,
                'total_pendapatan' => $totalPendapatan
            ];
        }

        return $data;
    }

    /**
     * Get laporan station tahunan
     */
    private function getLaporanStationTahunan($tahun)
    {
        $stations = $this->stationModel->findAll();
        $data = [];

        foreach ($stations as $station) {
            $totalSesi = $this->sesiModel->where('station_id', $station['id'])
                                        ->where('YEAR(created_at)', $tahun)
                                        ->countAllResults();

            // Calculate duration from waktu_mulai and waktu_berhenti
            $sesiList = $this->sesiModel->where('station_id', $station['id'])
                                       ->where('YEAR(created_at)', $tahun)
                                       ->findAll();

            $totalDurasi = 0;
            foreach ($sesiList as $sesi) {
                if ($sesi['waktu_mulai'] && $sesi['waktu_berhenti']) {
                    $start = new \DateTime($sesi['waktu_mulai']);
                    $end = new \DateTime($sesi['waktu_berhenti']);
                    $diff = $end->diff($start);
                    $totalDurasi += ($diff->h * 60) + $diff->i;
                }
            }

            $totalPendapatan = $this->sesiModel->selectSum('harga_total')
                                              ->where('station_id', $station['id'])
                                              ->where('YEAR(created_at)', $tahun)
                                              ->where('status_bayar !=', 'belum')
                                              ->first()['harga_total'] ?: 0;

            $data[] = [
                'station' => $station['nama_station'],
                'total_sesi' => $totalSesi,
                'total_durasi' => $totalDurasi,
                'total_pendapatan' => $totalPendapatan
            ];
        }

        return $data;
    }

    /**
     * Get laporan topup harian - breakdown per jam
     */
    private function getLaporanTopupHarian($tanggal)
    {
        // Get topup data grouped by hour
        $db = \Config\Database::connect();
        $hourlyData = $db->query("
            SELECT
                HOUR(created_at) as jam,
                COUNT(*) as total_transaksi,
                SUM(jumlah) as total_topup
            FROM topup
            WHERE DATE(created_at) = ?
            GROUP BY HOUR(created_at)
            ORDER BY jam
        ", [$tanggal])->getResultArray();

        // Create 24-hour array (0-23)
        $labels = [];
        $transaksiData = [];
        $topupData = [];

        for ($i = 0; $i < 24; $i++) {
            $labels[] = sprintf('%02d:00', $i);
            $found = false;

            foreach ($hourlyData as $data) {
                if ($data['jam'] == $i) {
                    $transaksiData[] = (int)$data['total_transaksi'];
                    $topupData[] = (int)$data['total_topup'];
                    $found = true;
                    break;
                }
            }

            if (!$found) {
                $transaksiData[] = 0;
                $topupData[] = 0;
            }
        }

        $totalTopup = array_sum($topupData);
        $totalTransaksi = array_sum($transaksiData);

        return [
            'total_topup' => $totalTopup,
            'total_transaksi' => $totalTransaksi,
            'chart_data' => [
                'labels' => $labels,
                'transaksi' => $transaksiData,
                'topup' => $topupData
            ],
            'periode_type' => 'harian'
        ];
    }

    /**
     * Get laporan topup bulanan - breakdown per tanggal (1-31)
     */
    private function getLaporanTopupBulanan($bulan)
    {
        // Get topup data grouped by day
        $db = \Config\Database::connect();
        $dailyData = $db->query("
            SELECT
                DAY(created_at) as tanggal,
                COUNT(*) as total_transaksi,
                SUM(jumlah) as total_topup
            FROM topup
            WHERE DATE_FORMAT(created_at, '%Y-%m') = ?
            GROUP BY DAY(created_at)
            ORDER BY tanggal
        ", [$bulan])->getResultArray();

        // Get number of days in the month
        $year = substr($bulan, 0, 4);
        $month = substr($bulan, 5, 2);
        $daysInMonth = cal_days_in_month(CAL_GREGORIAN, (int)$month, (int)$year);

        // Create daily array (1 to last day of month)
        $labels = [];
        $transaksiData = [];
        $topupData = [];

        for ($i = 1; $i <= $daysInMonth; $i++) {
            $labels[] = $i;
            $found = false;

            foreach ($dailyData as $data) {
                if ($data['tanggal'] == $i) {
                    $transaksiData[] = (int)$data['total_transaksi'];
                    $topupData[] = (int)$data['total_topup'];
                    $found = true;
                    break;
                }
            }

            if (!$found) {
                $transaksiData[] = 0;
                $topupData[] = 0;
            }
        }

        $totalTopup = array_sum($topupData);
        $totalTransaksi = array_sum($transaksiData);

        return [
            'total_topup' => $totalTopup,
            'total_transaksi' => $totalTransaksi,
            'chart_data' => [
                'labels' => $labels,
                'transaksi' => $transaksiData,
                'topup' => $topupData
            ],
            'periode_type' => 'bulanan',
            'bulan_info' => [
                'year' => $year,
                'month' => $month,
                'days_in_month' => $daysInMonth
            ]
        ];
    }

    /**
     * Get laporan topup tahunan - breakdown per bulan (Januari-Desember)
     */
    private function getLaporanTopupTahunan($tahun)
    {
        // Get topup data grouped by month
        $db = \Config\Database::connect();
        $monthlyData = $db->query("
            SELECT
                MONTH(created_at) as bulan,
                COUNT(*) as total_transaksi,
                SUM(jumlah) as total_topup
            FROM topup
            WHERE YEAR(created_at) = ?
            GROUP BY MONTH(created_at)
            ORDER BY bulan
        ", [$tahun])->getResultArray();

        // Create monthly array (1-12)
        $monthNames = [
            1 => 'Januari', 2 => 'Februari', 3 => 'Maret', 4 => 'April',
            5 => 'Mei', 6 => 'Juni', 7 => 'Juli', 8 => 'Agustus',
            9 => 'September', 10 => 'Oktober', 11 => 'November', 12 => 'Desember'
        ];

        $labels = [];
        $transaksiData = [];
        $topupData = [];

        for ($i = 1; $i <= 12; $i++) {
            $labels[] = $monthNames[$i];
            $found = false;

            foreach ($monthlyData as $data) {
                if ($data['bulan'] == $i) {
                    $transaksiData[] = (int)$data['total_transaksi'];
                    $topupData[] = (int)$data['total_topup'];
                    $found = true;
                    break;
                }
            }

            if (!$found) {
                $transaksiData[] = 0;
                $topupData[] = 0;
            }
        }

        $totalTopup = array_sum($topupData);
        $totalTransaksi = array_sum($transaksiData);

        return [
            'total_topup' => $totalTopup,
            'total_transaksi' => $totalTransaksi,
            'chart_data' => [
                'labels' => $labels,
                'transaksi' => $transaksiData,
                'topup' => $topupData
            ],
            'periode_type' => 'tahunan',
            'tahun' => $tahun
        ];
    }

    /**
     * Get laporan pendapatan harian - breakdown per jam
     */
    private function getLaporanPendapatanHarian($tanggal)
    {
        $db = \Config\Database::connect();

        // Get hourly revenue breakdown
        $hourlyRevenue = $db->query("
            SELECT
                HOUR(created_at) as jam,
                SUM(CASE WHEN status_bayar != 'belum' THEN harga_total ELSE 0 END) as pendapatan_sesi
            FROM sesi
            WHERE DATE(created_at) = ?
            GROUP BY HOUR(created_at)
            ORDER BY jam
        ", [$tanggal])->getResultArray();

        $hourlyTopup = $db->query("
            SELECT
                HOUR(created_at) as jam,
                SUM(jumlah) as pendapatan_topup
            FROM topup
            WHERE DATE(created_at) = ?
            GROUP BY HOUR(created_at)
            ORDER BY jam
        ", [$tanggal])->getResultArray();

        $hourlyProduk = $db->query("
            SELECT
                HOUR(tk.tanggal_transaksi) as jam,
                SUM(tkd.subtotal) as pendapatan_produk
            FROM transaksi_kasir_detail tkd
            JOIN transaksi_kasir tk ON tk.id = tkd.transaksi_kasir_id
            WHERE DATE(tk.tanggal_transaksi) = ?
            AND tkd.jenis_item = 'product_sale'
            GROUP BY HOUR(tk.tanggal_transaksi)
            ORDER BY jam
        ", [$tanggal])->getResultArray();

        // Create 24-hour arrays
        $labels = [];
        $sesiData = [];
        $topupData = [];
        $produkData = [];
        $totalData = [];

        for ($i = 0; $i < 24; $i++) {
            $labels[] = sprintf('%02d:00', $i);

            $sesi = 0;
            $topup = 0;
            $produk = 0;

            // Find sesi data for this hour
            foreach ($hourlyRevenue as $data) {
                if ($data['jam'] == $i) {
                    $sesi = (int)$data['pendapatan_sesi'];
                    break;
                }
            }

            // Find topup data for this hour
            foreach ($hourlyTopup as $data) {
                if ($data['jam'] == $i) {
                    $topup = (int)$data['pendapatan_topup'];
                    break;
                }
            }

            // Find produk data for this hour
            foreach ($hourlyProduk as $data) {
                if ($data['jam'] == $i) {
                    $produk = (int)$data['pendapatan_produk'];
                    break;
                }
            }

            $sesiData[] = $sesi;
            $topupData[] = $topup;
            $produkData[] = $produk;
            $totalData[] = $sesi + $topup + $produk;
        }

        return [
            'pendapatan_sesi' => array_sum($sesiData),
            'pendapatan_topup' => array_sum($topupData),
            'pendapatan_produk' => array_sum($produkData),
            'total_pendapatan' => array_sum($totalData),
            'chart_data' => [
                'labels' => $labels,
                'sesi' => $sesiData,
                'topup' => $topupData,
                'produk' => $produkData,
                'total' => $totalData
            ],
            'periode_type' => 'harian'
        ];
    }

    /**
     * Get laporan pendapatan bulanan - breakdown per tanggal
     */
    private function getLaporanPendapatanBulanan($bulan)
    {
        $db = \Config\Database::connect();

        // Get daily revenue breakdown for the month
        $dailyRevenue = $db->query("
            SELECT
                DAY(created_at) as tanggal,
                SUM(CASE WHEN status_bayar != 'belum' THEN harga_total ELSE 0 END) as pendapatan_sesi
            FROM sesi
            WHERE DATE_FORMAT(created_at, '%Y-%m') = ?
            GROUP BY DAY(created_at)
            ORDER BY tanggal
        ", [$bulan])->getResultArray();

        $dailyTopup = $db->query("
            SELECT
                DAY(created_at) as tanggal,
                SUM(jumlah) as pendapatan_topup
            FROM topup
            WHERE DATE_FORMAT(created_at, '%Y-%m') = ?
            GROUP BY DAY(created_at)
            ORDER BY tanggal
        ", [$bulan])->getResultArray();

        $dailyProduk = $db->query("
            SELECT
                DAY(tk.tanggal_transaksi) as tanggal,
                SUM(tkd.subtotal) as pendapatan_produk
            FROM transaksi_kasir_detail tkd
            JOIN transaksi_kasir tk ON tk.id = tkd.transaksi_kasir_id
            WHERE DATE_FORMAT(tk.tanggal_transaksi, '%Y-%m') = ?
            AND tkd.jenis_item = 'product_sale'
            GROUP BY DAY(tk.tanggal_transaksi)
            ORDER BY tanggal
        ", [$bulan])->getResultArray();

        // Get number of days in the month
        $year = substr($bulan, 0, 4);
        $month = substr($bulan, 5, 2);
        $daysInMonth = cal_days_in_month(CAL_GREGORIAN, (int)$month, (int)$year);

        // Create daily arrays
        $labels = [];
        $sesiData = [];
        $topupData = [];
        $produkData = [];
        $totalData = [];

        for ($i = 1; $i <= $daysInMonth; $i++) {
            $labels[] = $i;

            $sesi = 0;
            $topup = 0;
            $produk = 0;

            // Find data for this day
            foreach ($dailyRevenue as $data) {
                if ($data['tanggal'] == $i) {
                    $sesi = (int)$data['pendapatan_sesi'];
                    break;
                }
            }

            foreach ($dailyTopup as $data) {
                if ($data['tanggal'] == $i) {
                    $topup = (int)$data['pendapatan_topup'];
                    break;
                }
            }

            foreach ($dailyProduk as $data) {
                if ($data['tanggal'] == $i) {
                    $produk = (int)$data['pendapatan_produk'];
                    break;
                }
            }

            $sesiData[] = $sesi;
            $topupData[] = $topup;
            $produkData[] = $produk;
            $totalData[] = $sesi + $topup + $produk;
        }

        return [
            'pendapatan_sesi' => array_sum($sesiData),
            'pendapatan_topup' => array_sum($topupData),
            'pendapatan_produk' => array_sum($produkData),
            'total_pendapatan' => array_sum($totalData),
            'chart_data' => [
                'labels' => $labels,
                'sesi' => $sesiData,
                'topup' => $topupData,
                'produk' => $produkData,
                'total' => $totalData
            ],
            'periode_type' => 'bulanan',
            'bulan_info' => [
                'year' => $year,
                'month' => $month,
                'days_in_month' => $daysInMonth
            ]
        ];
    }

    /**
     * Get laporan pendapatan tahunan - breakdown per bulan
     */
    private function getLaporanPendapatanTahunan($tahun)
    {
        $db = \Config\Database::connect();

        // Get monthly revenue breakdown for the year
        $monthlyRevenue = $db->query("
            SELECT
                MONTH(created_at) as bulan,
                SUM(CASE WHEN status_bayar != 'belum' THEN harga_total ELSE 0 END) as pendapatan_sesi
            FROM sesi
            WHERE YEAR(created_at) = ?
            GROUP BY MONTH(created_at)
            ORDER BY bulan
        ", [$tahun])->getResultArray();

        $monthlyTopup = $db->query("
            SELECT
                MONTH(created_at) as bulan,
                SUM(jumlah) as pendapatan_topup
            FROM topup
            WHERE YEAR(created_at) = ?
            GROUP BY MONTH(created_at)
            ORDER BY bulan
        ", [$tahun])->getResultArray();

        $monthlyProduk = $db->query("
            SELECT
                MONTH(tk.tanggal_transaksi) as bulan,
                SUM(tkd.subtotal) as pendapatan_produk
            FROM transaksi_kasir_detail tkd
            JOIN transaksi_kasir tk ON tk.id = tkd.transaksi_kasir_id
            WHERE YEAR(tk.tanggal_transaksi) = ?
            AND tkd.jenis_item = 'product_sale'
            GROUP BY MONTH(tk.tanggal_transaksi)
            ORDER BY bulan
        ", [$tahun])->getResultArray();

        // Create monthly arrays (1-12)
        $monthNames = [
            1 => 'Januari', 2 => 'Februari', 3 => 'Maret', 4 => 'April',
            5 => 'Mei', 6 => 'Juni', 7 => 'Juli', 8 => 'Agustus',
            9 => 'September', 10 => 'Oktober', 11 => 'November', 12 => 'Desember'
        ];

        $labels = [];
        $sesiData = [];
        $topupData = [];
        $produkData = [];
        $totalData = [];

        for ($i = 1; $i <= 12; $i++) {
            $labels[] = $monthNames[$i];

            $sesi = 0;
            $topup = 0;
            $produk = 0;

            // Find data for this month
            foreach ($monthlyRevenue as $data) {
                if ($data['bulan'] == $i) {
                    $sesi = (int)$data['pendapatan_sesi'];
                    break;
                }
            }

            foreach ($monthlyTopup as $data) {
                if ($data['bulan'] == $i) {
                    $topup = (int)$data['pendapatan_topup'];
                    break;
                }
            }

            foreach ($monthlyProduk as $data) {
                if ($data['bulan'] == $i) {
                    $produk = (int)$data['pendapatan_produk'];
                    break;
                }
            }

            $sesiData[] = $sesi;
            $topupData[] = $topup;
            $produkData[] = $produk;
            $totalData[] = $sesi + $topup + $produk;
        }

        return [
            'pendapatan_sesi' => array_sum($sesiData),
            'pendapatan_topup' => array_sum($topupData),
            'pendapatan_produk' => array_sum($produkData),
            'total_pendapatan' => array_sum($totalData),
            'chart_data' => [
                'labels' => $labels,
                'sesi' => $sesiData,
                'topup' => $topupData,
                'produk' => $produkData,
                'total' => $totalData
            ],
            'periode_type' => 'tahunan',
            'tahun' => $tahun
        ];
    }

    /**
     * Get laporan produk harian
     */
    private function getLaporanProdukHarian($tanggal)
    {
        // Get penjualan produk dari transaksi kasir detail
        $penjualanProduk = $this->transaksiKasirDetailModel
                               ->select('transaksi_kasir_detail.*, produk.nama_produk, produk.kategori, produk.harga_jual')
                               ->join('transaksi_kasir', 'transaksi_kasir.id = transaksi_kasir_detail.transaksi_kasir_id')
                               ->join('produk', 'produk.id = transaksi_kasir_detail.item_id', 'left')
                               ->where('DATE(transaksi_kasir.tanggal_transaksi)', $tanggal)
                               ->where('transaksi_kasir_detail.jenis_item', 'product_sale')
                               ->findAll();

        $totalPenjualan = 0;
        $totalPendapatan = 0;
        $produkStats = [];

        foreach ($penjualanProduk as $item) {
            $totalPenjualan += $item['quantity'];
            $totalPendapatan += $item['subtotal'];

            // Group by product
            $produkId = $item['item_id'];
            if (!isset($produkStats[$produkId])) {
                $produkStats[$produkId] = [
                    'nama_produk' => $item['nama_produk'] ?: $item['nama_item'],
                    'kategori' => $item['kategori'] ?: 'Tidak diketahui',
                    'harga_satuan' => $item['harga_satuan'],
                    'total_quantity' => 0,
                    'total_pendapatan' => 0
                ];
            }
            $produkStats[$produkId]['total_quantity'] += $item['quantity'];
            $produkStats[$produkId]['total_pendapatan'] += $item['subtotal'];
        }

        // Sort by quantity untuk produk terlaris
        $produkTerlaris = array_values($produkStats);
        usort($produkTerlaris, function($a, $b) {
            return $b['total_quantity'] - $a['total_quantity'];
        });

        return [
            'total_penjualan' => $totalPenjualan,
            'total_pendapatan' => $totalPendapatan,
            'produk_terlaris' => array_slice($produkTerlaris, 0, 5), // Top 5
            'detail' => array_values($produkStats)
        ];
    }

    /**
     * Get laporan produk bulanan
     */
    private function getLaporanProdukBulanan($bulan)
    {
        // Get penjualan produk dari transaksi kasir detail untuk bulan tertentu
        $penjualanProduk = $this->transaksiKasirDetailModel
                               ->select('transaksi_kasir_detail.*, produk.nama_produk, produk.kategori, produk.harga_jual')
                               ->join('transaksi_kasir', 'transaksi_kasir.id = transaksi_kasir_detail.transaksi_kasir_id')
                               ->join('produk', 'produk.id = transaksi_kasir_detail.item_id', 'left')
                               ->where('DATE_FORMAT(transaksi_kasir.tanggal_transaksi, "%Y-%m")', $bulan)
                               ->where('transaksi_kasir_detail.jenis_item', 'product_sale')
                               ->findAll();

        $totalPenjualan = 0;
        $totalPendapatan = 0;
        $produkStats = [];

        foreach ($penjualanProduk as $item) {
            $totalPenjualan += $item['quantity'];
            $totalPendapatan += $item['subtotal'];

            // Group by product
            $produkId = $item['item_id'];
            if (!isset($produkStats[$produkId])) {
                $produkStats[$produkId] = [
                    'nama_produk' => $item['nama_produk'] ?: $item['nama_item'],
                    'kategori' => $item['kategori'] ?: 'Tidak diketahui',
                    'harga_satuan' => $item['harga_satuan'],
                    'total_quantity' => 0,
                    'total_pendapatan' => 0
                ];
            }
            $produkStats[$produkId]['total_quantity'] += $item['quantity'];
            $produkStats[$produkId]['total_pendapatan'] += $item['subtotal'];
        }

        // Sort by quantity untuk produk terlaris
        $produkTerlaris = array_values($produkStats);
        usort($produkTerlaris, function($a, $b) {
            return $b['total_quantity'] - $a['total_quantity'];
        });

        return [
            'total_penjualan' => $totalPenjualan,
            'total_pendapatan' => $totalPendapatan,
            'produk_terlaris' => array_slice($produkTerlaris, 0, 5), // Top 5
            'detail' => array_values($produkStats)
        ];
    }

    /**
     * Get laporan produk tahunan
     */
    private function getLaporanProdukTahunan($tahun)
    {
        // Get penjualan produk dari transaksi kasir detail untuk tahun tertentu
        $penjualanProduk = $this->transaksiKasirDetailModel
                               ->select('transaksi_kasir_detail.*, produk.nama_produk, produk.kategori, produk.harga_jual')
                               ->join('transaksi_kasir', 'transaksi_kasir.id = transaksi_kasir_detail.transaksi_kasir_id')
                               ->join('produk', 'produk.id = transaksi_kasir_detail.item_id', 'left')
                               ->where('YEAR(transaksi_kasir.tanggal_transaksi)', $tahun)
                               ->where('transaksi_kasir_detail.jenis_item', 'product_sale')
                               ->findAll();

        $totalPenjualan = 0;
        $totalPendapatan = 0;
        $produkStats = [];

        foreach ($penjualanProduk as $item) {
            $totalPenjualan += $item['quantity'];
            $totalPendapatan += $item['subtotal'];

            // Group by product
            $produkId = $item['item_id'];
            if (!isset($produkStats[$produkId])) {
                $produkStats[$produkId] = [
                    'nama_produk' => $item['nama_produk'] ?: $item['nama_item'],
                    'kategori' => $item['kategori'] ?: 'Tidak diketahui',
                    'harga_satuan' => $item['harga_satuan'],
                    'total_quantity' => 0,
                    'total_pendapatan' => 0
                ];
            }
            $produkStats[$produkId]['total_quantity'] += $item['quantity'];
            $produkStats[$produkId]['total_pendapatan'] += $item['subtotal'];
        }

        // Sort by quantity untuk produk terlaris
        $produkTerlaris = array_values($produkStats);
        usort($produkTerlaris, function($a, $b) {
            return $b['total_quantity'] - $a['total_quantity'];
        });

        return [
            'total_penjualan' => $totalPenjualan,
            'total_pendapatan' => $totalPendapatan,
            'produk_terlaris' => array_slice($produkTerlaris, 0, 5), // Top 5
            'detail' => array_values($produkStats)
        ];
    }
}