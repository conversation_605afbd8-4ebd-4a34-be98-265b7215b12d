<!DOCTYPE html>
<html>
<head>
    <title>Test Laporan JavaScript</title>
    <link href="<?= base_url('assets/css/bootstrap.min.css') ?>" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>Test Laporan JavaScript</h2>
        
        <div class="row">
            <div class="col-md-4">
                <label class="form-label">Periode</label>
                <select class="form-select" id="filterPeriode">
                    <option value="hari"><PERSON><PERSON></option>
                    <option value="bulan">Bulanan</option>
                    <option value="tahun"><PERSON><PERSON><PERSON></option>
                </select>
            </div>
            <div class="col-md-4" id="filterTanggalContainer">
                <label class="form-label">Tanggal</label>
                <input type="date" class="form-control" id="filterTanggal" value="2025-07-16">
            </div>
            <div class="col-md-4" id="filterBulanContainer" style="display: none;">
                <label class="form-label">Bulan</label>
                <select class="form-select" id="filterBulan">
                    <option value="2025-01">Januari 2025</option>
                    <option value="2025-02">Februari 2025</option>
                    <option value="2025-03">Maret 2025</option>
                    <option value="2025-04">April 2025</option>
                    <option value="2025-05">Mei 2025</option>
                    <option value="2025-06">Juni 2025</option>
                    <option value="2025-07" selected>Juli 2025</option>
                    <option value="2025-08">Agustus 2025</option>
                    <option value="2025-09">September 2025</option>
                    <option value="2025-10">Oktober 2025</option>
                    <option value="2025-11">November 2025</option>
                    <option value="2025-12">Desember 2025</option>
                </select>
            </div>
            <div class="col-md-4" id="filterTahunContainer" style="display: none;">
                <label class="form-label">Tahun</label>
                <select class="form-select" id="filterTahun">
                    <option value="2023">2023</option>
                    <option value="2024">2024</option>
                    <option value="2025" selected>2025</option>
                    <option value="2026">2026</option>
                    <option value="2027">2027</option>
                </select>
            </div>
        </div>
        
        <div class="row mt-3">
            <div class="col-md-12">
                <button class="btn btn-primary" id="filterButton">Test Filter</button>
                <button class="btn btn-success" onclick="testAPI()">Test API</button>
            </div>
        </div>
        
        <div class="row mt-3">
            <div class="col-md-12">
                <div id="results" class="alert alert-info">
                    Results will appear here...
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        console.log('Test page loaded');
        
        function updateDateFilter() {
            console.log('=== updateDateFilter CALLED ===');
            
            const filterPeriode = document.getElementById('filterPeriode');
            if (!filterPeriode) {
                console.error('filterPeriode element not found!');
                return;
            }
            
            const periode = filterPeriode.value;
            console.log('Current periode value:', periode);
            
            // Get elements
            const tanggalContainer = document.getElementById('filterTanggalContainer');
            const bulanContainer = document.getElementById('filterBulanContainer');
            const tahunContainer = document.getElementById('filterTahunContainer');
            
            console.log('Container elements:', {
                tanggal: !!tanggalContainer,
                bulan: !!bulanContainer,
                tahun: !!tahunContainer
            });
            
            // Hide all filter containers
            if (tanggalContainer) tanggalContainer.style.display = 'none';
            if (bulanContainer) bulanContainer.style.display = 'none';
            if (tahunContainer) tahunContainer.style.display = 'none';
            
            // Show appropriate filter
            console.log('Switching on periode:', periode);
            switch(periode) {
                case 'hari':
                    if (tanggalContainer) {
                        tanggalContainer.style.display = 'block';
                        console.log('✅ SHOWING tanggal filter');
                        document.getElementById('results').innerHTML = 'Showing: Tanggal Filter';
                    }
                    break;
                case 'bulan':
                    if (bulanContainer) {
                        bulanContainer.style.display = 'block';
                        console.log('✅ SHOWING bulan filter');
                        document.getElementById('results').innerHTML = 'Showing: Bulan Filter';
                    }
                    break;
                case 'tahun':
                    if (tahunContainer) {
                        tahunContainer.style.display = 'block';
                        console.log('✅ SHOWING tahun filter');
                        document.getElementById('results').innerHTML = 'Showing: Tahun Filter';
                    }
                    break;
            }
        }
        
        function testAPI() {
            console.log('Testing API...');
            
            fetch('<?= base_url('laporan/laporanKonsol') ?>?periode=hari&tanggal=2025-07-16', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                console.log('API Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('API Data:', data);
                document.getElementById('results').innerHTML = `
                    <h5>API Test Result:</h5>
                    <p>Success: ${data.success}</p>
                    <p>Data count: ${data.data ? data.data.length : 0}</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            })
            .catch(error => {
                console.error('API Error:', error);
                document.getElementById('results').innerHTML = `
                    <h5>API Error:</h5>
                    <p>${error.message}</p>
                `;
            });
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded');
            
            // Add event listeners
            document.getElementById('filterPeriode').addEventListener('change', updateDateFilter);
            document.getElementById('filterButton').addEventListener('click', function() {
                console.log('Filter button clicked');
                updateDateFilter();
            });
            
            // Initial call
            updateDateFilter();
        });
    </script>
</body>
</html>
