<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'PlaySphere | PS Rental System' ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Iceland&display=swap" rel="stylesheet">
    <style>
        /* Root Variables */
        :root {
            --primary-dark: rgb(30, 5, 177);
            --primary-light: rgb(22, 148, 233);
            --bg-light: rgb(222, 218, 236);
            --bg-dark: rgb(143, 150, 238);
            --text-dark: #2c3e50;
            --text-muted: #7f8c8d;
            --sidebar-width: 250px;
            --sidebar-width-collapsed: 70px;
        }

        /* Base Styles */
        body { 
            background: rgb(148, 186, 241); 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow-x: hidden;
        }
        
        /* Sidebar Base */
        .sidebar {
            width: var(--sidebar-width);
            height: 100vh;
            position: fixed;
            left: 0;
            top: 0;
            padding: 20px 0;
            color: white;
            transition: all 0.3s ease;
            z-index: 1000;
            background: linear-gradient(180deg, var(--primary-dark) 0%, var(--primary-light) 100%);
            box-shadow: 4px 0 10px rgba(0,0,0,0.1);
        }

        /* Brand Styling */
        .brand {
            font-size: 1.2em;
            font-weight: 700;
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            background: rgba(0,0,0,0.1);
        }

        .brand img {
            width: 210px;
            max-width: 100%;
            height: auto;
        }

        /* Navigation Links */
        .nav-link {
            color: rgba(255,255,255,0.8) !important;
            padding: 12px 20px !important;
            transition: all 0.3s;
            border-radius: 0 30px 30px 0;
            margin-right: 20px;
        }

        .nav-link:hover {
            background: rgba(255,255,255,0.1);
            color: white !important;
            transform: translateX(5px);
        }

        .nav-link.active {
            background: rgba(255,255,255,0.2);
            color: white !important;
            box-shadow: inset 4px 0 0 white;
        }

        .nav-link i { 
            width: 25px;
            text-align: center;
            margin-right: 10px;
        }

        /* Main Content Area */
        .main-content {
            margin-left: var(--sidebar-width);
            padding: 20px;
            min-height: 100vh;
            transition: margin-left 0.3s ease;
        }

        /* Content Header */
        .content-header {
            background: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        /* Submenu Styling */
        .submenu {
            padding-left: 25px;
            display: none;
            background: rgba(0,0,0,0.1);
            margin: 5px 20px 5px 0;
            border-radius: 0 15px 15px 0;
        }

        .menu-open .submenu { 
            display: block; 
        }

        .submenu .nav-link {
            font-size: 0.9em;
            padding: 8px 20px !important;
        }

        /* User Profile Section */
        .user-profile {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 15px;
            background: rgba(0,0,0,0.1);
        }

        /* Content Area */
        .content {
            background: var(--bg-light);
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            padding: 20px;
        }

        /* Card Styling */
        .card {
            background: linear-gradient(145deg, var(--bg-light), var(--bg-dark));
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        /* Sidebar Footer */
        .sidebar-footer {
            position: absolute;
            bottom: 0;
            width: 100%;
            padding: 15px;
            background: rgba(0,0,0,0.1);
            font-size: 0.8rem;
            border-top: 1px solid rgba(255,255,255,0.1);
        }

        /* Sidebar Toggle Button */
        .sidebar-toggle {
            display: none;
            position: fixed;
            top: 1rem;
            left: 1rem;
            z-index: 1040;
            background: var(--primary-dark);
            color: white;
            border: none;
            padding: 0.5rem;
            border-radius: 0.25rem;
        }

        /* Responsive Breakpoints */
        @media (max-width: 1200px) {
            .row {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            }
        }

        @media (max-width: 992px) {
            .sidebar {
                width: var(--sidebar-width-collapsed);
                padding: 10px 0;
            }
            
            .sidebar .brand span,
            .sidebar .nav-link span,
            .sidebar-footer,
            .user-profile div {
                display: none;
            }
            
            .main-content {
                margin-left: var(--sidebar-width-collapsed);
            }
            
            .nav-link i {
                font-size: 1.2rem;
                margin: 0;
            }
            
            .submenu {
                position: absolute;
                left: var(--sidebar-width-collapsed);
                top: auto;
                width: 200px;
                display: none;
                box-shadow: 2px 2px 5px rgba(0,0,0,0.2);
            }
            
            /* Remove hover behavior */

            .nav-item {
                position: relative;
            }

            .submenu .nav-link {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .brand img {
                width: 40px;
            }
        }

        @media (max-width: 768px) {
            .submenu {
                position: static;
                width: 100%;
                box-shadow: none;
                margin: 0;
                padding-left: 2rem;
            }
            .sidebar {
                transform: translateX(-100%);
                width: var(--sidebar-width);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .content-header {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
            
            .sidebar-toggle {
                display: block;
            }

            .brand img {
                width: 210px;
            }

            .sidebar .brand span,
            .sidebar .nav-link span,
            .sidebar-footer,
            .user-profile div {
                display: block;
            }
        }

        @media (max-width: 576px) {
            .content {
                padding: 10px;
            }

            .card-body {
                padding: 0.75rem;
            }
            
            .timer-display {
                font-size: 2rem;
            }
            
            .session-controls button {
                padding: 0.25rem 0.5rem;
            }
            
            .modal-dialog {
                margin: 0.5rem;
            }
        }

        /* Touch Device Optimizations */
        @media (hover: none) {
            .nav-link:hover {
                transform: none;
            }
            
            .card:hover {
                transform: none;
            }
        }
    </style>
    <?= $this->renderSection('styles') ?>
</head>
<body>
    <!-- Sidebar Toggle Button -->
    <button class="sidebar-toggle" id="sidebarToggle">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Sidebar -->
    <div class="sidebar">
        <div class="brand">
            <img src="<?= base_url('assets/img/logo-stik.png') ?>" alt="PlayStation" />
            <div>PlaySphere</div>
        </div>
        
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?= current_url() == base_url('/') ? 'active' : '' ?>" href="/">
                    <i class="fas fa-tachometer-alt"></i> <span>Dashboard</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#" data-bs-toggle="collapse" data-bs-target="#playstation-menu">
                    <i class="fas fa-gamepad"></i> <span>PlayStation</span>
                    <i class="fas fa-chevron-down float-end mt-1"></i>
                </a>
                <ul class="submenu nav flex-column" id="playstation-menu">
                    <li class="nav-item">
                        <a class="nav-link <?= current_url() == base_url('pstype') ? 'active' : '' ?>" href="/pstype">
                            <i class="fas fa-cog"></i> <span>Jenis PlayStation</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= current_url() == base_url('channel') ? 'active' : '' ?>" href="/channel">
                            <i class="fas fa-desktop"></i> <span>Channel</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= current_url() == base_url('package') ? 'active' : '' ?>" href="/package">
                            <i class="fas fa-box"></i> <span>Paket</span>
                        </a>
                    </li>
                </ul>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/report">
                    <i class="fas fa-chart-bar"></i> <span>Laporan</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/member">
                    <i class="fas fa-users"></i> <span>Member</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="/operator">
                    <i class="fas fa-user-shield"></i> <span>Operator</span>
                </a>
            </li>
        </ul>

        <!-- Sidebar Footer -->
        <div class="sidebar-footer">
            <div class="text-center">
                <p class="mb-1">V2.0 &copy; <?= date('Y') ?> 
                    <a href="https://langitinovasi.id" target="_blank" class="text-white text-decoration-none">
                        Langit Inovasi
                    </a>
                </p>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="content-header d-flex justify-content-between align-items-center">
            <h4 class="mb-0"><?= $title ?? 'Dashboard' ?></h4>
            <div>
                <span class="text-muted me-3">Operator: Admin</span>
                <a href="/logout" class="btn btn-outline-danger btn-sm">
                    <i class="fas fa-sign-out-alt"></i> Keluar
                </a>
            </div>
        </div>
        <div class="content">
            <?= $this->renderSection('content') ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Sidebar menu toggle
                    // Close any open submenu first
            document.querySelectorAll('.nav-link[data-bs-toggle="collapse"]').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // Close all other open submenus
                    document.querySelectorAll('.nav-item.menu-open').forEach(item => {
                        if (item !== this.closest('.nav-item')) {
                            item.classList.remove('menu-open');
                        }
                    });
                    
                    // Toggle current submenu
                    this.closest('.nav-item').classList.toggle('menu-open');
                });
            });
            
            // Close submenu when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.nav-item')) {
                    document.querySelectorAll('.nav-item.menu-open').forEach(item => {
                        item.classList.remove('menu-open');
                    });
                }
            });

        // Mobile sidebar toggle
        document.getElementById('sidebarToggle').addEventListener('click', function(e) {
            e.stopPropagation();
            document.querySelector('.sidebar').classList.toggle('show');
        });

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(e) {
            const sidebar = document.querySelector('.sidebar');
            const sidebarToggle = document.getElementById('sidebarToggle');
            
            if (window.innerWidth <= 768) {
                if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                    sidebar.classList.remove('show');
                }
            }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            const sidebar = document.querySelector('.sidebar');
            if (window.innerWidth > 768) {
                sidebar.classList.remove('show');
            }
        });
    </script>
    <?= $this->renderSection('scripts') ?>
</body>
</html>