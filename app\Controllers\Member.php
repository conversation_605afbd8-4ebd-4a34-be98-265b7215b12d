<?php
/**
 * Member Controller
 * Controller untuk manajemen data member dan transaksi top-up
 * Menangani CRUD member, top-up saldo, dan riwayat transaksi
 *
 * <AUTHOR> Team
 * @version 3.0
 */

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\MemberModel;
use App\Models\TopupModel;

class Member extends BaseController
{
    /**
     * Halaman utama member
     * Menampilkan daftar semua member dengan saldo dan riwayat
     *
     * @return string View member index dengan data member
     */
    public function index()
    {
        // Inisialisasi model
        $model = new MemberModel();
        $topup = new TopupModel();

        // Ambil data member dengan riwayat top-up
        $members = $model->getWithRiwayat();

        // Hitung total top-up per member untuk statistik
        foreach ($members as &$m) {
            $total = 0;
            foreach ($m['riwayat'] as $r) {
                $total += $r['jumlah'];
            }
            $m['total_topup'] = $total;
        }

        return view('member/index', ['member' => $members]);
    }

    /**
     * Simpan member baru
     * Membuat member baru dengan ID otomatis dan saldo awal 0
     *
     * @return \CodeIgniter\HTTP\ResponseInterface JSON response
     */
    public function simpan()
    {
        $model = new MemberModel();

        // Generate ID member otomatis dengan format PS + timestamp
        $id_member = 'PS' . date('ymdHis');

        // Simpan data member baru
        $model->save([
            'id_member' => $id_member,
            'nama'      => $this->request->getPost('nama'),
            'no_wa'     => $this->request->getPost('no_wa'),
            'saldo'     => 0 // Saldo awal 0
        ]);

        return $this->response->setJSON([
            'success' => true,
            'message' => 'Member berhasil ditambahkan'
        ]);
    }

    /**
     * Update data member
     * Mengupdate nama dan nomor WhatsApp member
     *
     * @param int $id ID member yang akan diupdate
     * @return \CodeIgniter\HTTP\ResponseInterface JSON response
     */
    public function update($id)
    {
        $model = new MemberModel();

        // Update data member
        $model->update($id, [
            'nama'  => $this->request->getPost('nama'),
            'no_wa' => $this->request->getPost('no_wa')
        ]);

        return $this->response->setJSON([
            'success' => true,
            'message' => 'Data berhasil diperbarui'
        ]);
    }

    public function hapus($id)
    {
        $model = new MemberModel();
        $model->delete($id);

        return redirect()->to(base_url('member'));
    }

    public function topup($id)
    {
        $jumlah = (int) $this->request->getPost('jumlah');

        if ($jumlah < 1000) {
 return $this->response
    ->setStatusCode(400)
    ->setJSON(['message' => 'Minimal top up Rp 1.000 yaa, jangan pelit 🫣']);
        }

        if ($jumlah % 1000 !== 0) {
return $this->response
    ->setStatusCode(400)
    ->setJSON(['message' => 'Top up harus kelipatan Rp 1.000, bukan recehan gan 😅']);
        }

        $memberModel = new MemberModel();
        $topupModel = new TopupModel();

        // Simpan ke tabel topup
        $topupModel->save([
            'id_member' => $id,
            'jumlah'    => $jumlah
        ]);

        // Tambahkan saldo ke member
        $member = $memberModel->find($id);
        $newSaldo = $member['saldo'] + $jumlah;
        $memberModel->update($id, ['saldo' => $newSaldo]);

        return $this->response->setJSON([
            'success' => true,
            'message' => 'Top up berhasil',
            'saldo_baru' => $newSaldo
        ]);
    }

    public function riwayat($id)
    {
        $topupModel = new TopupModel();
        $riwayat = $topupModel->where('id_member', $id)->orderBy('created_at', 'DESC')->findAll();
        return $this->response->setJSON($riwayat);
    }

    public function cetakRiwayat($id)
    {
        $topupModel = new TopupModel();
        $memberModel = new MemberModel();
        $member = $memberModel->find($id);

        if (!$member) {
            return $this->response
                ->setStatusCode(404)
                ->setBody("Member tidak ditemukan.");
        }

        $riwayat = $topupModel->where('id_member', $id)->orderBy('created_at', 'DESC')->findAll();

        $filename = 'riwayat_topup_' . $member['id_member'] . '.txt';
        $output = "Riwayat TopUp untuk Member: {$member['nama']}\n\n";

        $total = 0;
        foreach ($riwayat as $item) {
            $tanggal = date('d-m-Y H:i', strtotime($item['created_at']));
            $nominal = number_format($item['jumlah'], 0, ',', '.');
            $total += $item['jumlah'];
            $output .= "{$tanggal} - Rp {$nominal}\n";
        }

        $output .= "\nTotal TopUp: Rp " . number_format($total, 0, ',', '.');

        return $this->response
            ->setHeader('Content-Type', 'text/plain')
            ->setHeader('Content-Disposition', 'attachment; filename="' . $filename . '"')
            ->setBody($output);
    }

    public function cetakRiwayatPenggunaan($id)
    {
        $memberModel = new MemberModel();
        $member = $memberModel->find($id);

        if (!$member) {
            return $this->response
                ->setStatusCode(404)
                ->setBody("Member tidak ditemukan.");
        }

        $riwayatPenggunaan = $memberModel->getRiwayatPenggunaan($id);

        $filename = 'riwayat_penggunaan_' . $member['id_member'] . '.txt';
        $output = "Riwayat Penggunaan Saldo untuk Member: {$member['nama']}\n";
        $output .= "ID Member: {$member['id_member']}\n";
        $output .= "No. WhatsApp: {$member['no_wa']}\n\n";

        if (empty($riwayatPenggunaan)) {
            $output .= "Belum ada riwayat penggunaan saldo.\n";
        } else {
            $output .= "=== RIWAYAT PENGGUNAAN SALDO ===\n\n";
            
            $total = 0;
            foreach ($riwayatPenggunaan as $item) {
                $tanggalMulai = date('d-m-Y H:i', strtotime($item['waktu_mulai']));
                $tanggalBerhenti = $item['waktu_berhenti'] ? date('d-m-Y H:i', strtotime($item['waktu_berhenti'])) : 'Masih Berjalan';
                
                // Tentukan biaya berdasarkan jenis (paket atau non-paket)
                if ($item['nama_paket']) {
                    // Jika paket, tampilkan harga paket
                    $biaya = $item['harga_paket'];
                } else {
                    // Jika non-paket, hitung biaya berdasarkan durasi dan tarif per menit
                    if ($item['waktu_berhenti']) {
                        $mulai = strtotime($item['waktu_mulai']);
                        $berhenti = strtotime($item['waktu_berhenti']);
                        $durasiDetik = $berhenti - $mulai;
                        $durasiMenit = ceil($durasiDetik / 60); // Pembulatan ke atas per menit
                        $hargaPerJam = $item['harga_member'] ?? 0;
                        $hargaPerMenit = round($hargaPerJam / 60);
                        $biaya = $durasiMenit * $hargaPerMenit;
                    } else {
                        // Jika masih berjalan, gunakan harga_total sementara
                        $biaya = $item['harga_total'];
                    }
                }
                $nominal = number_format($biaya, 0, ',', '.');
                
                $station = $item['nama_station'] . ' (' . $item['nama_konsol'] . ')';
                $paket = $item['nama_paket'] ? $item['nama_paket'] : 'Non-Paket';
                $status = ucfirst($item['status']);
                
                // Hitung durasi
                $durasi = '-';
                if ($item['waktu_berhenti']) {
                    $mulai = strtotime($item['waktu_mulai']);
                    $berhenti = strtotime($item['waktu_berhenti']);
                    $durasiDetik = $berhenti - $mulai;
                    $jam = floor($durasiDetik / 3600);
                    $menit = floor(($durasiDetik % 3600) / 60);
                    $durasi = sprintf('%02d:%02d', $jam, $menit);
                }
                
                $total += $biaya;
                
                $output .= "Tanggal: {$tanggalMulai}\n";
                $output .= "Station: {$station}\n";
                $output .= "Paket: {$paket}\n";
                $output .= "Durasi: {$durasi}\n";
                $output .= "Biaya: Rp {$nominal}\n";
                $output .= "Status: {$status}\n";
                $output .= "Selesai: {$tanggalBerhenti}\n";
                $output .= "----------------------------------------\n";
            }
            
            $output .= "\nTotal Penggunaan Saldo: Rp " . number_format($total, 0, ',', '.');
        }

        return $this->response
            ->setHeader('Content-Type', 'text/plain')
            ->setHeader('Content-Disposition', 'attachment; filename="' . $filename . '"')
            ->setBody($output);
    }

     public function cari()
    {
        $q = $this->request->getGet('q');
        $model = new MemberModel();

        $result = $model
            ->like('nama', $q)
            ->orLike('id_member', $q)
            ->findAll(10);

        return $this->response->setJSON($result);
    }
}
