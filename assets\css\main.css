/**
 * Main Layout CSS
 * Styling untuk layout utama aplikasi PlaySphere
 * Termasuk sidebar, topbar, dan responsive design
 * 
 * <AUTHOR>
 * @version 3.0
 */

/* ===== GLOBAL STYLES ===== */
/* Body styling */
body {
  background-color: #f4f6f9;
  font-family: 'Segoe UI', sans-serif;
}

/* ===== SIDEBAR STYLES ===== */
.sidebar {
  width: 220px;
  height: 100vh;
  background-color: #37474F;
  color: white;
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  z-index: 1000;
  transition: transform 0.3s ease;
}

.sidebar.collapsed {
  transform: translateX(-100%);
}

.sidebar .brand {
  font-size: 1.3rem;
  font-weight: bold;
  text-align: center;
  padding: 0;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  height: 140px;
  width: 100%;
}

.sidebar .brand img {
  width: 210px;
  height: 130px;
  margin: 0;
  padding: 0;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  object-fit: contain;
  transition: filter 0.3s ease, transform 0.3s ease;
  cursor: pointer;
}

.sidebar .brand img:hover {
  filter: brightness(0) invert(1);
  transform: scale(1.05);
}

.sidebar .nav-link {
  color: #cfd8dc;
  padding: 12px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 12px;
}

.sidebar .nav-link i {
  font-size: 1.1rem;
  width: 20px;
  text-align: center;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
  background-color: #4FC3F7;
  color: black;
  border-radius: 8px;
  transform: translateX(5px);
  box-shadow: 0 2px 8px rgba(79, 195, 247, 0.3);
}

.sidebar .btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.sidebar .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
}

/* Mobile: Hide text, show only icons */
@media (max-width: 768px) {
  .sidebar {
    width: 80px;
  }

  /* Logo mengecil di mobile */
  .sidebar .brand {
    height: 80px;
  }

  .sidebar .brand img {
    width: 80px;
    height: 80px;
    padding: 10px;
  }

  .sidebar .nav-text {
    display: none;
  }

  .sidebar .nav-link {
    justify-content: center;
    padding: 15px 10px;
  }

  .sidebar .nav-link i {
    font-size: 1.3rem;
  }

  .sidebar .btn .nav-text {
    display: none;
  }

  .sidebar .btn {
    padding: 10px;
  }

  .sidebar .btn i {
    font-size: 1.2rem;
  }

  .sidebar-footer {
    font-size: 0.7rem;
    padding: 8px 5px;
  }
}

.sidebar-footer {
  font-size: 0.8rem;
  padding: 10px 16px;
  text-align: center;
  color: #b0bec5;
}

.sidebar-footer a {
  color: #cfd8dc;
  text-decoration: none;
}

/* ===== MAIN CONTENT STYLES ===== */
.content {
  margin-left: 220px;
  min-height: 100vh;
  padding-bottom: 80px;
  transition: margin-left 0.3s ease;
}

.content.full-width {
  margin-left: 0;
}

.topbar {
  background-color: #ffffff;
  padding: 15px 30px;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  position: relative;
}

.topbar-logo {
  width: 28px;
  height: 28px;
  object-fit: contain;
  border-radius: 4px;
  vertical-align: middle;
  margin-top: -3px;
}

.topbar-time {
  font-size: 0.85rem;
  text-align: right;
  color: #607d8b;
}

.topbar .badge {
  font-size: 0.75rem;
}

/* Mobile Menu Button */
.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #37474F;
  cursor: pointer;
  padding: 5px;
}

/* Overlay untuk mobile */
.sidebar-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

.sidebar-overlay.show {
  display: block;
}

/* ===== MOBILE RESPONSIVE ===== */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
  }

  .sidebar.show {
    transform: translateX(0);
  }

  .content {
    margin-left: 0;
  }

  .mobile-menu-btn {
    display: block;
  }

  .topbar {
    padding: 10px 15px;
  }

  .topbar .fw-bold {
    font-size: 1.1rem !important;
  }

  .topbar #tanggal-jam {
    font-size: 0.9rem !important;
    text-align: center;
  }

  .topbar .text-end {
    font-size: 0.85rem;
  }

  .container-fluid {
    padding: 15px !important;
  }

  /* Flexbox untuk topbar mobile */
  .topbar {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }

  .topbar > div:first-child {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .topbar .text-center {
    order: 2;
  }

  .topbar .text-end {
    order: 3;
    text-align: center !important;
  }
}

@media (max-width: 480px) {
  .sidebar {
    width: 70px;
  }

  /* Logo lebih kecil lagi di ponsel */
  .sidebar .brand {
    height: 70px;
  }

  .sidebar .brand img {
    width: 70px;
    height: 70px;
    padding: 8px;
  }

  .topbar {
    padding: 8px 12px;
  }

  .topbar #tanggal-jam {
    font-size: 0.8rem !important;
  }

  .container-fluid {
    padding: 10px !important;
  }
}

/* Tablet */
@media (min-width: 769px) and (max-width: 1024px) {
  .topbar {
    padding: 12px 20px;
  }

  .container-fluid {
    padding: 20px !important;
  }
}

/* Mobile Footer - hanya tampil di mobile */
.mobile-footer {
  background-color: #37474F;
  color: #cfd8dc;
  text-align: center;
  padding: 20px 15px;
  margin-top: 30px;
  font-size: 0.85rem;
  display: none; /* Default hidden */
}

.mobile-footer a {
  color: #4FC3F7;
  text-decoration: none;
}

.mobile-footer a:hover {
  color: white;
  text-decoration: underline;
}

/* Tampilkan footer di mobile */
@media screen and (max-width: 768px) {
  .mobile-footer {
    display: block !important;
  }
}

/* Tampilkan footer di ponsel */
@media screen and (max-width: 480px) {
  .mobile-footer {
    display: block !important;
    padding: 15px 10px;
    font-size: 0.8rem;
  }
}
