<?php namespace App\Controllers;

class PsType extends BaseController
{
    protected $psTypeModel;

    public function __construct()
    {
        $this->psTypeModel = new \App\Models\PsTypeModel();
    }

    public function index()
    {
        $data = [
            'title' => 'Pengaturan PlayStation',
            'ps_types' => $this->psTypeModel->findAll()
        ];
        return view('pstype/index', $data);
    }

    public function add()
    {
        $data = [
            'name' => $this->request->getPost('name'),
            'price_per_hour' => $this->request->getPost('price_per_hour'),
            'description' => $this->request->getPost('description')
        ];

        $this->psTypeModel->insert($data);
        return redirect()->to('/pstype')->with('message', 'PlayStation berhasil ditambahkan');
    }

    public function edit($id)
    {
        $data = [
            'name' => $this->request->getPost('name'),
            'price_per_hour' => $this->request->getPost('price_per_hour'),
            'description' => $this->request->getPost('description')
        ];

        $this->psTypeModel->update($id, $data);
        return redirect()->to('/pstype')->with('message', 'PlayStation berhasil diupdate');
    }

    public function delete($id)
    {
        $this->psTypeModel->delete($id);
        return redirect()->to('/pstype')->with('message', 'PlayStation berhasil dihapus');
    }
}