-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jan 29, 2025 at 11:50 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `billing_ps`
--

-- --------------------------------------------------------

--
-- Table structure for table `billing_sessions`
--

CREATE TABLE `billing_sessions` (
  `id` int(11) NOT NULL,
  `channel_id` int(11) DEFAULT NULL,
  `member_id` int(11) DEFAULT NULL,
  `package_id` int(11) DEFAULT NULL,
  `player_name` varchar(100) DEFAULT NULL,
  `start_time` datetime DEFAULT NULL,
  `end_time` datetime DEFAULT NULL,
  `pause_time` datetime DEFAULT NULL,
  `total_pause_duration` int(11) DEFAULT 0,
  `status` enum('active','paused','completed') DEFAULT 'active',
  `total_price` decimal(10,2) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `channels`
--

CREATE TABLE `channels` (
  `id` int(11) NOT NULL,
  `number` varchar(50) DEFAULT NULL,
  `ps_type_id` int(11) DEFAULT NULL,
  `ip_address` varchar(15) DEFAULT NULL,
  `status` enum('available','in_use','maintenance','connected','disconnected') DEFAULT 'disconnected',
  `connection_status` enum('connected','disconnected') DEFAULT 'disconnected',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `session_data` text DEFAULT NULL,
  `last_ping` timestamp NULL DEFAULT NULL,
  `last_seen` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `channels`
--

INSERT INTO `channels` (`id`, `number`, `ps_type_id`, `ip_address`, `status`, `connection_status`, `created_at`, `updated_at`, `session_data`, `last_ping`, `last_seen`) VALUES
(12, 'Channel 1', 5, '************', 'available', 'disconnected', '2025-01-26 04:50:05', '2025-01-29 08:53:43', NULL, NULL, '2025-01-29 01:53:23'),
(14, 'Channel 2', 6, '************', 'available', 'disconnected', '2025-01-26 05:05:48', '2025-01-29 05:09:16', NULL, NULL, '2025-01-28 22:08:54'),
(15, 'Channel 3', 5, '************', 'in_use', 'connected', '2025-01-27 02:49:22', '2025-01-29 22:50:28', '{\"playerName\":\"zulfan\",\"packageName\":\"Non Paket\",\"timer\":\"00:04:41\",\"price\":\"Rp 15.000\",\"isPaused\":false,\"sessionType\":\"non-package\",\"startTime\":\"2025-01-29T22:45:46.878Z\",\"endTime\":null}', NULL, '2025-01-29 15:50:28'),
(16, 'Channel 4', 6, '************', 'available', 'disconnected', '2025-01-27 02:49:43', '2025-01-29 01:50:57', NULL, NULL, NULL),
(17, 'Channel 5', 5, '************', 'available', 'disconnected', '2025-01-27 02:50:10', '2025-01-28 08:47:50', NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `members`
--

CREATE TABLE `members` (
  `id` int(11) NOT NULL,
  `member_code` varchar(20) DEFAULT NULL,
  `name` varchar(100) NOT NULL,
  `phone` varchar(15) DEFAULT NULL,
  `balance` decimal(10,2) DEFAULT 0.00,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `packages`
--

CREATE TABLE `packages` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `duration` int(11) DEFAULT NULL COMMENT 'Durasi dalam menit',
  `price` decimal(10,2) NOT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `packages`
--

INSERT INTO `packages` (`id`, `name`, `duration`, `price`, `description`, `is_active`, `created_at`, `updated_at`) VALUES
(2, 'Paket 2 Jam', 120, 25000.00, 'Durasi 2 jam', 1, '2025-01-27 00:08:02', '2025-01-26 17:40:59'),
(3, 'Paket Malam', 360, 50000.00, 'Paket 6 jam malam', 1, '2025-01-27 00:08:02', '2025-01-26 17:40:58'),
(4, 'Paket khsus', 390, 40000.00, 'enam jam setengah', 1, '2025-01-26 17:20:26', '2025-01-26 18:32:30'),
(5, 'paket oye', 600, 80000.00, 'sepuluh jam', 1, '2025-01-26 17:21:05', '2025-01-26 17:21:05'),
(6, 'paket mblenek', 1440, 5000.00, 'Sedino suwengi ben kapok\r\n', 1, '2025-01-26 17:21:33', '2025-01-26 17:40:38'),
(7, 'paket diluk', 1, 100000.00, 'ben kapok', 1, '2025-01-27 06:21:43', '2025-01-27 06:22:30'),
(8, 'sak jam', 60, 500000.00, 'sak jam 500 ewu', 1, '2025-01-29 04:56:02', '2025-01-29 04:56:02'),
(9, 'rong menit', 2, 60000.00, 'rong menit tok', 1, '2025-01-29 06:01:15', '2025-01-29 06:01:15');

-- --------------------------------------------------------

--
-- Table structure for table `ps_types`
--

CREATE TABLE `ps_types` (
  `id` int(11) NOT NULL,
  `name` varchar(50) NOT NULL,
  `price_per_hour` decimal(10,2) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ps_types`
--

INSERT INTO `ps_types` (`id`, `name`, `price_per_hour`, `description`, `created_at`, `updated_at`) VALUES
(5, 'PS-4', 15000.00, 'PlayStation 4', '2025-01-25 23:50:39', '2025-01-26 16:56:35'),
(6, 'PS-5', 25000.00, 'PlayStation 5', '2025-01-26 16:56:28', '2025-01-26 16:56:28');

-- --------------------------------------------------------

--
-- Table structure for table `topup_history`
--

CREATE TABLE `topup_history` (
  `id` int(11) NOT NULL,
  `member_id` int(11) DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `transaction_date` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `billing_sessions`
--
ALTER TABLE `billing_sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `channel_id` (`channel_id`),
  ADD KEY `member_id` (`member_id`),
  ADD KEY `package_id` (`package_id`);

--
-- Indexes for table `channels`
--
ALTER TABLE `channels`
  ADD PRIMARY KEY (`id`),
  ADD KEY `channels_ibfk_1` (`ps_type_id`);

--
-- Indexes for table `members`
--
ALTER TABLE `members`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `member_code` (`member_code`);

--
-- Indexes for table `packages`
--
ALTER TABLE `packages`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `ps_types`
--
ALTER TABLE `ps_types`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `topup_history`
--
ALTER TABLE `topup_history`
  ADD PRIMARY KEY (`id`),
  ADD KEY `member_id` (`member_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `billing_sessions`
--
ALTER TABLE `billing_sessions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `channels`
--
ALTER TABLE `channels`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=18;

--
-- AUTO_INCREMENT for table `members`
--
ALTER TABLE `members`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `packages`
--
ALTER TABLE `packages`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `ps_types`
--
ALTER TABLE `ps_types`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `topup_history`
--
ALTER TABLE `topup_history`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `billing_sessions`
--
ALTER TABLE `billing_sessions`
  ADD CONSTRAINT `billing_sessions_ibfk_1` FOREIGN KEY (`channel_id`) REFERENCES `channels` (`id`),
  ADD CONSTRAINT `billing_sessions_ibfk_2` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`),
  ADD CONSTRAINT `billing_sessions_ibfk_3` FOREIGN KEY (`package_id`) REFERENCES `packages` (`id`);

--
-- Constraints for table `channels`
--
ALTER TABLE `channels`
  ADD CONSTRAINT `channels_ibfk_1` FOREIGN KEY (`ps_type_id`) REFERENCES `ps_types` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `topup_history`
--
ALTER TABLE `topup_history`
  ADD CONSTRAINT `topup_history_ibfk_1` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
